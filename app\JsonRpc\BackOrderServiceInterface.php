<?php
declare(strict_types=1);

namespace App\JsonRpc;

interface BackOrderServiceInterface
{
    /**
     * 列表
     * @param int $export
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return array
     */
    public function list(int $export, int $page, int $pageLimit, array $search);

    /**
     * 查单个 - 订单
     * @param array $where
     * @param array|string[] $filed
     */
    public function getOrder(array $where, array $filed = ['*']);

    /**
     * 获取订单详情
     * @param array $where
     * @param array $filed
     * @return mixed
     */
    public function getOrderDetail(array $where, array $filed = ['*']);

    /**
     * 获取详情列表
     * @param int $export
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return array
     */
    public function detailList(int $export, int $page, int $pageLimit, array $search);

    /**
     * 获取装箱列表
     * @param int $export
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return array
     */
    public function packList(int $export, int $page, int $pageLimit, array $search);

    /**
     * 校验单号是否存在
     * @param $serialNos
     */
    public function checkSerialNos(array $serialNos);

    /**
     * 退返单 与 装箱发货 对比
     * 是否差异
     * @param string $serial_no
     * @return array
     */
    public function checkIsDiffDelivery(string $serial_no);

    /**
     * 校验店内码是否属于此次退返任务单
     * @param $uniqueCodes //店内码 数组
     */
    public function checkIfUniqueCodeInBackOrderBySerialNo($serialNo, array $uniqueCodes);

    /**
     * 校验店内码是否属于此次退返任务单
     * @param $barcodes //条码 数组
     */
    public function checkIfBarcodeInBackOrderBySerialNo($serialNo, array $barcodes);

    /**
     * 更新单
     * @param array $where
     * @param array $data
     * @return bool
     */
    public function updateOrder(array $where, array $data);

    /**
     * 箱号发货
     * @param array $boxDelivery
     * @return mixed
     */
    public function boxDelivery(array $boxDelivery);
    
    /**
     * 箱号发货作废
     * @param array $boxIds
     * @return mixed
     */
    public function boxVoidDelivery(array $boxIds);

    /**
     * 发货记录列表
     * @param int $export
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return mixed
     */
    public function boxDeliveryList(int $export, int $page, int $pageLimit, array $search);

    /**
     * 标记质检
     * @param array $boxNos
     * @return mixed
     */
    public function addQualitys(array $boxNos);

    /**
     * 箱号称重
     * @param array $data
     * @return mixed
     */
    public function pickWeight(array $data);

    /**
     * 作废退返单子单
     * @param int $id
     * @return mixed
     */
    public function backVoid(int $id,array $params = []);

    /**
     * 退返单导出明细
     * @param array $data
     * @return mixed
     */
    public function exportDetailList(array $data);
}
