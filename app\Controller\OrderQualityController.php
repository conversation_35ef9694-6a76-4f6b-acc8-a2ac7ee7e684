<?php
declare(strict_types=1);

namespace App\Controller;

use App\Constants\ErrorCode;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\JsonRpc\OrderQualityServiceInterface;
use App\JsonRpc\SkuServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use App\Library\Facades\AdminService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;

/**
 * Class OrderQualityController
 * @Controller()
 * @package App\Controller
 */
class OrderQualityController extends AbstractController
{
    /**
     * @Inject()
     * @var WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject()
     * @var OrderQualityServiceInterface
     */
    private $OrderQualityService;

    /**
     * @RequestMapping(path="/orderQuality/lists", methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function lists ()
    {
        $data['title'] = '质检记录';
        $warehouse = [];
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        if (!empty( $wIds )) {
            $warehouse['ids'] = $wIds;
        }
        $warehouse_info = collect( $this->WarehouseService->getWarehouses($warehouse) )->pluck( 'name', 'id' )->all();
        if ($this->isAjax()) {
            $where = $this->request->all();
            //处理时间
            if (!empty( $where['date'] )) {
                $date = explode( " - ", $where['date'] );
                $where['start_date'] = $date[0];
                $where['end_date'] = $date[1];
            }
            //处理店内码
            if (!empty( $where['unique_code'] )) {
                $where['unique_code'] = explode( "\n", $where['unique_code'] );
            }
            $where['export'] = $where['export'] ?? 0;
            $where['w_ids'] = $wIds;
            $page_size = $where['limit'] ?? $this->pageLimit();
            $info = $this->OrderQualityService->getOrderQualityLists( $where, (int)$where['page'], (int)$page_size );
            $result = [];
            if (!empty( $info['data'] )) {
                $status = PublicCode::order_quality_status;
                foreach ($info['data'] as $key => $item) {
                    $result['data'][$key] = $item;
                    $result['data'][$key]['status_name'] = $status[$item['status']] ?? '-';
                    $result['data'][$key]['w_name'] = $warehouse_info[$item['w_id']] ?? '';
                }
            } else {
                $result['data'] = [];
            }
            $result['total'] = $info['total'] ?? count( $result['data'] );
            return $this->returnApi( ResponseCode::SUCCESS, '操作成功', $result['data'], ['count' => $result['total'], 'limit' => $page_size, 'export' => $where['export']] );
        }
        $data['warehouse_info'] = $warehouse_info;
        $data['status'] = PublicCode::order_quality_status;
        return $this->show( 'orderQuality/lists', $data );
    }

    /**
     * @RequestMapping(path="/orderQuality/quality", methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function quality ()
    {
        $data['title'] = '订单质检';
        return $this->show( 'orderQuality/quality', $data );
    }

    /**
     * @RequestMapping(path="/orderQuality/doQuality", methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function doQuality ()
    {
        $params = $this->request->post();
        $userInfo = $this->getUserInfo();
        try {
            $params['admin_id'] = $userInfo['uid'];
            $params['admin_name'] = $userInfo['nickname'];
            $result = $this->OrderQualityService->doQuality( $params );
        } catch (\Exception $exception) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, $exception->getMessage() );
        }
        return $this->returnApi( ResponseCode::SUCCESS, '质检成功', $result );
    }

    /**
     * @RequestMapping(path="/orderQuality/checkQuality", methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function checkQuality ()
    {
        $params = $this->request->post();
        if (empty( $params['unique_code'] )) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, '请输入店内码' );
        }
        $order_info = $this->OrderQualityService->checkOrderQualityInfo( $params );
        if (empty( $order_info['order'] )) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, '无订单信息' );
        }

        if ($order_info['order']['status'] == -1) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, '订单已取消' );
        }

        if($order_info['order']['current_generation'] >= 7)
        {
            return $this->returnApi( ErrorCode::SERVER_ERROR, '该店内码已质检完毕,请勿重复操作' );
        }

        if ($order_info['order']['current_generation'] < 5) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, '店内码未出库' );
        }

        $result = [];
        $result['goods_url'] = $order_info['order']['goods_image'] ?? '';
        $result['unique_code'] = $order_info['order']['unique_code'] ?? '';
        $result['branch_serial_no'] = $order_info['order']['branch_serial_no'] ?? '';
        $result['order_branch_id'] = $order_info['order']['id'] ?? '';
        $result['brand_name'] = $order_info['order']['brand_name'] ?? '';
        $result['w_id'] = $order_info['order']['w_id'] ?? '';
        $result['sku_id'] = $order_info['order']['sku_id'] ?? '';
        if (!empty( $order_info['order']['category_name'] )) {
            $category = explode( "->", $order_info['order']['category_name'] );
            $result['category_name'] = end( $category );
        } else {
            $result['category_name'] = '';
        }
        $result['order_branch_remark'] = $order_info['order']['remark'] ?? '无';
        $result['order_branch_source'] = PublicCode::order_source[$order_info['order']['source']] ?? '';
        $result['order_branch_status'] = ($order_info['order']['status'] == -1) ? '订单取消' : '无';
        $result['spu_no'] = $order_info['order']['spu_no'] ?? '';
        $result['spec_name'] = $order_info['order']['spec_name'] ?? '';
        if (!empty( $order_info['quality'] )) {
            $result['quality_id'] = $order_info['quality']['id'] ?? 0;
            $result['already_msg'] = '已质检' . PublicCode::order_quality_status[$order_info['quality']['status']] . ',可修改质检结果';
        } else {
            $result['quality_id'] = 0;
            $result['already_msg'] = '';
        }
        return $this->returnApi( ResponseCode::SUCCESS, '请求成功', $result );
    }
}