<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * erp盘点服务消费者
 */
Interface  StServiceInterface
{
    /**
     * 添加盘点任务
     * @param array $data
     * @return mixed
     */
    public function addTask(array $data);

    /**
     * 盘点任务货架分布
     * @param $page
     * @param $limit
     * @param $stId
     */
    public function getShelfDistributionList($page,$limit,$stId);

    /**
     * 任务部署
     * @param $stId
     */
    public function deploy($data);

    /**
     * 任务部署
     * @param $stId
     */
    public function editDeploy($data);

    /**
     * 获取部署列表
     * @param $stId
     */
    public function deployList($stId,$page,$limit);

    /**
     * 获取任务详情
     * @param $stId
     */
    public function detail($stId);

    /**
     * 获取任务清单
     * @param $stId
     */
    public function billList($stId,$page,$limit);

    /**
     * 盘点任务单据统计摘要
     * @param $stId
     */
    public function statistics($stId);

    /**
     * 工作部署人员变更
     * @param $deployId
     * @param $adminType 管理人员的类型 1=初盘，2=复盘
     * @param $newAdminId 管理人员id
     * @param $newAdminName 管理人员姓名
     */
    public function changeAdmin($deployId,$adminType,$adminId,$adminName);

    /**
     * 接单
     * @param $deployId
     * @param $adminType 管理人员的类型 1=初盘，2=复盘
     * @param $newAdminId 管理人员id
     */
    public function acceptTask($deployId,$adminType,$adminId);

    /**
     * 获取一个盘点任务
     * @param $stId
     */
    public function getTaskOne($stId);

    /**
     * 保存盘点数据
     * @param $stId
     * @param $stWay 1=店内码，2=条码
     * @param $data
     */
    public function saveSbData($wId,$stId,$stType,$adminInfo,$data);

    /**
     * 保存盘点数据 - 新 - 重构 - 20230317
     * @param $params
     * @param $epcCodes
     * @return array
     */
    public function saveSbDataNew($params,$epcCodes);

    /**
     * 获取某一任务下的实盘结果
     * @param int $st_id
     * @param int $page
     * @param int $limit
     * @param array $where
     * @param array $field
     */
    public function getStResultListByStId(int $st_id,int $page = 1, int $limit = 10, array $where = [], array $field = []);

    /**
     * 盘点任务单列表
     * @param int $page
     * @param int $limit
     * @param array $where
     * @param array $field
     */
    public function getStocktakingTaskList($authWIds,int $page = 1, int $limit = 10, array $where = [], array $field = []);

    /**
     * 盘点任务全部货架分布
     * @param $page
     * @param $limit
     * @param $stId
     */
    public function getShelfDistributionAll($stId);

    /**
     * 更改状态
     * @param $st_id '盘点状态: 0=草稿,1=即将开始，2=正在预盘，3=正在复盘，4=复盘完成，5=已完成，6=已作废'
     * @param $data '更新数据'
     */
    public function updateStatus($st_id,$data);

    /**
     * 根据盘点任务id获取单据下所有货架排
     * @param $stId
     */
    public function getShelfLinesByStId($stId);

    /**
     * 根据盘点数据获取旗下应该包含的货架排
     * @param array $stInfo
     * @return mixed
     */
    public function getShelfLinesByStInfo(array $stInfo);

    /**
     * 创建盘点任务并提交部署任务数据
     */
    public function addTaskAndDeploy($stInfoData,$deployData);

    /**
     * 盘点任务单详情
     * @param int $id
     */
    public function getStocktakingTaskOne(int $id);

    /**
     * 获取盘点综合结果摘要：盘盈，盘亏
     * @param $stId
     * @return \Hyperf\Database\Model\Builder[]|\Hyperf\Database\Model\Collection
     */
    public function getStResultSummaryById($stId);

    /**
     * 根据info_data字段内的id获取对应name
     * @param $ids
     * @param $type
     * @return false|string
     */
    public function convertInfoData($ids,$type);

    /**
     * 添加盘点任务 - 20220823改版-最简版
     * @param array $data
     * @param $adminInfo
     * @return mixed
     */
    public function addTaskSimple(array $data,$adminInfo);

    /**
     * 根据盘点id获取盘点单货架
     * @param $stId
     */
    public function getStShelfLines($stId);

    /**
     * 获取盘点单已部署过的货架
     * @param $stId
     */
    public function getStDeployedShelfLines($stId);

    /**
     * 获取盘点单未部署过的货架
     * @param $stId
     */
    public function getStUnDeployedShelfLines($stId);

    /**
     * 保存部署任务（新增或更新）
     * @param $data
     */
    public function saveDeploy(array $data);

    /**
     * 删除部署任务
     * @param $stId
     * @param $deployId
     */
    public function delDeploy($stId,$deployId);

    /**
     * 是否需要生成库存调整单时，判断盈亏
     * @param $stId
     */
    public function getStResultSummaryByIdForStockAdjust($stId);

    /**
     * 获取盘点任务下的货架排
     * @param $stId
     */
    public function getShelfLinesBtStId($stId);

    /**
     * 盘点串位理货
     * @param $stId
     */
    public function transferWrongPlaceData($stId);

    /**
     * 货架采集列表
     * @param $stId
     * @param $where
     * @param $page
     * @param $limit
     */
    public function getShelfGatherList($stId,$where,$page,$limit);

    /**
     * 获取有实盘记录的采集货架排
     * @param $stId
     * @param $where
     */
    public function getGatherShelfLines($stId,$where);

    /**
     * 重置采集货架下的采集数据
     * @param $stId
     * @param $shelfLines
     */
    public function resetGatherShelfLines($stId,$shelfLines);

    /**
     * 货架动盘
     * @param $stId
     * @param $shelfLines
     */
    public function shelfDynamicSt($stId,$shelfLines);

    /**
     * 刷新盈亏成本
     * @param $stId
     * @param $adminInfo
     */
    public function refreshPlCost($stId,$adminInfo);

    /**
     * 根据父级类目获取子类目id
     * @param $cateIds
     * @return array
     */
    public function getChildCategory($cateIds);

    /**
     * 获取盘点单内串位货品，组装理货数据返回
     * @param $stId
     * @return array[]
     */
    public function getTallyDataFromStBill($stId);

    /**
     * 串位理货（走异步）
     * @param $tType
     * @param $wId
     * @param $redisKey
     * @param array $adminInfo
     * @return void
     */
    public function wrongPlaceDataTally($tType,$wId, $redisKey,array $adminInfo);

    /**
     * 导出实盘结果（店内码实时状态追加 - 20231027 - yj）
     * @param int $st_id
     * @param int $page
     * @param int $limit 传-1，表示取所有数据
     * @param array $where
     * @param array $field
     * @return array
     */
    public function getStResultListByStIdExport(int $st_id,int $page = 1, int $limit = 10, array $where = [], array $field = []);

    /**
     * 更新StBill表的sku_id
     *
     * @param int $stId 盘点任务ID
     */
    public function updateStBillSkuId(int $stId);

    /**
     * 批量查询指定店内码是否在生产区
     * @param array $uniqueCodes 店内码数组
     * @return array 在生产区的店内码数组
     */
    public function checkProductArea($uniqueCodes);

    /**
     * 获取盘点数据
     * @param $stId
     * @param array $params 查询参数
     * @return array
     */
    public function getStResultData($stId, array $params);

}