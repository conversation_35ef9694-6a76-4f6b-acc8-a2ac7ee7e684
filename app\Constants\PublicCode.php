<?php
declare(strict_types=1);

namespace App\Constants;

use App\Library\DecisionTree\Model\TreeRoot;
use Hyperf\Constants\AbstractConstants;

/**
 * Class PublicCode
 * @package App\Constants
 */
class PublicCode extends AbstractConstants
{
    // 仓库状态 1有效 0无效
    const warehouse_status_valid = 1;
    const warehouse_status_invalid = 0;

    // 货架类型
    const shelf_type = [
        1 => '常规货架',
        2 => '高位货架',
        3 => '陈列货架',
        4 => '暂存货架',
    ];
    // 货架号状态
    const shelf_status_valid = 1;
    const shelf_status_invalid = 0;
    const shelf_status = [
        self::shelf_status_valid => '有效',
        self::shelf_status_invalid => '作废',
    ];

    // 存储区状态
    const block_status_valid = 1;
    const block_status_invalid = 0;
    const block_status = [
        self::block_status_valid => '有效',
        self::block_status_invalid => '作废',
    ];

    // 生产区生产类型
    const produce_area_source_type_sale = 25;// 销售(订单)
    const produce_area_source_type_service = 5;// 售后单
    const produce_area_source_type = [
        21 => '差异调整出库',
        22 => '退返出库',
        23 => '调拨出库',
        24 => '盘亏出库',
        25 => '销售出库',
        26 => '配送丢失出库',
        27 => '配送报损出库',
        28 => '拍照出库',
        29 => '其他出库',
        30 => '库存调整单出库',
        31 => '鞋服报损出库',

        5 => '退货回库'
    ];

    // 生产区货品状态
    const produce_area_status_no_finish = 0;
    const produce_area_status_finish = 1;
    const produce_area_status = [
        self::produce_area_status_no_finish => '未完成',
        self::produce_area_status_finish => '已完成'
    ];
    // 生产区货品标记方式
    const produce_area_sign_type_unique_code = 1;
    const produce_area_sign_type_barcode = 2;
    const produce_area_sign_type = [
        1 => '店内码',
        2 => '条形码'
    ];

    //拣货单状态
    const pick_order_status_valid = -1;//-1作废
    const pick_order_status_stay = 1;//1待拣货
    const pick_order_status_in = 2;//2拣货中
    const pick_order_status_finish = 3;//3已完成
    const pick_order_status = [
        self::pick_order_status_valid => '作废',
        self::pick_order_status_stay => '待拣货',
        self::pick_order_status_in => '拣货中',
        self::pick_order_status_finish => '已完成',
    ];

    //单据类型
    const order_type = [
        21 => '差异单出库',
        22 => '退返单',
        23 => '调拨单',
        24 => '盘点-亏',
        25 => '销售(订单)',
        26 => '配送丢失',
        27 => '配送报损',
    ];

    //拣货方式
    const pick_type_barcode = 1;//条形码
    const pick_type_unique_code = 2;//店内码
    //拣货方式
    const pick_type = [
        self::pick_type_barcode => '条形码',
        self::pick_type_unique_code => '店内码',
    ];

    const pick_mode_0 = 0;//普通拣货
    const pick_mode_1 = 1;//报缺重拣
    const pick_mode = [
        self::pick_mode_0 => '普通拣货',
        self::pick_mode_1 => '报缺重拣',
    ];

    // 回库方式
    const reback_type_unique_code = 1;// 店内码
    const reback_type_barcode = 2;// 条码
    const reback_type = [
        self::reback_type_unique_code => '店内码',
        self::reback_type_barcode => '条码'
    ];
    const reback_source_type_sale_order = 25;// 来源单据类型：销售订单

    //分拣任务状态
    const pick_task_status_valid = -1;
    const pick_task_status_normal = 1;
    const pick_task_status_doing = 2;
    const pick_task_status_success = 3;
    const pick_task_status = [
        self::pick_task_status_normal => '待分拣',
        self::pick_task_status_doing => '分拣中',
        self::pick_task_status_success => '已完成',
        self::pick_task_status_valid => '作废',
    ];

    //分拣任务类型
    const pick_task_type_rfid = 1;
    const pick_task_type_unique_code = 2;
    const pick_task_type = [
        self::pick_task_type_rfid => 'RFID分拣',
        self::pick_task_type_unique_code => '店内码分拣',
    ];

    //明细分拣状态
    const pick_task_detail_status_stay = 0;
    const pick_task_detail_status_finish = 1;
    const pick_task_detail_status = [
        self::pick_task_detail_status_stay => '未分拣',
        self::pick_task_detail_status_finish => '已分拣',
    ];

    // 残次码类型
    const imperfect_code_type_unique = 1;
    const imperfect_code_type_barcode = 2;
    const imperfect_code_type = [
        1 => '店内码',
        2 => '条码'
    ];
    // 残次-是否使用
    const imperfect_used = [
        0 => '未使用',
        1 => '已使用'
    ];
    // 残次-类型
    const imperfect_type_arrival = 1;
    const imperfect_type_allot = 6;
    const imperfect_type = [
        1 => '到货原残',
        2 => '漏检原残',
        3 => '退货后残',
        4 => '库损后残',
        5 => '库内借用后残',
        6 => '调拨在途后残'
    ];
    const imperfect_type_barcode = [
        2 => '漏检原残',
        3 => '退货后残',
        4 => '库损后残',
        5 => '库内借用后残'
    ];
    // 残次-状态
    const imperfect_status_cancel = -1;
    const imperfect_status_wait_verify = 0;
    const imperfect_status_verify_pass = 1;
    const imperfect_status_verify_refuse = 2;
    const imperfect_status = [
        self::imperfect_status_cancel => '作废',
        self::imperfect_status_wait_verify => '待审核',
        self::imperfect_status_verify_pass => '通过',
        self::imperfect_status_verify_refuse => '驳回'
    ];
    // 残次-取消原因
    const imperfect_cancel_reason = [
        1 => '维修恢复',
        2 => '标记错误'
    ];

    //分拣墙
    const order_pick_wall = [
        1 => 'A',
        2 => 'B',
        3 => 'C',
        4 => 'D',
        5 => 'E',
        6 => 'F',
        7 => 'G',
        8 => 'H',
    ];

    /**
     * wms订单质检状态
     */
    const order_quality_status = [
        1 => '通过',
        2 => '不通过',
    ];

    /**
     * 订单来源
     */
    const order_source = [
        1 => '门店',
        2 => '小程序',
        3 => '安卓',
        4 => 'IOS',
        5 => '代理',
        6 => '抖音'
    ];

    /**
     * 按商品件数分类
     */
    const order_pick_order_type = [
        1 => '单品订单',
        2 => '组合订单',
        3 => '大宗订单',
        4 => '二次发货订单',
    ];

    /**
     * 拣货顺序
     */
    const pick_sort = [
        1 => '按下单时间',
        2 => '按货架位',
    ];

    //预约到货单状态
    const arrival_status = [
        '-1' => '作废',
        '0' => '待到货',
        '1' => '已到货'
    ];

    //按商品件数分类
    const order_pick_type = [
        1 => '单品订单',
        2 => '组合订单',
        3 => '大宗订单'
    ];

    //订单-发货类型
    const order_delivery_type = [
        1 => '部分发货',
        2 => '货齐发货'
    ];

    //订单操作环节
    const order_current_generation = [
        '-1' => '停止发货',
        '1' => '等待发货',
        '2' => '分配快递',
        '3' => '分配货品',
        '4' => '导出拣货单',
        '5' => '出库',
        '6' => '质检',
        '7' => '制单',
        '8' => '快递交接'
    ];

    //订单发货状态
    const order_delivery_status = [
        '1' => '未发货',
        '2' => '部分发货',
        '3' => '已发货',
        '4' => '取消发货'
    ];

    //订单异常
    const order_exception_status = [
        '1' => '无异常',
        '2' => '拦截发货',
        '3' => '订单取消',
        '4' => '生产异常'
    ];

    //订单货品发货状态
    const order_detail_delivery_status = [
        '1' => '未发货',
        '2' => '已发货',
        '3' => '取消发货'
    ];

    //货品异常
    const order_detail_exception_status = [
        '1' => '无异常',
        '2' => '取消发货',
        '3' => '货品匹配失败',
        '4' => '出库失败',
        '5' => '质检不通过'
    ];

    // 退返任务单-状态
    const back_order_status_wait_take = 4;
    const back_order_status_wait_out = 5;
    //const back_order_status_wait_delivery = 6;
    const back_order_status_already_delivery = 7;
    const back_order_status_already_sign = 8;
    const back_order_status_already_void = -1;
    const back_order_status = [
        self::back_order_status_wait_take => '待接单',
        self::back_order_status_wait_out => '待出库',
        //self::back_order_status_wait_delivery => '待发货',
        self::back_order_status_already_delivery => '已发货',
        self::back_order_status_already_sign => '已签收',
        self::back_order_status_already_void => '已作废'
    ];
    // 退返任务单-运费承担方
    const back_order_freight_bear = [
        1 => '收货方',
        2 => '发货方'
    ];
    // 退返任务单-发货方式
    const back_order_delivery_type = [
        1 => '物流发货',
        2 => '自提'
    ];
    // 退返任务单-退返类型
    const back_order_type_purchase = 1;
    const back_order_type_diff = 2;
    const back_order_type = [
        self::back_order_type_purchase => '采购退返',
        self::back_order_type_diff => '差异退返',
    ];
    // 合作模式
    const co_model = [
        1 => '买断',
        2 => '代销'
    ];

    // 装箱状态
    const goods_packing_status = [
        0 => '作废',
        1 => '正常',
        2 => '已发货'
    ];

    // 售后单-类型
    const order_service_type_0 = 0;
    const order_service_type = [
        0 => '未知',
        1 => '仅退款',
        2 => '退款退货',
        3 => '换货'
    ];
    // 售后单-处理方式 1自动 0不自动处理
    const order_service_auto_yes = 1;
    const order_service_auto_no = 0;

    // 售后单-申请用户类型
    const order_service_apply_type_u = 1;// 用户
    const order_service_apply_type_m = 2;// 系统

    // 售后单状态- 0待审核，1驳回，2关闭，3用户取消，4待商品退回，5商品已签收，6待退款，7待处理，8处理完毕
    const order_service_status_wait_return = 4;
    const order_service_status_already_sign = 5;

    // 售后单-退回方式
    const order_service_sign_return_type_u = 1;
    const order_service_sign_return_type_l = 2;
    const order_service_sign_return_type = [
        1 => '用户寄回',
        2 => '快递退回'
    ];
    // 售后单-退回后，存放地址
    const order_service_quality_address_type_w = 1;
    const order_service_quality_address_type_t = 2;
    const order_service_quality_address_type = [
        1 => '仓库',
        2 => '暂存区'
    ];
    // 售后单-质检结果
    const order_service_quality_res_yes = 1;
    const order_service_quality_res_no = 0;
    const order_service_quality_res = [
        0 => '不通过',
        1 => '通过'
    ];
    // 售后单-质检后存放地址
    const order_service_quality_address_status_w = 1;
    const order_service_quality_address_status_t = 2;
    const order_service_quality_address_status_u = 3;
    const order_service_quality_address_status = [
        1 => '回仓',
        2 => '暂存区',
        3 => '退回给用户'
    ];
    // 售后单-退回给用户物流-状态
    const order_service_logistics_status_valid = 1;// 有效
    const order_service_logistics_status_invalid = 0; //无效

    // 出库任务单状态
    const out_store_status_cancel = -1;// 作废
    const out_store_status_wait = 0;// 待出库
    const out_store_status_ing = 1;// 出库中
    const out_store_status_finish = 2;// 已完成
    // 出库任务单状态 - 所有生效状态
    const out_store_status_valid = [
        self::out_store_status_wait,
        self::out_store_status_ing,
        self::out_store_status_finish
    ];

    /**
     * 盘点状态: 1=待盘点，2=盘点中，3=待审核，4=已完成，5=已作废，6=驳回
     */
    const ST_STATUS = [
        1 => '待盘点',
        2 => '盘点中',
        3 => '待审核',
        4 => '已完成',
        5 => '已作废',
        6 => '驳回',
    ];

    const ST_STATUS_MAP = [
        'not_stocktaking' => 1,
        'stocktaking'=> 2,
        'to_audit'=> 3,
        'finished'=> 4,
        'invalid'=> 5,
        'reject'=> 6,
    ];
    /*const ST_STATUS = [
        'to_stocktaking' => 1,
        'stocktaking' => 2,
        'to_audit' => 3,
        'finished' => 4,
        'invalid' => 5,
        'reject' => 6,
    ];*/

    //快递规则是否启用
    const express_rules_status = [
        1 => '启用',
        0 => '关闭',
    ];

    /**
     * 库存查询 商品码类型: 1 = spu，2=sku，3=货号，4=商品条码，5=店内码，6=货架号，7=批次号
     */
    const CODE_TYPE = [
        1 => 'spu',
        2 => 'sku',
        3 => '货号',
        4 => '商品条码',
        5 => '店内码',
        6 => '货架号',
        7 => '批次号',
    ];

    /**
     * 店内码在库库存
     */
    const UNIQUE_CODE_STATUS = [
        1 => '在库',
        2 => '出库',
        3 => '锁定',
        4 => '采购差异',
    ];

    /**
     * sku管理方式
     */
    const SKU_MANAGE_TYPE = [
        1 => '店内码',
        2 => '条码',
    ];

    const PICK_ORDER_TYPE = [
        //21 => '差异单出库拣货',
        22 => '退返拣货',
        23 => '调拨拣货',
        //24 => '盘点拣货',
        25 => '销售拣货',
        //26 => '配送丢失拣货',
        27 => '配送报损拣货',
        31 => '报损拣货'
    ];


    // 售后单-售后原因
    const service_reason_refuse = 1;
    const service_reason = [
        1 => '无原因拒收',
        2 => '快递不到',
        3 => '联系不到客户',

        4 => '未收到货',

        5 => '尺码不合适',
        6 => '样式颜色不喜欢',
        7 => '实物与照片不一致',
        8 => '质量问题',
        9 => '仓库发错货',
        10 => '怀疑不是正品',
        12 => '尺码问题',
        13 => '发错货',
        14 => '未穿着-质量问题',
        15 => '已穿着-质量问题',
        16 => '商品不喜欢',
        17 => '不想要了'
    ];

    // 装箱店内码异常类型
    const GOODS_PACKING_U_E_TYPE_MAP = [
        'pass' => 0, // 校验通过
        'not_exists' => 1, // 不存在
        'not_exists_in_batch' => 2,// 不属于于此批次
        'box_already_exist' => 3,// 箱号已存在
        'not_out_store' => 4,// 货品未出库
        'locked' => 5,// 店内码已锁定
        'packed' => 6,// 货品已装箱
        //'not_match_with_gather_way' => 3,// 商品码类型与采集方式不一致（比如批量上传的只能是店内码，而不能是条码或epc码）
    ];

    // 退返状态 0待部门审核 1部门驳回 2待财务审核 3财务驳回 4待接单 5待出库 6待发货 7已发货 8已签收 -1已作废
    const BACK_ORDER_STATUS = [
        0 => '待部门审核',
        1 => '部门驳回',
        2 => '待财务审核',
        3 => '财务驳回',
        4 => '待接单',
        5 => '待出库',
        6 => '待发货',
        7 => '已发货',
        8 => '已签收',
        -1 => '已作废',
    ];

    // 店内码日志操作类型
    const UNIQUE_CODE_LOG_TYPE = [
        "1"=>"生成店内码",
        "2"=>"新品入库",
        "3"=>"销售锁定",
        "4"=>"销售解锁",
        "5"=>"销售出库",
        "6"=>"销售发货",
        "7"=>"退货入库",
        "8"=>"调拨锁定",
        "9"=>"调拨解锁",
        "10"=>"调拨出库",
        "11"=>"调拨回库",
        "12"=>"调拨入库上架",
        "13"=>"拍照锁定",
        "14"=>"拍照解锁",
        "15"=>"拍照出库",
        "16"=>"拍照回库",
        "17"=>"退返锁定",
        "18"=>"退返解锁",
        "19"=>"退返出库",
        "20"=>"退返发货",
        "21"=>"测量锁定",
        "22"=>"测量解锁",
        "23"=>"测量出库",
        "24"=>"测量回库",
        "25"=>"变为原残",
        "26"=>"变为后残",
        "27"=>"取消标残",
        "28"=>"更改批次或进货折扣",
        "29"=>"修改SKU或者尺码",
        "30"=>"转移货架下架",
        "31"=>"转移货架上架",
        "32"=>"打包装箱",
        "33"=>"补全库存信息",
        "34"=>"借用领用锁定",
        "35"=>"借用领用取消锁定",
        "36"=>"用户要求锁定",
        "37"=>"用户要求取消锁定",
        "38"=>"异常商品锁定",
        "39"=>"异常商品解锁",
        "40"=>"确认丢失",
        "41"=>"取消退返锁定",
        "42"=>"添加待标残",
        "43"=>"暂存区上架",
        "44"=>"暂存区下架",
        "45"=>"打印吊牌",
        "46"=>"绑定RFID",
        "47"=>"差异调整出库",
        "48"=>"盘亏出库",
        "49"=>"配送丢失出库",
        "50"=>"配送报损出库",
        "51"=>"退货进入生产区",
        "81"=>"盘点",
        "82"=>"报损出库",
        "83"=>"丢失出库",
        "84"=>"调整入库",
        "85"=>"调整出库",
        "86"=>"订单串码销售",
        "87"=>"PDA-替码解锁",
        "88"=>"PDA-替码锁定",
        "89"=>"库存调整-出库锁定",
        "90"=>"库存调整-出库解锁",
        "91"=>"手工锁定",
        "92"=>"手工解锁",
        "93"=>"鞋服报损-出库锁定",
        "94"=>"鞋服报损-出库解锁",
        "95"=>"商品拆分修改SKU、SPU"
    ];

    const UNIQUE_CODE_LOG_TYPE_MAP = [
        "generate_unique_code"=>"1",//生成店内码
        "new_goods_in_store"=>"2",//新品入库
        "sale_lock"=>"3",//销售锁定
        "sale_unlock"=>"4",//销售解锁
        "sale_out_store"=>"5",//销售出库
        "sale_delivery"=>"6",//销售发货
        "back_order_in_store"=>"7",//退货入库
        "allot_lock"=>"8",//调拨锁定
        "allot_unlock"=>"9",//调拨解锁
        "allot_out_store"=>"10",//调拨出库
        "allot_back_store"=>"11",//调拨回库
        "allot_in_store_on_shelf"=>"12",//调拨入库上架
        "photo_lock"=>"13",//拍照锁定
        "photo_unlock"=>"14",//拍照解锁
        "photo_out_store"=>"15",//拍照出库
        "photo_back_store"=>"16",//拍照回库
        "back_lock"=>"17",//退返锁定
        "back_unlock"=>"18",//退返解锁
        "back_out_store"=>"19",//退返出库
        "back_delivery"=>"20",//退返发货
        "measure_lock"=>"21",//测量锁定
        "measure_unlock"=>"22",//测量解锁
        "measure_out_store"=>"23",//测量出库
        "measure_back_store"=>"24",//测量回库
        "change_to_original_imperfect"=>"25",//变为原残
        "change_to_after_imperfect"=>"26",//变为后残
        "cancel_mark_imperfect"=>"27",//取消标残
        "modify_batch_or_discount"=>"28",//更改批次或进货折扣
        "modify_sku_or_size"=>"29",//修改SKU或者尺码
        "transfer_shelf_down"=>"30",//转移货架下架
        "transfer_shelf_up"=>"31",//转移货架上架
        "goods_packing"=>"32",//打包装箱
        "perfect_stock_info"=>"33",//补全库存信息
        "borrow_lock"=>"34",//借用领用锁定
        "borrow_unlock"=>"35",//借用领用取消锁定
        "user_demand_lock"=>"36",//用户要求锁定
        "user_demand_unlock"=>"37",//用户要求取消锁定
        "exception_goods_lock"=>"38",//异常商品锁定
        "exception_goods_unlock"=>"39",//异常商品解锁
        "confirm_loss"=>"40",//确认丢失
        "cancel_back_lock"=>"41",//取消退返锁定
        "add_to_be_mark_imperfect"=>"42",//添加待标残
        "staging_area_on_shelf"=>"43",//暂存区上架
        "staging_area_down_shelf"=>"44",//暂存区下架
        "print_tags"=>"45",//打印吊牌
        "bind_rfid"=>"46",//绑定RFID
        "diff_out_store" => "47",//差异调整出库
        "deficit_out_store" => "48",//盘亏出库
        "lose_out_store" => "49",//配送丢失出库
        "loss_out_store" => "50",//配送报损出库
        "back_order_in_produce_area" => "51",//退货进入生产区
        "st" => "81", // 盘点
        "report_break_out_store" => "82", // 报损出库
        "report_loss_out_store" => "83", // 丢失出库
        "adjust_in_store" => "84", // 调整入库
        "adjust_out_store" => "85", // 调整出库
        "order_code_sale" => "86", // 订单串码销售
        "pda_unlock_code" => "87", // PDA-替码解锁
        "pda_lock_code" => "88", // PDA-替码锁定
        "adjust_out_lock" => "89", // 库存调整-出库锁定
        "adjust_out_unlock" => "90", // 库存调整-出库解锁
        "hand_lock" => "91", // 手工锁定
        "hand_unlock" => "92", // 手工解锁
        "imperfect_loss_out_lock" => "93", // 鞋服报损-出库锁定
        "imperfect_loss_out_unlock" => "94", // 鞋服报损-出库解锁
        "spu_cross" => "95"// 商品拆分修改SKU、SPU
    ];

    // 盘点范围值id
    const ST_VALUE_ID = [
        'shelf_line' => 1,
        'brand' => 2,
        'category' => 3,
        'barcode' => 4,
        'unique_code' => 5,
    ];

    // new盘点
    // 盘点类型：1=店内码，2=条形码，3=RFID
    const ST_TYPE = [
        1 => '店内码',
        2 => '条形码',
        3 => 'RFID',
    ];

    // st_range_key_id 盘点范围（st_range_key表对应id）：1=全盘，2=抽盘，3=流盘
    const ST_RANGE_KEY = [
        1 => '全盘',
        2 => '抽盘',
        3 => '流盘',
    ];

    // 盘点范围value
    const ST_RANGE_VALUE = [
        1 => '货架',
        2 => '品牌',
        3 => '类目',
        4 => '条码',
        5 => '店内码',
    ];

    // 盘点状态: 0=草稿,1=即将开始，2=正在预盘，3=正在复盘，4=复盘完成，5=已完成，6=已作废,7=正在创建盘点单，8=创建盘点单失败'
    const NEW_ST_STATUS = [
        0 => '草稿',
        1 => '即将开始',
        2 => '正在预盘',
        3 => '正在复盘',
        4 => '复盘完成',
        5 => '已完成',
        6 => '已作废',
        7 => '正在创建盘点单',
        8 => '创建盘点单失败',
    ];

    // 幂等性校验令牌key
    const IDEMPOTENCE_TOKEN_KEY = "idempotence_token_key";

    /**
     * 理货类型：1=店内码理货，2=条形码理货，3=RFID理货
     */
    const TALLY_TYPE = [
        'unique_code' => 1,
        'barcode' => 2,
        'RFID' => 3,
    ];

    /**
     * 理货类型名称：1=店内码理货，2=RFID理货，3=条形码理货
     */
    const TALLY_TYPE_NAME = [
        1 => '店内码理货',
        2 => '条形码理货',
        3 => 'RFID理货',
    ];

    /**
     * 状态： 1=进行中  2=已完成
     */
    const TALLY_STATUS = [
        'ongoing' => 1,
        'finished' => 2,
    ];

    /**
     * 状态名称： 1=进行中  2=已完成
     */
    const TALLY_STATUS_NAME = [
        1 => '进行中',
        2 => '已完成',
    ];

    /**
     * 装箱商品码类型： 1=店内码  2=条码
     */
    const PK_GOODS_CODE_TYPE_NAME = [
        1 => '店内码',
        2 => '条码',
    ];

    /**
     * 理货结果类型：1=正常，2=异常
     */
    const TALLY_RESULT_TYPE = [
        'normal' => 1,
        'exception' => 2
    ];

    /**
     * 理货结果类型：1=正常，2=异常
     */
    const TALLY_RESULT_TYPE_NAME = [
        1 => '正常',
        2 => '异常',
    ];
    //订单详情操作类型
    const ORDER_OPERATION_TYPE = [
        'redistribution' => '导单-重新分配货品',
        'reserved_goods' => '导单-保留匹配货品',
        'cancel_delivery' => '取消发货'
    ];
    const GOODS_CODE_TYPE_NAME = [
        1 => '店内码',
        2 => '条码',
        3 => 'EPC码',
    ];

    // 装箱任务类型 任务类型：1=调拨任务，2=退返任务
    const PK_TASK_TYPE_NAME = [
        1 => '调拨',
        2 => '退返',
    ];

    //装箱质检
    const goods_packing_quality = [
        0 => '否',
        1 => '是'
    ];

    /**
     * 商品码类型：1=店内码，2=条形码，3=epc码
     */
    const GOODS_CODE_TYPE = [
        'unique_code' => 1,
        'barcode' => 2,
        'epc_code' => 3,
    ];

    const ST_TYPE_MAP = [
        "unique_code" => 1,
        "barcode" => 2,
        "rfid" => 3,
    ];

    const ST_RANGE_KEY_MAP = [
        'st_all'=>1, // 全盘
        'st_part'=>2, // 抽盘
        'st_flow'=>3, // 流盘
    ];

    const ST_RANGE_VALUE_MAP = [
        "shelf_code" => 1,
        "brand" => 2,
        "category" => 3,
        "barcode" => 4,
        "unique_code" => 5,
    ];

    // 盘点单表店内码状态：状态(1=在库，2=出库，3=锁定，4=入库未生成库存（存在采购差异货品）)
    const UNIQUE_CODE_STATUS_NAME = [
        0 => '',
        1 => '在库',
        2 => '出库',
        3 => '锁定',
        4 => '入库未生成库存（存在采购差异货品）',
    ];


    const logistics_send_type = [
        1 => '物流',
        2 => '自提',
        3 => '快递'
    ];

    const logistics_company_type = [
        1 => '快递',
        2 => '物流',
        3 => '菜鸟快递公司'
    ];

    // 仓库绩效统计数据类型
    const ACHIEVE_ALLOT_PICK = 1;
    const ACHIEVE_BACK_PICK = 2;
    const ACHIEVE_ALLOT_PACK = 3;
    const ACHIEVE_BACK_PACK = 4;
    const ACHIEVE_ALLOT_SEND = 5;
    const ACHIEVE_BACK_SEND = 6;
    const ACHIEVE_UP_SHELF = 7;
    const ACHIEVE_RFID_BIND = 8;
    const ACHIEVE_TAG_PRINT = 9;
    const ACHIEVE_SALE_PICK = 10;
    const ACHIEVE_TAG_PRINT_AGAIN = 11;
    const ACHIEVE_TALLY_ONE = 1201;// 绩效表理货的类型为12，1201代表搜索方式1
    const ACHIEVE_TALLY_TWO = 1202;// 绩效表理货的类型为12，1202代表搜索方式2
    const ACHIEVE_BIND_RFID = 13;
    const ACHIEVE_BOX_CHECK = 14;
    const ACHIEVE_BOX_QUALITY = 15;
    const ACHIEVE_LOGISTICS = 16;
    const ACHIEVE_BOX_IN_STORE = 17;
    const ACHIEVE_TAG_AILI_PRINT = 19;
//    const ACHIEVE_SALE_QUALITY = 4;
//    const ACHIEVE_SALE_LOGISTICS = 5;
//    const ACHIEVE_SALE_DELIVERY = 8;
//    const ACHIEVE_SALE_PICK_WALL = 9;
//    const ACHIEVE_TALLY = 13;
//    const ACHIEVE_STOCK_CHECK_INI = 14;
//    const ACHIEVE_STOCK_CHECK_REPLAY = 15;

    const achievement_type = [
        self::ACHIEVE_ALLOT_PICK => '调拨拣货',
        self::ACHIEVE_BACK_PICK => '退返拣货',
        self::ACHIEVE_ALLOT_PACK => '调拨装箱',
        self::ACHIEVE_BACK_PACK => '退返装箱',
        self::ACHIEVE_ALLOT_SEND => '调拨发货',
        self::ACHIEVE_BACK_SEND => '退返发货',
        self::ACHIEVE_UP_SHELF => '扫描上架',
        self::ACHIEVE_RFID_BIND => 'RFID磁扣生产',
        self::ACHIEVE_TAG_PRINT => '吊牌打印',
        self::ACHIEVE_SALE_PICK => '销售拣货',
        self::ACHIEVE_TAG_PRINT_AGAIN => '补打吊牌',
        self::ACHIEVE_TALLY_ONE => '理货（不去重）',
        self::ACHIEVE_TALLY_TWO => '理货（单日去重）',
        self::ACHIEVE_BIND_RFID => '磁扣绑定',
        self::ACHIEVE_BOX_CHECK => '箱号清点',
        self::ACHIEVE_BOX_QUALITY => '箱号质检',
        self::ACHIEVE_LOGISTICS => '到货登记',
        self::ACHIEVE_BOX_IN_STORE => '磁板入库',
        self::ACHIEVE_TAG_AILI_PRINT => '艾利吊牌打印',
    ];

    //需要埋点的单据类型 22退返单，23调拨-出，25销售(订单)
    const achievement_pick_map = [
        22 => self::ACHIEVE_BACK_PICK,
        23 => self::ACHIEVE_ALLOT_PICK,
        25 => self::ACHIEVE_SALE_PICK
    ];
    /**
     * 出库异常原因
     */
    const OUT_STORE_EXCEPTION_REMARK = [
        1 => '实物丢失',
        2 => '转残',
        3 => '商品信息不符',
        4 => '商品原吊牌丢失',
    ];

    /**
     * 报损单状态
     */
    const imperfect_loss_status_void = -1;
    const imperfect_loss_status_wait = 1;
    const imperfect_loss_status_out_wait = 2;
    const imperfect_loss_status_wait_proof = 3;
    const imperfect_loss_status_finish = 4;

    const imperfect_loss_status_map = [
        self::imperfect_loss_status_void => '已作废',
        self::imperfect_loss_status_wait => '待接单',
        self::imperfect_loss_status_out_wait => '待出库',
        self::imperfect_loss_status_wait_proof => '待传凭证',
        self::imperfect_loss_status_finish => '已完成'
    ];

    //售后单处理时，仓库默认选中天津嘉民仓
    const select_stock_id = 1082;

    #是否装箱
    const GOODS_PACK = 1;#是
    const NOT_GOODS_PACK = 0;#否

    #模版类型
    const TEMPLATE_FIVE_TEN = 1;
    const TEMPLATE_FOUR_NINE = 2;
    const TEMPLATE_RFID_FIVE_NINE = 3;
    const TEMPLATE_FOUR_NINE_TSC = 4;
    const TEMPLATE_FOUR_SIX_TSC = 5;
    const TEMPLATE_SIX_FOUR_AILI = 6;
    const TEMPLATE_SIX_FOUR_AILI_BIND = 7;
    const TEMPLATE_FOUR_NINE_AILI_BIND = 8;
    const TEMPLATE_SIX_FOUR_AILI_BIND_PASSTAG = 9;
    const TEMPLATE_FOUR_NINE_AILI_BIND_PASSTAG = 10;
    const TEMPlATE_TYPE = [
        ['type' => SELF::TEMPLATE_FIVE_TEN, 'name' => '5*10模版', "checked" => false],
        ['type' => SELF::TEMPLATE_FOUR_NINE, 'name' => '4*9模版', "checked" => false],
        ['type' => SELF::TEMPLATE_FOUR_NINE_TSC, 'name' => '4*9模版-RFID', "checked" => false],
        ['type' => SELF::TEMPLATE_RFID_FIVE_NINE, 'name' => '5.5*9.5模版-RFID', "checked" => true],
        ['type' => SELF::TEMPLATE_FOUR_SIX_TSC, 'name' => '6*4模版-TSC打印机', "checked" => false],
        ['type' => SELF::TEMPLATE_SIX_FOUR_AILI, 'name' => '6*4模版-艾利打印机', "checked" => false],
        ['type' => SELF::TEMPLATE_SIX_FOUR_AILI_BIND, 'name' => '6*4模板-艾利-自动绑定', "checked" => false],
        ['type' => SELF::TEMPLATE_FOUR_NINE_AILI_BIND, 'name' => '4*9模板-艾利-自动绑定', "checked" => false],
        ['type' => SELF::TEMPLATE_SIX_FOUR_AILI_BIND_PASSTAG, 'name' => '6*4模板-PASSTAG-自动绑定', "checked" => false],
        ['type' => SELF::TEMPLATE_FOUR_NINE_AILI_BIND_PASSTAG, 'name' => '4*9模板-PASSTAG-自动绑定', "checked" => false],
    ];
    const TEMPLATE_FOUR_NINE_AILI = 1;
    const TEMPLATE_FOUR_SIX_AILI = 2;
    const TEMPlATE_FRONT_TYPE = [
        ['type' => SELF::TEMPLATE_FOUR_NINE_TSC, 'name' => '4*9模版-TSC打印机', "checked" => false],
        ['type' => SELF::TEMPLATE_FOUR_SIX_TSC, 'name' => '6*4模版-TSC打印机', "checked" => true],
        ['type' => SELF::TEMPLATE_FOUR_NINE_AILI, 'name' => '4*9模版-艾利打印机', "checked" => false],
        ['type' => SELF::TEMPLATE_FOUR_SIX_AILI, 'name' => '6*4模版-艾利打印机', "checked" => false],
        ['type' => SELF::TEMPLATE_SIX_FOUR_AILI_BIND_PASSTAG, 'name' => '6*4模板-PASSTAG', "checked" => false],
        ['type' => SELF::TEMPLATE_FOUR_NINE_AILI_BIND_PASSTAG, 'name' => '4*9模板-PASSTAG', "checked" => false],
    ];

    const EPC_RULE = [
        SELF::TEMPLATE_FOUR_NINE_AILI => [
            'check' => 0,
            'company_sign' => 9,
            'epc_type' => 2
        ],
        SELF::TEMPLATE_FOUR_SIX_AILI => [
            'check' => 0,
            'company_sign' => 9,
            'epc_type' => 1
        ],
        SELF::TEMPLATE_SIX_FOUR_AILI_BIND => [
            'check' => 0,
            'company_sign' => 9,
            'epc_type' => 1
        ],
        SELF::TEMPLATE_FOUR_NINE_AILI_BIND => [
            'check' => 0,
            'company_sign' => 9,
            'epc_type' => 2
        ],
        SELF::TEMPLATE_SIX_FOUR_AILI_BIND_PASSTAG => [
            'check' => 0,
            'company_sign' => 9,
            'epc_type' => 1
        ],
        SELF::TEMPLATE_FOUR_NINE_AILI_BIND_PASSTAG => [
            'check' => 0,
            'company_sign' => 9,
            'epc_type' => 2
        ]
    ];

    #生产类型
    const PRODUCTION_TYPE_ALLO = 1;
    const PRODUCTION_TYPE_INSTORE = 2;
    const PRODUCTION_TYPE = [
        SELF::PRODUCTION_TYPE_ALLO => '调拨生产',
        SELF::PRODUCTION_TYPE_INSTORE => '新品入库'
    ];

    #入库方式
    const IN_STORE_UNIQUE_CODE =1;
    const IN_STORT_BARCODE = 2;
    const IN_STORE_TYPE = [
        SELF::IN_STORE_UNIQUE_CODE => '店内码入库',
        SELF::IN_STORT_BARCODE => '条码入库'
    ];

    //价签模本类型
    const ONE_TEMP_TYPE = 1;
    const FOUR_TEMP_TYPE = 2;
    const TWELVE_TEMP_TYPE = 3;
    const EIGHTEEN_TEMP_TYPE = 4;
    const PRICE_TEMP_TYPE = [
        SELF::ONE_TEMP_TYPE => '一图模版',
        SELF::FOUR_TEMP_TYPE => '四图模版',
        SELF::TWELVE_TEMP_TYPE => '十二图模版',
        SELF::EIGHTEEN_TEMP_TYPE => '十八图模版'
    ];
}
