<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Controller;

use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;

/**
 * @Controller()
 */
class AdminController extends AbstractController
{
   /**
     * @Inject()
     * @var \App\JsonRpc\AdminServiceInterface
     */
    private $AdminService;


    /**
     * @RequestMapping(path="/admin/get_admin_info", methods="get,post")
     * @param RequestInterface $request
     */
    public function get_admin_info(RequestInterface $request){
        $admin_code = $request->input('admin_code');
        if (empty(trim($admin_code))){
            return $this->returnApi('501','编码不能为空');
        }
        $admin_info = $this->AdminService->codeToInfo($admin_code);
        if (empty($admin_info)){
            $admin_info = $this->AdminService->mobileToInfo($admin_code);
            if (empty($admin_info)){
                return $this->returnApi('501','用户不存在');
            }else{
                return $this->returnApi('200','获取用户信息成功',$admin_info);
            }
        }else{
            if ($admin_info['status'] != 1){
                return $this->returnApi('501','无效用户');
            }else{
                return $this->returnApi('200','获取用户信息成功',$admin_info);
            }
        }
    }
}