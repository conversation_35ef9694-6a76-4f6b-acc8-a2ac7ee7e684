<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use App\Constants\CachePre;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Exception\BusinessException;
use App\JsonRpc\TemplateSetServiceInterface;
use App\Library\Facades\AdminService;
use App\Library\Facades\ConfigService;
use App\Library\Facades\HashService;
use App\Library\Facades\InStoreService;
use App\Library\Facades\SyncTaskService;
use App\Library\Facades\TemplateSetService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Validation\Rule;

/**
 * @Controller(prefix="/instore")
 */
class InStoreController extends AbstractController
{
    /**
     * @Inject()
     * @var \App\JsonRpc\WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject()
     * @var \App\JsonRpc\SupplierServiceInterface
     */
    private $supplierService;

    /**
     * @Inject()
     * @var \App\JsonRpc\BrandServiceInterface
     */
    private $brnadService;

    /**
     * @Inject()
     * @var \App\JsonRpc\ArrivalOrderServiceInterface
     */
    private $arrivalOrderService;

    /**
     * @Inject()
     * @var \App\JsonRpc\LogisticsInfoServiceInterface
     */
    private $logisticsInfoService;

    private $status = [
        '0' => '入库中',
        '1' => '入库完成',
        '-1' => '作废',
    ];

    private $batch_type = [
        '1' => '店内码',
        '2' => '条形码'
    ];

    private $search_type = [
        '1' => '店内码查找',
        '2' => '货号查找',
        '3' => '条形码查询',
        '4' => 'SPU查询',
        '5' => 'SKU查询',
    ];

    private $listSearchTypeMap = [
        '1' => 'in_unique_code',
        '2' => 'spu_no',
        '3' => 'barcode',
        '4' => 'spu_id',
        '5' => 'sku_id',
    ];

    private $searchTypeMap = [
        '1' => 'unique_code',
        '2' => 'spu_no',
        '3' => 'barcode',
        '4' => 'spu_id',
        '5' => 'sku_id',
    ];

    const TYPE_MAP = [
        1 => '采购入库',
        2 => '差异采购入库',
        3 => '调拨入库',
        4 => '盘盈入库',
        5 => '退货入库',
        6 => '反向调拨入库'
    ];

    /**
     * @RequestMapping(path="add", methods="get,post")
     * @return mixed
     */
    public function add(RequestInterface $request)
    {
        if ($this->isAjax()) {
            $params = $this->validate($request->all(),'add');

            $userInfo = $this->getUserInfo();

            $isTransfer = 0;
            if (in_array($params['type'],[1,2])){
                //获取入库批次信息
                $arrivalOrder = $this->arrivalOrderService->getOrder(['serial_no' => $params['serial_no']]);
                if (empty($arrivalOrder)){
                    return $this->returnApi(ResponseCode::SERVICE_ERROR, "入库批次不存在");
                }

                //检测生成的入库批次是否为中转入库批次

                if ($params['batch_type'] == 1 && isset($arrivalOrder['allot_w_id']) && !empty($arrivalOrder['allot_w_id']) && $arrivalOrder['w_id'] != $arrivalOrder['allot_w_id']){//店内码 &&  存在调入仓 && 调入仓id不为空 && 预约仓!=调入仓
                    //获取中转入库批次 不存在中转入库批次时，生成中转入库批次
                    $inStoreInfo = InStoreService::getInStoreInfo(['arrival_no' => $params['serial_no'], 'statusS' => [0, 1], 'is_transfer' => 1, 'batch_type' => 1]);
                    if (empty($inStoreInfo)){
                        $isTransfer = 1;
                    }
                }
            }

            try {
                $params['admin_id'] = $userInfo['uid'];
                $params['admin_name'] = $userInfo['nickname'];
                $params['is_transfer'] = $isTransfer;
                $info = InStoreService::add($params);
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }
            $data = [];
            $data['op_id'] = $info['id'];
            $data['req_router_name'] = '创建任务单';
            $data['snow_id'] = strval($request->getAttribute('snow_id'));
            wlog($data['snow_id'],$data);
            return $this->returnApi(ResponseCode::SUCCESS, '创建成功',$info);
        }
    }

    /**
     * 字符转码方法
     */
    private function encoding_convert ($str = '')
    {
        $str = str_replace( 'º', '°', $str );
        //将字符串的编码从UTF-8 转到gbk
        return iconv( 'UTF-8', 'gbk//IGNORE', $str );
    }

    /**
     * @RequestMapping(path="list", methods="get,post")
     * @return mixed
     */
    public function list(RequestInterface $request){
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        //获取仓库信息
        $warehouse_list = $this->WarehouseService->getWarehouses(['ids'=>$wIds],['id','name']);
        //获取供应商信息
        $supplier_list = $this->supplierService->getSuppliers([], ['id','name']);
        //品牌信息
        $brand_list = $this->brnadService->getBrands();
        $where = $this->validate($request->all(),'search');
        if ($this->isAjax()) {
            $where = $this->validate($request->all(),'search');
            $page = intval($request->input('page',1));
            $limit = intval($request->input('limit',30));

            if (!empty($where['date_range'])){
                $dateRangeArray = explode(' - ',$where['date_range']);
                $where['start_time'] = $dateRangeArray[0];
                $where['end_time'] = date('Y-m-d 23:59:59',strtotime($dateRangeArray[1]));
                unset($where['date_range']);
            }
            if (!empty($where['order_no'])){
                $serialNos = explode("\n",$where['order_no']);
                $where['order_no'] = [];
                foreach ($serialNos as $item){
                    $serial_no = trim($item);
                    if(!empty($serial_no)){
                        $where['order_no'][] = $serial_no;
                    }
                }
                if(empty($where['order_no'])){
                    unset($where['order_no']);
                }
            }else{
                unset($where['order_no']);
            }
            if (!empty($where['search_type']) && array_key_exists($where['search_type'],$this->search_type)){
                $searchKey = $this->listSearchTypeMap[$where['search_type']];
                $where[$searchKey] = $where['search_value'];
                unset($where['search_type']);
                unset($where['search_value']);
            }
            if (!empty($where['warehouse_ids'])){
                $where['w_id'] = explode(',',$where['warehouse_ids']);
                unset($where['warehouse_ids']);
            }else{
                $where['w_id'] = $wIds;
            }

            if (!empty($where['supplier_ids'])){
                $where['sup_id'] = explode(',',$where['supplier_ids']);
                unset($where['supplier_ids']);
            }
            $list = InStoreService::list($page, $limit, $where);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $limit]);
        }

        $data = [
            'warehouse_list' => $warehouse_list,
            'supplier_list' => $supplier_list,
            'brand_list' => $brand_list,
            'status_list' => $this->status,
            'search_type' => $this->search_type,
            'in_type' => self::TYPE_MAP,
            'where' => $where
        ];

        logger()->debug('instore/list:data',[$data]);

        return $this->show('instore/list', $data);
    }


    /**
     * @RequestMapping(path="batch_list", methods="get,post")
     * @return mixed
     */
    public function batchList(RequestInterface $request){
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        //获取仓库信息
        $warehouse_list = $this->WarehouseService->getWarehouses(['ids'=>$wIds],['id','name']);
        //获取供应商信息
        $supplier_list = $this->supplierService->getSuppliers([], ['id','name']);
        //品牌信息
        $brand_list = $this->brnadService->getBrands();
        $where = $this->validate($request->all(),'search');
        if ($this->isAjax()) {
            $where = $this->validate($request->all(),'search');
            logger()->info('where=======================================', [$where]);
            $page = intval($request->input('page',1));
            $limit = intval($request->input('limit',30));

            if (!empty($where['date_range'])){
                $dateRangeArray = explode(' - ',$where['date_range']);
                $where['start_time'] = $dateRangeArray[0];
                $where['end_time'] = date('Y-m-d 23:59:59',strtotime($dateRangeArray[1]));
                unset($where['date_range']);
            }
            if (!empty($where['search_type']) && array_key_exists($where['search_type'],$this->search_type)){
                $searchKey = $this->listSearchTypeMap[$where['search_type']];
                $where[$searchKey] = $where['search_value'];
                unset($where['search_type']);
                unset($where['search_value']);
            }
            if (!empty($where['warehouse_ids'])){
                $where['w_id'] = explode(',',$where['warehouse_ids']);
                unset($where['warehouse_ids']);
            }else{
                $where['w_id'] = $wIds;
            }
//            $where['type'] = 1;
            if (!empty($where['supplier_ids'])){
                $where['sup_id'] = explode(',',$where['supplier_ids']);
                unset($where['supplier_ids']);
            }
            if (!empty($where['brand_ids'])){
                $where['brand_id'] = explode(',',$where['brand_ids']);
                unset($where['brand_ids']);
            }
            $list = InStoreService::list($page, $limit, $where);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $limit]);
        }

        $data = [
            'warehouse_list' => $warehouse_list,
            'supplier_list' => $supplier_list,
            'brand_list' => $brand_list,
            'status_list' => $this->status,
            'search_type' => $this->search_type,
            'where' => $where
        ];

        logger()->debug('instore/list:data',[$data]);

        return $this->show('instore/batch_list', $data);
    }

    /**
     * @param $where array 查询参数
     * @param $wIds array 仓库id列表
     * @return mixed
     */
    private function handleRevAllotSearch($where,$wIds){
        if (!empty($where['date_range'])){
            $dateRangeArray = explode(' - ',$where['date_range']);
            $where['start_check_at'] = $dateRangeArray[0];
            $where['end_check_at'] = date('Y-m-d 23:59:59',strtotime($dateRangeArray[1]));
            unset($where['date_range']);
        }
        if (!empty($where['search_type']) && array_key_exists($where['search_type'],$this->search_type)){
            $searchKey = $this->listSearchTypeMap[$where['search_type']];
            $where[$searchKey] = $where['search_value'];
            unset($where['search_type']);
            unset($where['search_value']);
        }
        if (!empty($where['in_w_id'])){
            $where['in_w_id'] = explode(',',$where['in_w_id']);
        }

        if (!empty($where['out_w_id'])){
            $where['w_id'] = explode(',',$where['out_w_id']);
        }else{
            $where['w_id'] = $wIds;
        }

        $where['type'] = 6;
        unset($where['out_w_id']);
        return $where;
    }
    /**
     * 反向调拨列表
     * @RequestMapping(path="rev_allot_list", methods="get,post")
     * @return mixed
     */
    public function revAllotList(RequestInterface $request){
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        //获取仓库信息
        $warehouse_list = $this->WarehouseService->getWarehouses([],['id','name']);
        $out_warehouse_list = [];
        foreach ($warehouse_list as $item){
            if(in_array($item['id'],$wIds)){
                $out_warehouse_list[] = $item;
            }
        }
        //品牌信息
        $where = $this->validate($request->all(),'search_rev_allot');
        logger()->info('request where',[$where]);
        if ($this->isAjax()) {
            $page = intval($request->input('page',1));
            $limit = intval($request->input('limit',30));
            $where = $this->handleRevAllotSearch($where,$wIds);
            $list = InStoreService::revAllotlist($page, $limit, $where);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $limit]);
        }

        $data = [
            'warehouse_list' => $warehouse_list,
            'out_warehouse_list' => $out_warehouse_list,
            'status_list' => $this->status,
            'search_type' => $this->search_type,
            'where' => $where
        ];

        return $this->show('instore/rev_allot_list', $data);
    }

    /**
     * 反向调拨单详情列表导出
     * @RequestMapping(path="export_rev_allot", methods="get,post")
     * @return mixed
     */
    public function exportRevAllot(RequestInterface $request){
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $where = $this->validate($request->all(),'search_rev_allot');
        $where = $this->handleRevAllotSearch($where,$wIds);
        $list = InStoreService::revAllotlist(0, 0, $where);
        if($list['data']){
            try {
                $url = exportToExcel(config('file_header.rev_allot_export'),$list['data'],'反向调拨单详情列表');
            }catch (\Exception $e){
                throw new BusinessException('导出失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
            }
        }else{
            throw new BusinessException('无数据，导出失败！');
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
    }

    /**
     * @RequestMapping(path="export", methods="get,post")
     * @return mixed
     */
    public function export(RequestInterface $request){
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $where = $this->validate($request->all(),'search');
        if (!empty($where['date_range'])){
            $dateRangeArray = explode(' - ',$where['date_range']);
            $where['start_time'] = $dateRangeArray[0];
            $where['end_time'] = date('Y-m-d 23:59:59',strtotime($dateRangeArray[1]));
            unset($where['date_range']);
        }
        if (!empty($where['search_type']) && array_key_exists($where['search_type'],$this->search_type)){
            $searchKey = $this->listSearchTypeMap[$where['search_type']];
            $where[$searchKey] = $where['search_value'];
            unset($where['search_type']);
            unset($where['search_value']);
        }
        if (!empty($where['warehouse_ids'])){
            $where['w_id'] = explode(',',$where['warehouse_ids']);
            unset($where['warehouse_ids']);
        }else{
            $where['w_id'] = $wIds;
        }
//        $where['type'] = 1;
        if (!empty($where['supplier_ids'])){
            $where['sup_id'] = explode(',',$where['supplier_ids']);
            unset($where['supplier_ids']);
        }
        if (!empty($where['brand_ids'])){
            $where['brand_id'] = explode(',',$where['brand_ids']);
            unset($where['brand_ids']);
        }
        if (!empty($where['order_no'])){
            $serialNos = explode("\n",$where['order_no']);
            $where['order_no'] = [];
            foreach ($serialNos as $item){
                $serial_no = trim($item);
                if(!empty($serial_no)){
                    $where['order_no'][] = $serial_no;
                }
            }
            if(empty($where['order_no'])){
                unset($where['order_no']);
            }
        }else{
            unset($where['order_no']);
        }
        $list = InStoreService::list(0, 0, $where);
        if($list['data']){
            try {
                $url = exportToExcel(config('file_header.instore_export'),$list['data'],'入库任务单列表');
            }catch (\Exception $e){
                throw new BusinessException('导出失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
            }
        }else{
            throw new BusinessException('无数据，导出失败！');
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
    }

    /**
     * @RequestMapping(path="export_no_spu", methods="get,post")
     * @return mixed
     */
    public function exportNoSpu(RequestInterface $request){
        $where = $this->validate($request->all(),'search');

        if (!empty($where['date_range'])){
            $dateRangeArray = explode(' - ',$where['date_range']);
            $where['start_time'] = $dateRangeArray[0];
            $where['end_time'] = date('Y-m-d 23:59:59',strtotime($dateRangeArray[1]));
            unset($where['date_range']);
        }
        if (!empty($where['search_type']) && array_key_exists($where['search_type'],$this->search_type)){
            $searchKey = $this->listSearchTypeMap[$where['search_type']];
            $where[$searchKey] = $where['search_value'];
            unset($where['search_type']);
            unset($where['search_value']);
        }
        if (!empty($where['warehouse_ids'])){
            $where['w_id'] = explode(',',$where['warehouse_ids']);
            unset($where['warehouse_ids']);
        }

        if (!empty($where['supplier_ids'])){
            $where['sup_id'] = explode(',',$where['supplier_ids']);
            unset($where['supplier_ids']);
        }
        $list = InStoreService::list(0, 0, $where);
        if($list['data']){
            try {
                $url = exportToExcel(config('file_header.instore_export'),$list['data'],'入库任务单列表');
            }catch (\Exception $e){
                throw new BusinessException('导出失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
            }
        }else{
            throw new BusinessException('无数据，导出失败！');
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
    }


    /**
     * @RequestMapping(path="detail_list", methods="get,post")
     * @return mixed
     */
    public function detailList(RequestInterface $request){
        $where = $this->validate($request->all(),'detail_search');
        $page = intval($request->input('page',1));
        $limit = intval($request->input('limit',30));

        if (!empty($where['search_type']) && array_key_exists($where['search_type'],$this->search_type)){
            $searchKey = $this->searchTypeMap[$where['search_type']];
            $where[$searchKey] = $where['search_value'];
        }
        unset($where['search_type']);
        unset($where['search_value']);

        $list = InStoreService::detailList($where,$page ,$limit);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $limit]);
    }

    /**
     * @RequestMapping(path="diff_list", methods="get,post")
     * @return mixed
     */
    public function diffList(RequestInterface $request){
        $where = $this->validate($request->all(),'diff_search');
        $page = intval($request->input('page',1));
        $limit = intval($request->input('limit',30));
        $list = InStoreService::diffList($where,$page ,$limit);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $limit]);
    }

    /**
     * @RequestMapping(path="diff_export", methods="get,post")
     * @return mixed
     */
    public function diffExport(RequestInterface $request)
    {
        $where = $this->validate($request->all(),'diff_search');
        $list = InStoreService::diffList($where,0);
        if($list['data']){
            try {
                $url = exportToExcel(config('file_header.instore_diff_export'),$list['data'],'入库详情');
            }catch (\Exception $e){
                throw new BusinessException('导出失败！',ResponseCode::SERVER_ERROR,$e);
            }
        }else{
            throw new BusinessException('无数据，导出失败！');
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
    }

    /**
     * @RequestMapping(path="bat_diff_export", methods="get,post")
     * @return mixed
     */
    public function batDiffExport(RequestInterface $request)
    {
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $where = $this->validate($request->all(),'search');
        if (!empty($where['warehouse_ids'])){
            $where['w_id'] = explode(',',$where['warehouse_ids']);
            unset($where['warehouse_ids']);
        }else{
            $where['w_id'] = $wIds;
        }
        $list = InStoreService::batDiffList($where,0);
        if($list['data']){
            try {
                $url = exportToExcel(config('file_header.bat_diff_export'),$list['data'],'无商品信息差异列表',["任务单号" => 'string',
                    "采购单号" => 'string',
                    "预约单号" => 'string',
                    "采购商" => 'string',
                    "结算方式" => 'string',
                    "仓库" => 'string',
                    "批次号" => 'string',
                    "店内码" => 'string',
                    "条形码" => 'string',
                    "原始条形码" => 'string',
                    "SPU" => 'number',
                    "SKU" => 'number',
                    "差异类型" => 'string',
                    "数量" => 'number',
                    "差异数量" => 'number',
                    "货架位" => 'string',
                    "操作人" => 'string',
                    "操作时间" => 'string']);
            }catch (\Exception $e){
                throw new BusinessException('导出失败！',ResponseCode::SERVER_ERROR,$e);
            }
        }else{
            throw new BusinessException('无数据，导出失败！');
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
    }

    /**
     * @RequestMapping(path="detail_export", methods="get,post")
     * @return mixed
     */
    public function detailExport(RequestInterface $request)
    {
        $where = $this->validate($request->all(),'detail_search');

        if (!empty($where['search_type']) && array_key_exists($where['search_type'],$this->search_type)){
            $searchKey = $this->searchTypeMap[$where['search_type']];
            $where[$searchKey] = $where['search_value'];
        }
        unset($where['search_type']);
        unset($where['search_value']);

        $list = InStoreService::detailList($where,0);
        if($list['data']){
            try {
                $url = exportToExcel(config('file_header.instore_detail_export'),$list['data'],'入库详情');
            }catch (\Exception $e){
                throw new BusinessException('导出失败！',ResponseCode::SERVER_ERROR,$e);
            }
        }else{
            throw new BusinessException('无数据，导出失败！');
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
    }

    /**
     * @RequestMapping(path="cancel", methods="get,post")
     * @return mixed
     */
    public function cancel(RequestInterface $request){
        $id = $request->input('id');
        if(!$id){
            throw new BusinessException('任务id不能为空');
        }
        $result = InStoreService::cancel($id);
        if($result){
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }else{
            return $this->returnApi(ResponseCode::SERVER_ERROR, '操作失败');
        }
    }

    /**
     * @RequestMapping(path="changeBarcode", methods="get,post")
     * @return mixed
     */
    public function changeBarcode(RequestInterface $request){
        $id = $request->input('id');
        if(!$id){
            throw new BusinessException('任务id不能为空');
        }
        $changeList = $request->input('changeList',[]);
        if(empty($changeList)){
            throw new BusinessException('变更数据不能为空');
        }
        $result = InStoreService::changeBarcode(['in_store_id' => $id,'changeList'=>$changeList]);
        if($result){
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }else{
            return $this->returnApi(ResponseCode::SERVER_ERROR, '操作失败');
        }
    }

    /**
     * @RequestMapping(path="changeUniqueCodeBarcode", methods="get,post")
     * @return mixed
     */
    public function changeUniqueCodeBarcode(RequestInterface $request){
        $id = $request->input('id');
        if(!$id){
            throw new BusinessException('任务id不能为空');
        }
        $changeList = $request->input('changeList',[]);
        if(empty($changeList)){
            throw new BusinessException('变更数据不能为空');
        }
        $result = InStoreService::changeUniqueCodeBarcode(['in_store_id' => $id,'changeList'=>$changeList]);
        if($result){
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }else{
            return $this->returnApi(ResponseCode::SERVER_ERROR, '操作失败');
        }
    }

    /**
     * @RequestMapping(path="changePage", methods="get,post")
     * @return mixed
     */
    public function changePage(RequestInterface $request){
        $id = $request->input('id');
        if(!$id){
            throw new BusinessException('任务id不能为空');
        }

        $barcodes = $request->input('barcodes');
        if(!$barcodes){
            throw new BusinessException('条码不能为空');
        }
        $barcodes = explode(',',$barcodes);
        $data['id'] = $id;
        $data['changeList'] = [];
        $barcodes = array_unique($barcodes);
        foreach ($barcodes as $item){
            $data['changeList'][] = [
                'old_barcode' => $item,
                'new_barcode' => ''
            ];
        }
        return $this->show('instore/changeBarcode', $data);
    }

    /**
     * @RequestMapping(path="changeImportPage", methods="get,post")
     * @return mixed
     */
    public function changeImportPage(RequestInterface $request){
        $id = $request->input('id');
        if(!$id){
            throw new BusinessException('任务id不能为空');
        }
        $info = InStoreService::info($id);
        if(!$info){
            throw new BusinessException('无法获取任务信息');
        }
        $batch_type = $info['batch_type'];
        $data['id'] = $id;
        $data['batch_type'] = $batch_type;
        $tplList = TemplateSetService::tplList(TemplateSetService::TYPE_CHANGE_BARCODE);
        $data['tplInfo'] = $tplList[$batch_type];
        logger()->info('changeImportPage:params',[$data]);
        return $this->show('instore/changeImport', $data);
    }

    /**
     * @RequestMapping(path="batChangeImportPage", methods="get,post")
     * @return mixed
     */
    public function batChangeImportPage(RequestInterface $request){
        $batch_type = $request->input('batch_type');
        if(!$batch_type){
            throw new BusinessException('变更类型不能为空');
        }

        $data['batch_type'] = $batch_type;
        $tplList = TemplateSetService::tplList(TemplateSetService::TYPE_BAT_CHANGE_BARCODE);
        if(!isset($tplList[$batch_type])){
            throw new BusinessException('变更类型无对应模板信息');
        }
        $data['tplInfo'] = $tplList[$batch_type];
        logger()->info('changeImportPage:params',[$data]);
        return $this->show('instore/batChangeImport', $data);
    }

    /**
     * @RequestMapping(path="batChangeImport", methods="get,post")
     * @return mixed
     */
    public function batChangeImport(RequestInterface $request){
        $batch_type = $request->input('batch_type');
        $file = $request->file('file');
        $userInfo = $this->getUserInfo();
        if($batch_type == 1){
            $ruleMap = [
                'msg' => [
                    'in_stock_no.required'=>'批次编码不能为空',
                    'unique_code.required'=>'店内码不能为空',
                    'new_barcode.required'=>'条码不能为空'
                ],
                'rule' => ['unique_code'=>'required','in_stock_no'=>'required','new_barcode'=>'required']
            ];
            $configTemplate = getTemplateInfo(TemplateSetService::TYPE_BAT_CHANGE_BARCODE, $batch_type);
            logger()->debug('changeImport',[$configTemplate]);
            // 读取excel数据
            $importData = readExcelByHeader($file, $configTemplate['original_data'],
                $ruleMap['rule'],
                $ruleMap['msg']);

        }else{
            $ruleMap = [
                'msg' => [
                    'in_stock_no.required'=>'批次编码不能为空',
                    'old_barcode.required'=>'旧条码不能为空',
                    'new_barcode.required'=>'新条码不能为空',
                ],
                'rule' => ['in_stock_no'=>'required','old_barcode'=>'required','new_barcode'=>'required']
            ];
            $configTemplate = getTemplateInfo(TemplateSetService::TYPE_BAT_CHANGE_BARCODE, $batch_type);
            // 读取excel数据
            $importData = readExcelByHeader($file, $configTemplate['original_data'],
                $ruleMap['rule'],
                $ruleMap['msg']);
        }
        if(empty($importData)){
            throw new BusinessException('上传数据不能为空');
        }
        $cacheKey = CachePre::getKey(sprintf(CachePre::ALLOT_BATCH_IN_DATA,$userInfo['uid']));
        redis()->set($cacheKey,gzencode(json_encode($importData,JSON_UNESCAPED_UNICODE)),CachePre::CACHE_ONE_HOUR);
        $params = [];
        $params['service_name'] = 'in_store:batChangeCode';
        $params['admin_id'] = $userInfo['uid'];
        $params['admin_name'] = $userInfo['nickname'];
        $params['task_name'] = '无商品信息商品批量变更店内码';
        $params['sys_type'] = 'wms';
        $params['params'] = json_encode([[
            'admin_id'=>$userInfo['uid'],
            'admin_name'=>$userInfo['nickname'],
            'batch_type'=>$batch_type,
            'cache_key'=>$cacheKey,
        ]]);
        $taskInfo = SyncTaskService::add($params,60);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $taskInfo);
    }

    /**
     * @RequestMapping(path="changeImport", methods="get,post")
     * @return mixed
     */
    public function changeImport(RequestInterface $request){
        // 存在则返回，不存在则返回默认值 null
        $id = intval($request->input('in_store_id'));
        if(!$id){
            throw new BusinessException('任务id不能为空');
        }
        $info = InStoreService::info($id);
        if(!$info){
            throw new BusinessException('无法获取任务信息');
        }
        $batch_type = $info['batch_type'];
        $file = $request->file('file');
        if($batch_type == 1){
            $ruleMap = [
                'msg' => [
                    'unique_code.required'=>'店内码不能为空',
                    'new_barcode.required'=>'条码不能为空'
                ],
                'rule' => ['unique_code'=>'required','new_barcode'=>'required']
            ];
            $configTemplate = getTemplateInfo(TemplateSetService::TYPE_CHANGE_BARCODE, $batch_type);
            logger()->debug('changeImport',[$configTemplate]);
            // 读取excel数据
            $importData = readExcelByHeader($file, $configTemplate['original_data'],
                $ruleMap['rule'],
                $ruleMap['msg']);
            $result = InStoreService::changeUniqueCodeBarcode(['in_store_id' => $id,'changeList'=>$importData]);
        }else{
            $ruleMap = [
                'msg' => [
                    'old_barcode.required'=>'旧条码不能为空',
                    'new_barcode.required'=>'新条码不能为空',
                ],
                'rule' => ['old_barcode'=>'required','new_barcode'=>'required']
            ];
            $configTemplate = getTemplateInfo(TemplateSetService::TYPE_CHANGE_BARCODE, $batch_type);
            // 读取excel数据
            $importData = readExcelByHeader($file, $configTemplate['original_data'],
                $ruleMap['rule'],
                $ruleMap['msg']);
            $result = InStoreService::changeBarcode(['in_store_id' => $id,'changeList'=>$importData]);
        }

        if($result){
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }else{
            return $this->returnApi(ResponseCode::SERVER_ERROR, '操作失败');
        }
    }

    /**
     * @RequestMapping(path="changeUniqueCodePage", methods="get,post")
     * @return mixed
     */
    public function changeUniqueCodePage(RequestInterface $request){
        $id = $request->input('id');
        if(!$id){
            throw new BusinessException('任务id不能为空');
        }

        $ids = $request->input('ids');
        if(!$ids){
            throw new BusinessException('请选择要变更的数据');
        }
        $ids = explode(',',$ids);
        $list = InStoreService::detailByIds($ids);

        $data['id'] = $id;
        $data['changeList'] = [];
        foreach ($list as $item){
            $data['changeList'][] = [
                'unique_code' => $item['unique_code'],
                'old_barcode' => $item['old_barcode'],
                'new_barcode' => ''
            ];
        }
        return $this->show('instore/changeUniqueCodeBarcode', $data);
    }

    /**
     * @RequestMapping(path="detail", methods="get,post")
     * @return mixed
     */
    public function detail(RequestInterface $request){
        $data['title'] = "任务单明细";
        // 存在则返回，不存在则返回默认值 null
        $id = $request->input('id');
        $info = InStoreService::info($id);

        if (empty($info)){
            $data['error_info'] = "任务单不存在";
        }else{
            $data['info'] = $info;
        }
        $logInfo = getLog(['model_name'=>'instore','op_id'=>$id]);
        $data['log_list'] = $logInfo['data'] ?? [];
        logger()->debug('detail_data',['data'=>$data]);
        return $this->show('instore/detail', $data);
    }

    /**
     * @RequestMapping(path="import", methods="get,post")
     * @return mixed
     */
    public function import(RequestInterface $request){
        $data['title'] = "货品入库";
        // 存在则返回，不存在则返回默认值 null
        $id = intval($request->input('id'));
        $info = InStoreService::info($id);
        $totalCacheKey = CachePre::getKey(CachePre::INSTORE_TOTAL_DATA,$this->getUserId(),$id) ;
        if (empty($info)){
            throw new BusinessException('任务单不存在');
        }
        if ($this->isAjax()) {
            $data = $this->validate($this->request->all(),'import');
            $file = $request->file('file');

            if($info['type'] == 3){
                $ruleMap = [
                    2 => [
                        'msg' => [
                            'admin_id.integer'=>'员工号为数值',
                            'admin_id.required'=>'员工号不能为空',
                            'barcode.required'=>'条码不能为空',
                            'shelf_code.required'=>'货架号不能为空',
                            'num.integer'=>'数量必须为数值',
                            'num.required'=>'数量不能为空',
                            'num.min'=>'数量必须大于1'
                        ],
                        'rule' => ['admin_id'=>'required|integer','num'=>'required|integer|min:1','barcode'=>'required','shelf_code'=>'required']
                    ],
                    1 => [
                        'msg' => [
                            'admin_id.integer'=>'员工号为数值',
                            'admin_id.required'=>'员工号不能为空',
                            'unique_code.required'=>'店内码不能为空',
                            'shelf_code.required'=>'货架号不能为空'
                        ],
                        'rule' => ['admin_id'=>'required|integer','unique_code'=>'required','shelf_code'=>'required']
                    ]
                ];
                // 读取excel数据
                $importData = readExcelByHeader($file, config('file_header.allot_instore_import')[$data['import_type']],
                    $ruleMap[$data['import_type']]['rule'],
                    $ruleMap[$data['import_type']]['msg']);
            }else{
                $ruleMap = [
                    2 => [
                        'msg' => [
                            'admin_id.integer'=>'员工号为数值',
                            'admin_id.required'=>'员工号不能为空',
                            'barcode.required'=>'条码不能为空',
                            'shelf_code.required'=>'货架号不能为空',
                            'num.integer'=>'数量必须为数值',
                            'num.required'=>'数量不能为空',
                            'num.min'=>'数量必须大于1'
                        ],
                        'rule' => ['admin_id'=>'required|integer','num'=>'required|integer|min:1','barcode'=>'required','shelf_code'=>'required']
                    ],
                    1 => [
                        'msg' => [
                            'admin_id.integer'=>'员工号为数值',
                            'admin_id.required'=>'员工号不能为空',
                            'unique_code.required'=>'店内码不能为空',
                            'barcode.required'=>'条码不能为空',
                            'shelf_code.required'=>'货架号不能为空'
                        ],
                        'rule' => ['admin_id'=>'required|integer','unique_code'=>'required','barcode'=>'required','shelf_code'=>'required']
                    ]
                ];
                $configTemplate = getTemplateInfo(TemplateSetServiceInterface::IMPORT_INSTORE_NAME, $data['import_type']);
                // 读取excel数据
                $importData = readExcelByHeader($file, $configTemplate['original_data'],
                    $ruleMap[$data['import_type']]['rule'],
                    $ruleMap[$data['import_type']]['msg']);
            }
            //验证传入数据是否正确
            $importList = InStoreService::import(['task_id'=>$id,'import_data'=>$importData]);
            $importListCollect = collect($importList['data']);
            $totalInfo = [
                'import_type' => $data['import_type'],
                'total_num' => $importList['match_info']['totalNum'] ?? 0,
                //'total_barcode' => $importListCollect->count('barcode'),
                'shelf_num' => $importListCollect->unique('shelf_code')->count(),
                'match_num' => $importList['match_info']['matchNum'] ?? 0,
                'limit_diff_num' => $importList['match_info']['limit_diff_num'] ?? 0,
                'total_diff_num' => $importList['match_info']['total_diff_num'] ?? 0,
                'current_diff_num' => $importList['match_info']['current_diff_num'] ?? 0,
                'over_rate' => $importList['match_info']['over_rate'] ?? 0,
                //'match_rate' => bcmul(bcdiv(strval($importList['match_info']['matchNum'] ?? 0) ,strval($importList['match_info']['totalNum'] ?? 0),2),'100')  ,
            ];

//            if($totalInfo['total_num'] > 0){
//                $totalInfo['match_rate'] = bcmul(bcdiv(strval($totalInfo['match_num']) ,strval($totalInfo['total_num'] ?? 0),2),'100')  ;
//            }else{
//                $totalInfo['match_rate'] = 0;
//            }

            $listCacheKey = CachePre::getKey(CachePre::INSTORE_LIST_DATA,$this->getUserId(),$id) ;

            usort($importList['data'],function ($a,$b){
                return $b['diff_reason_type'] - $a['diff_reason_type'];
            });
            HashService::del($listCacheKey);
            HashService::saveDataToHash($listCacheKey, $importList['data'], 60 * 30,true);
            redis()->hMSet($totalCacheKey,$totalInfo);
            redis()->expire($totalCacheKey,60 * 30);

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $totalInfo);
        }


        $data['info'] = $info;
        $data['import_info'] = redis()->hGetAll($totalCacheKey);
        if($info['type'] == 3){
            $data['tpl_list'] = TemplateSetService::tplList(TemplateSetService::TYPE_ALLOT_INSTORE);
        }else{
            $data['tpl_list'] = TemplateSetService::tplList(TemplateSetService::TYPE_INSTORE);
        }
        logger()->debug('import_data',['data'=>$data]);
        return $this->show('instore/import', $data);
    }


    /**
     * @RequestMapping(path="confirm", methods="get,post")
     * @return mixed
     */
    public function confirm(RequestInterface $request){
        // 存在则返回，不存在则返回默认值 null
        $id = intval($request->input('id'));
        $info = InStoreService::info($id);
        $listCacheKey = CachePre::getKey(CachePre::INSTORE_LIST_DATA,$this->getUserId(),$id) ;
        $totalCacheKey = CachePre::getKey(CachePre::INSTORE_TOTAL_DATA,$this->getUserId(),$id) ;
        if (empty($info)){
            throw new BusinessException('任务单不存在');
        }
        $importInfo = redis()->hGetAll($totalCacheKey);
        if (empty($importInfo)){
            throw new BusinessException('任务单临时数据丢失，请重新上传');
        }
        $userInfo = $this->getUserInfo();
        $confirmInfo = [
            'serial_no' => $info['arrival_no'],
            'import_type' => $importInfo['import_type'],
            'task_id' => $id,
            'admin_id' => $userInfo['uid'],
            'admin_name' => $userInfo['nickname'],
            'list_cache_key' => $listCacheKey,
        ];
        $result = InStoreService::confirm($confirmInfo);
        if($result){
            redis()->del($listCacheKey,$totalCacheKey);
            $data = $request->all();
            $data['op_id'] = $id;
            $data['req_router_name'] = '货品入库';
            $data['remark'] = '入库数量：'.($info['final_num'] - $result['final_num']);
            $data['snow_id'] = strval($request->getAttribute('snow_id'));
            wlog($data['snow_id'],$result);
        }

        return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
    }

    /**
     * @RequestMapping(path="finish", methods="get,post")
     * @return mixed
     */
    public function finish(RequestInterface $request){
        // 存在则返回，不存在则返回默认值 null
        $id = intval($request->input('id'));
        $info = InStoreService::info($id);
        if (empty($info)){
            throw new BusinessException('任务单不存在');
        }
        $result = InStoreService::finish($id);
        $data['op_id'] = $id;
        $data['req_router_name'] = '完成入库';
        $data['snow_id'] = strval($request->getAttribute('snow_id'));
        wlog($data['snow_id'],$result);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
    }

    /**
     * 获取临时存储调拨商品数据
     * @RequestMapping(path="templist", methods="get,post")
     * @return mixed
     */
    public function templist(RequestInterface $request)
    {
        $id = intval($request->input('id'));
        $page = intval($request->input('page',1));
        $limit = intval($request->input('limit',$this->pageLimit()));
        $ret = HashService::getDataListFromHash(CachePre::getKey(CachePre::INSTORE_LIST_DATA,$this->getUserId(),$id), $page, $limit);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $ret['data'], ['count' => $ret['total'], 'limit' => $limit]);
    }

    /**
     * @RequestMapping(path="exportTempList", methods="get,post")
     * @return mixed
     */
    public function exportTempList(RequestInterface $request){
        $id = intval($request->input('id'));
        $list = HashService::getDataAllFromHash(CachePre::getKey(CachePre::INSTORE_LIST_DATA,$this->getUserId(),$id));
        if($list){
            try {
                $url = exportToExcel(config('file_header.instore_temp_export'),$list,'入库详情');
            }catch (\Exception $e){
                throw new BusinessException('导出失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
            }
        }else{
            throw new BusinessException('无数据，导出失败！');
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
    }

    /**
     * 数据验证
     * @param array $data
     * @return array
     */
    protected function validate(array $data,$secne='default'){

        $message = [
            'brand_ids.string' => '品牌参数有误!',
            'allot_no.string' => '调拨单号有误!',
            'allot_diff_no.string' => '调拨差异单号有误!',
            'warehouse_ids.string' => '仓库参数有误!',
            'status.numeric' => '状态参数有误!',
            'wait_finish.numeric' => '入库状态参数有误!',
            'in_stock_no.string' => '批次号有误!',
            'purchase_no.string' => '采购单号有误!',
            'serial_no.required' => '来源单号不能为空!',
            'search_type.numeric' => '查询类型有误!',
            'in_store_id.required' => '任务id不能为空!',
            'in_w_id.string' => '调入仓id有误!',
            'out_w_id.string' => '调出仓id有误!',
            'search_value.string' => '查询值有误!',
            'date_range.string' => '时间格式有误!',
            'import_type.in' => '导入方式错误!',
            'diff_type.in' => '差异类型错误!',
            'search_code.string' => '货品编码类型错误!',
            'import_type.required' => '请选择导入方式!',
            'batch_type.in' => '入库方式错误!',
            'batch_type.required' => '请选择入库方式!',
            'type.required' => '入库类型不能为空!',
            'type.in' => '入库类型错误!',
            'arrival_no.string' => '任务单号格式有误',
            'arrival_box_no.string' => '箱号格式有误',
            'third_party_no.string' => '三方发货单号格式有误',
        ];
        $rules = [
            'brand_ids' => 'string',
            'allot_no' => 'string',
            'allot_diff_no' => 'string',
            'in_store_id' => 'required|numeric',
            'in_w_id' => 'string',
            'out_w_id' => 'string',
            'serial_no' => 'required|string',
            'import_type' => [
                'required',
                Rule::in([1,2])
            ],
            'batch_type' => [
                'required',
                Rule::in([1,2,3])
            ],
            'diff_type' => [
                Rule::in([1,2,3])
            ],
            'type' => [
                Rule::in(array_keys(self::TYPE_MAP))
            ],
            'file' => 'required|file',
            'warehouse_ids' => 'string',
            'supplier_ids' => 'string',
            'status' => 'numeric',
            'wait_finish' => 'numeric',
            'in_stock_no' => 'string',
            'search_code' => 'string',
            'purchase_no' => 'string',
            'search_type' => 'numeric',
            'search_value' => 'string',
            'date_range' => 'string',
            'order_no' => 'string',
            'arrival_no' => 'string',
            'arrival_box_no' => 'string',
            'third_party_no' => 'string',
            'is_transfer' => [
                Rule::in([0,1])
            ],
        ];

        $secnes = [
            'search' => ['is_transfer','warehouse_ids','type','supplier_ids','wait_finish','brand_ids','status','in_stock_no','purchase_no','search_type','order_no','search_value','date_range', 'arrival_no', 'arrival_box_no', 'arrival_no', 'third_party_no'],
            'search_rev_allot' => ['in_w_id','out_w_id','allot_no','allot_diff_no','search_type','order_no','search_value','date_range'],
            'detail_search' => ['in_store_id','search_type','search_value'],
            'diff_search' => ['in_store_id','diff_type','search_code'],
            'import' => ['in_store_id','import_type'],
            'change_import' => ['in_store_id','batch_type'],
            'add' => ['serial_no','batch_type','type'],
        ];
        $useRule = [];
        if(isset($secnes[$secne])){
            foreach ($secnes[$secne] as $item){
                $useRule[$item] = $rules[$item];
            }
        }else{
            throw new BusinessException('验证场景值有误');
        }

        $validator = validate()->make(
            $data,
            $useRule,
            $message
        );

        return $validator->validate(); //验证数据有效性
    }
}