<?php

declare(strict_types=1);

namespace App\JsonRpc;


Interface AllotDiffServiceInterface
{
    /**
     * 调拨差异单列表
     * @param int $page
     * @param int $limit
     * @param array $where [
     *      "diff_id":"调拨差异id",
     *      "sku_id":"sku",
     *      "barcode":"条码",
     *      "unique_code":"店内码",
     * ]
     * @param array|string[] $field
     * @return array
     */
    public function wmsList(int $page = 1, int $limit = 10, array $where = [], array $field = ['*']);

    /**
     * 调拨差异单列表
     * @param int $page
     * @param int $limit
     * @param array $where [
     *      "diff_id":"调拨差异id",
     *      "sku_id":"sku",
     *      "barcode":"条码",
     *      "unique_code":"店内码",
     * ]
     * @param array|string[] $field
     * @return array
     */
    public function resultList(int $page = 1, int $limit = 10, array $where = [], array $field = ['*']);

    /**
     * 根据调拨单号获取差异详请
     * @param string $allotNo
     * @return array
     */
    public function infoByAllotNo(string $allotNo);

    /**
     * 根据调拨单号获取差异详请
     * @param string $params [
     *   "handle_num" => 处理数量
     *   "diff_reason" => 差异原因
     *   "liabler" => 归属方
     *   "detail_id" => 差异明细id
     * ]
     * @return array
     */
    public function handleDiff(array $params);

    /**
     * 取消处理结果
     * @param string $id
     * @return array
     */
    public function cancelResult(int $id);

    /**
     * 完成调拨差异处理
     * @param string $id
     * @return array
     */
    public function over(int $id);

    /**
     * 根据调拨单号获取差异详请
     * @param string $params [
     *   "admin_name" => 操作人
     *   "admin_id" => 操作人id
     *   "diff_id" => 差异明细id
     * ]
     * @return array
     */
    public function finishDecision(array $params);
}
