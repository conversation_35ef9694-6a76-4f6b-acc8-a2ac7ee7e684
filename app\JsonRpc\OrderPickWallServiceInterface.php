<?php
declare(strict_types=1);

namespace App\JsonRpc;

interface OrderPickWallServiceInterface
{
    /**
     * 查询店内码所匹配的订单。并按照时间倒叙取最后一条订单数据
     * @param array $where
     * @return mixed
     */
    public function checkOrderUniqueCode (array $where);

    /**
     * 查询订单下所有的unique_code
     * @param array $where
     * @return mixed
     */
    public function getOrderUniqueCodes (array $where);
}