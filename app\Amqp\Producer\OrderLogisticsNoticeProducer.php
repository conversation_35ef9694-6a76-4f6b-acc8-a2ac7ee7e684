<?php

declare(strict_types=1);

namespace App\Amqp\Producer;

use Hyperf\Amqp\Annotation\Producer;
use Hyperf\Amqp\Message\ProducerMessage;

/**
 * 线上或代理订单物流通知(队列)
 * @Producer(exchange="bigoffs.direct", routingKey="online_logistics_notice")
 */
class OrderLogisticsNoticeProducer extends ProducerMessage
{
    /**
     * OrderLogisticsNoticeProducer constructor.
     * @param array $params = [
     *      "msg_id" => 消息ID,
     *      "source" => 类型【2小程序，5代理】,
     *      "logistics_no" => 物流单号,
     *      "parent_serial_no" => 内部订单号,
     *      "order_detail_id" => 订单明细 ID ,多个用竖线分隔开
     *      "sku_id" => sku_id 多个用竖线分隔开 【为代理订单时必传】
     *      "status" => 状态【1成功，2失败】
     *      "msg" => 原因描述
     * ]
     */
    public function __construct(array $params)
    {
        $this->payload = $params;
    }
}
