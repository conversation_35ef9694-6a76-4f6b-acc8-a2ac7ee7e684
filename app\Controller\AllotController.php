<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Controller;

use App\Constants\CachePre;
use App\Constants\ResponseCode;
use App\Exception\BusinessException;
use App\JsonRpc\TemplateSetServiceInterface;
use App\Library\Facades\AdminService;
use App\Library\Facades\AllotDiffService;
use App\Library\Facades\AllotService;
use App\Library\Facades\GoodsPackingService;
use App\Library\Facades\HashService;
use App\Library\Facades\InStoreService;
use App\Library\Facades\LogisticsInfoService;
use App\Library\Facades\LogisticsService;
use App\Library\Facades\OutStoreService;
use App\Library\Facades\SyncTaskService;
use App\Library\Facades\TaskService;
use App\Library\Facades\TemplateSetService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Validation\Rule;

/**
 * @Controller
 */
class AllotController extends AbstractController
{
    /**
     * @Inject()
     * @var \App\JsonRpc\WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject()
     * @var \App\JsonRpc\PickOrderServiceInterface
     */
    private $PickOrderService;

    /**
     * @Inject()
     * @var \App\JsonRpc\BrandServiceInterface
     */
    private $BrandService;

    /**
     * @Inject()
     * @var \App\JsonRpc\CategoryServiceInterface
     */
    private $CategoryService;

    /**
     * @Inject()
     * @var \App\Service\HashService
     */
    private $HashService;

    /**
     * @Inject()
     * @var \App\JsonRpc\AdminServiceInterface
     */
    private $AdminService;

    /**
     * @Inject()
     * @var \App\JsonRpc\OutStoreServiceInterface
     */
    private $OutStoreService;

    //状态 0=待出库，1=出库中，2=已完成，-1=作废
    //状态 1=待审核，2=待接单，3=驳回，4=待出库，5=待发货，6=待签收，7=待入库，8=已完成，9=作废
    private $status = [
        '2' => '待接单',
        '4' => '待出库',
//        '5' => '待发货',
        '6' => '待签收',
        '7' => '待入库',
        '8' => '已完成',
        '9' => '作废',
    ];

    private $search_type = [
        '1' => '店内码查找',
        '2' => '货号查找',
        '3' => '条形码查询',
        '4' => 'SPU查询',
        '5' => 'SKU查询',
    ];

    private $out_type = [
        '1' => '店内码级',
        '2' => '条形码级'
    ];

    private $searchTypeMap = [
        '1' => 'unique_code',
        '2' => 'spu_no',
        '3' => 'barcode',
        '4' => 'spu_id',
        '5' => 'sku_id',
    ];
    //单据类型【21差异调整单-出，22退返单，23调拨-出，24盘点-亏，25销售(订单)，26配送丢失，27配送报损】
    private $taskTypeMap = [
        '25' => '销售出库',
        '23' => '调拨出库',
        '28' => '拍照出库',
        '27' => '报损出库',
        '22' => '退返单出库',
    ];

    /**
     * @RequestMapping(path="/allot/import", methods="get,post")
     * @return mixed
     */
    public function import(RequestInterface $request){
        $data['title'] = "店内码批量入库";
        $data['tpl_list'] = TemplateSetService::tplList(TemplateSetService::TYPE_BATCH_ALLOT_IN);
        return $this->show('allot/import', $data);
    }

    /**
     * @RequestMapping(path="/allot/batchIn", methods="post")
     * @return mixed
     */
    public function batchIn(RequestInterface $request){
        $file = $request->file('file');
        $configTemplate = getTemplateInfo(TemplateSetService::TYPE_BATCH_ALLOT_IN, 1);
        // 读取excel数据
        $importData = readExcelByHeader($file, $configTemplate['original_data']);
        $userInfo = $this->getUserInfo();
        $uniqueName = md5(time() . mt_rand(1, 10000));
        $up_res = upload($uniqueName . '.' .$file->getExtension(), $file->getPathname(), 4);
        $cacheKey = CachePre::getKey(sprintf(CachePre::ALLOT_BATCH_IN_DATA,$userInfo['uid']));
        redis()->set($cacheKey,gzencode(json_encode($importData,JSON_UNESCAPED_UNICODE)),CachePre::CACHE_ONE_HOUR);
        $params = [];
        $params['service_name'] = 'allot:batchIn';
        $params['admin_id'] = $userInfo['uid'];
        $params['admin_name'] = $userInfo['nickname'];
        $params['task_name'] = '调拨批量入库';
        $params['sys_type'] = 'wms';
        $params['params'] = json_encode([[
            'admin_id'=>$userInfo['uid'],
            'admin_name'=>$userInfo['nickname'],
            'upload_file'=>$up_res,
            'cache_key'=>$cacheKey,
        ]]);
        $taskInfo = SyncTaskService::add($params,60);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $taskInfo);

    }

    /**
     * @RequestMapping(path="/allot/batchSign", methods="get")
     * @return mixed
     */
    public function batchSign(RequestInterface $request){
        $data['title'] = "店内码批量签收";
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        //获取仓库信息
        $warehouse_list = $this->WarehouseService->getWarehouses(['ids'=>$wIds],['id','name']);
        $data['warehouse_list'] = $warehouse_list;
        $tplList = TemplateSetService::tplList(TemplateSetService::TYPE_BATCH_ALLOT_SIGN);
        $data['tplInfo'] = $tplList[1];
        return $this->show('allot/batchSign', $data);
    }


    /**
     * @RequestMapping(path="/allot/boxReg", methods="get")
     * @return mixed
     */
    public function boxReg(RequestInterface $request){
        $data['title'] = "到货登记";
        return $this->show('allot/boxReg', $data);
    }

    /**
     * @RequestMapping(path="/allot/checkSign", methods="post")
     * @return mixed
     */
    public function checkSign(RequestInterface $request){
        $file = $request->file('file');
        $configTemplate = getTemplateInfo(TemplateSetService::TYPE_BATCH_ALLOT_IN, 1);
        // 读取excel数据
        $importData = readExcelByHeader($file, $configTemplate['original_data']);
        $userInfo = $this->getUserInfo();
        $uniqueName = md5(time() . mt_rand(1, 10000));
        $up_res = upload($uniqueName . '.' .$file->getExtension(), $file->getPathname(), 4);
        $cacheKey = CachePre::getKey(sprintf(CachePre::ALLOT_BATCH_IN_DATA,$userInfo['uid']));
        redis()->set($cacheKey,gzencode(json_encode($importData,JSON_UNESCAPED_UNICODE)),CachePre::CACHE_ONE_HOUR);
        $params = [];
        $params['service_name'] = 'allot:batchIn';
        $params['admin_id'] = $userInfo['uid'];
        $params['admin_name'] = $userInfo['nickname'];
        $params['task_name'] = '调拨批量入库';
        $params['sys_type'] = 'wms';
        $params['params'] = json_encode([[
            'admin_id'=>$userInfo['uid'],
            'admin_name'=>$userInfo['nickname'],
            'upload_file'=>$up_res,
            'cache_key'=>$cacheKey,
        ]]);
        $taskInfo = SyncTaskService::add($params,60);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $taskInfo);

    }

    /**
     * @RequestMapping(path="/allot/send", methods="get,post")
     * @return mixed
     */
    public function send(RequestInterface $request){
        $params = $this->validate($request->all(),'send');
        $result = AllotService::info($params['id']);
        if(!$result['info']){
            throw new BusinessException('调拨任务不存在');
        }
        $info = $result['info'];
        $userInfo = $this->getUserInfo();

        try {
            $params['admin_id'] = $userInfo['uid'];
            $params['box_ids'] = explode(",", $params['box_ids']);
            $params['admin_name'] = $userInfo['nickname'];
            AllotService::allotSend($params);;
        } catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
        $data = [];
        $data['op_id'] = $info['id'];
        $data['req_router_name'] = '调拨发货';
        $data['snow_id'] = strval($request->getAttribute('snow_id'));
        wlog($data['snow_id'],$data);
        return $this->returnApi(ResponseCode::SUCCESS, '发货成功');
    }

    /**
     * @RequestMapping(path="/allot/finishSend", methods="get,post")
     * @return mixed
     */
    public function finishSend(RequestInterface $request){
        $params = $this->validate($request->all(),'finishSend');
        $result = AllotService::info($params['id']);
        if(!$result['info']){
            throw new BusinessException('调拨任务不存在');
        }
        $info = $result['info'];
        $userInfo = $this->getUserInfo();

        try {
            $params['admin_id'] = $userInfo['uid'];
            $params['admin_name'] = $userInfo['nickname'];
            AllotService::finishSend($params);;
        } catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
        $data = [];
        $data['op_id'] = $info['id'];
        $data['req_router_name'] = '调拨完成发货';
        $data['snow_id'] = strval($request->getAttribute('snow_id'));
        wlog($data['snow_id'],$data);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
    }

    /**
     * @RequestMapping(path="/allot/detail", methods="get,post")
     * @return mixed
     */
    public function detail(RequestInterface $request){
        $data['title'] = "调拨明细";
        // 存在则返回，不存在则返回默认值 null
        $id = $request->input('id');
        $result = AllotService::info($id);

        if (empty($result)){
            $data['error_info'] = "任务单不存在";
        }else{
            $data['info'] =  $result['info'];
        }
        $wIds = AdminService::organizeWareHouseData($this->getUserId());

        //调拨发货后生成调拨入库单 6=待签收，7=待入库
        if($data['info']['status'] != 2){
            //返回出库任务信息
            $outInfo = OutStoreService::getValidByOrderNo($data['info']['serial_no']);
        }

        //调拨发货后生成调拨入库单 6=待签收，7=待入库
        if(in_array($data['info']['status'],[7,8])){
            //返回出库任务信息
            $diffInfo = AllotDiffService::infoByAllotNo($data['info']['serial_no']);
        }

        $logInfo = getLog(['model_name'=>'allot','op_id'=>$id]);
        $logistics_list = LogisticsService::getLogisticsConfigByWId($data['info']['out_w_id']);
        $logistics = LogisticsInfoService::getLogisticsInfo(['rela_no' => $data['info']['serial_no']]);
        $data['log_list'] = $logInfo['data'] ?? [];
        $data['logistics_list'] = $logistics_list ?? [];
        $data['logistics'] = $logistics ?? [];
        $data['diffInfo'] = $diffInfo ?? [];
        $data['outInfo'] = $outInfo ?? [];
        $data['wIds'] = $wIds ?? [];
        $data['tpl_list'] = TemplateSetService::tplList(TemplateSetService::TYPE_ALLOT_PACK_SIGN);
        logger()->debug('detail_data',['data'=>$data]);
        return $this->show('allot/detail', $data);
    }


    /**
     * @RequestMapping(path="/allot/stat", methods="get,post")
     * @return mixed
     */
    public function stat(RequestInterface $request){
        // 存在则返回，不存在则返回默认值 null
        $id = $request->input('id');
        $result = AllotService::stat($id);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $result);
    }

    /**
     * @RequestMapping(path="/allot/over", methods="get,post")
     * @return mixed
     */
    public function over(RequestInterface $request){
        // 存在则返回，不存在则返回默认值 null
        $id = $request->input('id');
        $result = AllotService::over($id);
        $data = [];
        $data['op_id'] = $id;
        $data['req_router_name'] = '完成调拨';
        $data['snow_id'] = strval($request->getAttribute('snow_id'));
        wlog($data['snow_id'],$data);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $result);
    }

    /**
     * @RequestMapping(path="/allot/cancel", methods="get,post")
     * @return mixed
     */
    public function cancel(RequestInterface $request){
        // 存在则返回，不存在则返回默认值 null
        $id = $request->input('id');
        if (!$id) {
            throw new BusinessException('任务id不能为空');
        }
        $result = AllotService::cancel($id,'wms作废调拨');
        $data = [];
        $data['op_id'] = $id;
        $data['req_router_name'] = '作废调拨';
        $data['snow_id'] = strval($request->getAttribute('snow_id'));
        wlog($data['snow_id'],$data);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $result);
    }

    /**
     * @RequestMapping(path="/allot/handle_diff", methods="get,post")
     * @return mixed
     */
    public function handleDiff(RequestInterface $request){
        $params = $this->validate($request->all(),'handle_diff');
        if(in_array($params['diff_reason'],[1]) && $params['liabler'] == 2){
            throw new BusinessException('配送漏发归属方必须为调出方!');
        }
        if(in_array($params['diff_reason'],[4]) && $params['liabler'] == 1){
            throw new BusinessException('签收漏签归属方必须为调入方!');
        }
        $result = AllotDiffService::handleDiff($params);
        $data = [];
        $data['op_id'] = $params['diff_id'];
        $data['model_name'] = 'allot_diff';
        $data['req_router_name'] = '处理调拨差异';
        $data['snow_id'] = strval($request->getAttribute('snow_id'));
        wlog($data['snow_id'],$data);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $result);
    }

    /**
     * @RequestMapping(path="/allot/cancel_result", methods="get,post")
     * @return mixed
     */
    public function cancelResult(RequestInterface $request){
        $params = $this->validate($request->all(),'cancel_result');
        $id = (int) ($params['result_id'] ?? 0);
        $result = AllotDiffService::cancelResult($id);
        $data = [];
        $data['op_id'] = $params['diff_id'];
        $data['model_name'] = 'allot_diff';
        $data['req_router_name'] = '取消调拨差异';
        $data['snow_id'] = strval($request->getAttribute('snow_id'));
        wlog($data['snow_id'],$data);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $result);
    }

    /**
     * 完成调拨差异
     * @RequestMapping(path="/allot/over_diff", methods="get,post")
     * @return mixed
     */
    public function overDiff(RequestInterface $request){
        $params = $this->validate($request->all(),'over_diff');
        $userInfo = $this->getUserInfo();


        $id = (int) ($params['diff_id'] ?? 0);
        $allot_id = (int) ($params['allot_id'] ?? 0);
        $params['admin_id'] = $userInfo['uid'];
        $params['admin_name'] = $userInfo['nickname'];
        $params['diff_id'] = $id;
        $result = AllotDiffService::finishDecision($params);
        $data = [];
        $data['op_id'] = $allot_id;
        $data['req_router_name'] = '完成调拨差异处理';
        $data['snow_id'] = strval($request->getAttribute('snow_id'));
        wlog($data['snow_id'],$data);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $result);
    }

    /**
     * 详情页列表
     * @RequestMapping(path="/allot/detail_list", methods="get,post")
     * @return mixed
     */
    public function detailList(RequestInterface $request){
        $where = $this->validate($request->all(),'detail_search');
        $page = intval($request->input('page',1));
        $limit = intval($request->input('limit',30));
        $id = $request->input('id');
        $result = AllotService::info($id);
        if(!$result['info']){
            throw new BusinessException('调拨任务不存在');
        }
        $info = $result['info'];
        if ($info['status'] == 2){//待接单状态，直接显示调拨单数据
            $list = AllotService::detailList((int) $id,$page ,$limit);
        }else{
            $outTaskInfo = OutStoreService::getOutStore(['order_no'=>$info['serial_no'],'last_data'=>1]);
            $where['out_store_id']  = $outTaskInfo['id'] ?? 0;
            if($info['status'] == 9 && empty($where['out_store_id'])){
                unset($where['id']) ;
                $list = AllotService::detailList((int) $id,$page ,$limit);
            }else{
                unset($where['id']) ;
                $list = OutStoreService::detailGroupList($where,$page ,$limit);
            }

        }

        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $limit]);
    }

    /**
     * @RequestMapping(path="/allot/warn_list", methods="get,post")
     * @return mixed
     */
    public function warnList(RequestInterface $request)
    {
        $data['title'] = '差异预警';

        if ($this->isAjax()) {
            $where = $this->validate($request->all(),'warn_search');
            //入库
            if (!empty($where['in_w_ids'])){
                $where['in_w_id'] = explode(',',$where['in_w_ids']);
                unset($where['in_w_ids']);
            }
            //出库
            if (!empty($where['out_w_ids'])){
                $where['out_w_id'] = explode(',',$where['out_w_ids']);
                unset($where['out_w_ids']);
            }
            $page = intval($request->input('page',1));
            $limit = intval($request->input('limit',30));
            $where['end_warn_date'] = date('Y-m-d');
            $list = AllotService::warnList($page, $limit, $where);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $limit]);
        }
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        //获取仓库信息
        $warehouse_list = $this->WarehouseService->getWarehouses([],['id','name']);
        $ownList = [];
        foreach ($warehouse_list as $item){
            if(in_array($item['id'],$wIds)){
                $ownList[] = $item;
            }
        }

        $data = [
            'in_list' => $ownList,
            'out_list' => $warehouse_list
        ];

        return $this->show('allot/warn_list', $data);
    }

    /**
     * 详情页列表
     * @RequestMapping(path="/allot/export_warn_detail_list", methods="get,post")
     * @return mixed
     */
    public function exportWarnDetailList(RequestInterface $request){
        $where = $this->validate($request->all(),'warn_search');
        //入库
        if (!empty($where['in_w_ids'])){
            $where['in_w_id'] = explode(',',$where['in_w_ids']);
            unset($where['in_w_ids']);
        }
        //出库
        if (!empty($where['out_w_ids'])){
            $where['out_w_id'] = explode(',',$where['out_w_ids']);
            unset($where['out_w_ids']);
        }
        $where['end_warn_date'] = date('Y-m-d');
        $url = AllotService::exportWarnDetailList( $where);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
    }


    private function searchParams($data,$wIdOptions){
        $where = $this->validate($data,'search');
        logger()->debug('list_where:start',[$where]);
        $where['is_wms'] = 1;

        if (!empty($where['date_range'])){
            $dateRangeArray = explode(' - ',$where['date_range']);
            $where['start_time'] = $dateRangeArray[0];
            $where['end_time'] = date('Y-m-d 23:59:59',strtotime($dateRangeArray[1]));
        }
        if (!empty($where['send_date_range'])){
            $dateRangeArray = explode(' - ',$where['send_date_range']);
            $where['last_send_start_time'] = $dateRangeArray[0];
            $where['last_send_end_time'] = date('Y-m-d 23:59:59',strtotime($dateRangeArray[1]));
        }
        if (!empty($where['show_date_range'])){
            $dateRangeArray = explode(' - ',$where['show_date_range']);
            $where['show_start_time'] = $dateRangeArray[0];
            $where['show_end_time'] = date('Y-m-d 23:59:59',strtotime($dateRangeArray[1]));
        }

        if (!empty($where['sign_time'])){
            $dateRangeArray = explode(' - ',$where['sign_time']);
            $where['sign_start_time'] = $dateRangeArray[0];
            $where['sign_end_time'] = date('Y-m-d 23:59:59',strtotime($dateRangeArray[1]));
        }

        if (!empty($where['search_type']) && array_key_exists($where['search_type'],$this->search_type)){
            $searchKey = $this->searchTypeMap[$where['search_type']];
            $where[$searchKey] = $where['search_value'];
        }
        //入库
        if (!empty($where['in_w_ids'])){
            $where['in_w_id'] = explode(',',$where['in_w_ids']);
            unset($where['in_w_ids']);
        }
        //出库
        if (!empty($where['out_w_ids'])){
            $where['out_w_id'] = explode(',',$where['out_w_ids']);
            unset($where['out_w_ids']);
        }
        if(!empty($where['out_w_id']) && !empty($where['in_w_id'])){
            $where['warehouse_search_type'] = 'and';
        }
        if(empty($where['out_w_id']) && empty($where['in_w_id'])){
            $where['warehouse_search_type'] = 'or';
            $where['in_w_id'] = $wIdOptions['w_ids'];
            $where['out_w_id'] = $wIdOptions['w_ids'];
        }

        if (!empty($where['export_num'])){
            $where['export'] = 1;
            unset($where['export_num']);
        }

        if (!empty($where['serial_no'])){
            $serialNos = explode("\n",$where['serial_no']);
            $where['serial_no'] = [];
            foreach ($serialNos as $item){
                $serial_no = trim($item);
                if(!empty($serial_no)){
                    $where['serial_no'][] = $serial_no;
                }
            }
            if(empty($where['serial_no'])){
                unset($where['serial_no']);
            }
        }else{
            unset($where['serial_no']);
        }


        if (!empty($where['transfer_batch_no'])){
            $serialNos = explode("\n",$where['transfer_batch_no']);
            $where['transfer_batch_no'] = [];
            foreach ($serialNos as $item){
                $serial_no = trim($item);
                if(!empty($serial_no)){
                    $where['transfer_batch_no'][] = $serial_no;
                }
            }
            if(empty($where['transfer_batch_no'])){
                unset($where['transfer_batch_no']);
            }
        }else{
            unset($where['transfer_batch_no']);
        }

        if (!empty($where['exception_num'])){
            $where['exception'] = 1;
            unset($where['exception_num']);
        }

        if (isset($where['transfer']) && $where['transfer'] != "") {
            $where['is_not_transfer'] = ($where['transfer'] == 0) ? 1 : 0;
        }

        unset($where['transfer']);
        unset($where['date_range']);
        unset($where['send_date_range']);
        unset($where['search_type']);
        unset($where['search_value']);
        unset($where['out_w_ids']);
        unset($where['in_w_ids']);
        logger()->debug('list_where:end',$where);
        return $where;
    }

    /**
     * @RequestMapping(path="/allot/list", methods="get,post")
     * @return mixed
     */
    public function list(RequestInterface $request)
    {
        $data['title'] = '调拨列表';
        $wIdOptions = $this->getWidOptions();

        $w_id = $request->input('w_id','');
        $start_time = $request->input('start_time');
        $end_time = $request->input('end_time');

        if ($this->isAjax()) {
            $page = intval($request->input('page',1));
            $limit = intval($request->input('limit',30));
            $where = $this->searchParams($request->all(),$wIdOptions);
            $list = AllotService::list($page, $limit, $where);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $limit]);
        }

        $time = '';
        if(!empty($start_time) && !empty($end_time)) $time = $start_time . ' - ' . $end_time;
        $data = [
            'w_ids' => explode(",", $w_id),
            'time' => $time,
            'in_list' => $wIdOptions['in_list'],
            'out_list' => $wIdOptions['out_list'],
            'status_list' => $this->status,
            'search_type' => $this->search_type,
            'task_type' => $this->taskTypeMap
        ];

        return $this->show('allot/list', $data);
    }

    /**
     * @RequestMapping(path="/allot/diff_list", methods="get,post")
     * @return mixed
     */
    public function diffList(RequestInterface $request)
    {
        $where = $this->validate($request->all(),'diff_list');
        logger()->debug('list_where:start',$where);
        $page = intval($request->input('page',1));
        $limit = intval($request->input('limit',30));
        $isExport = intval($request->input('is_export',0));
        logger()->debug('list_where:end',$where);
        if (!empty($where['search_type']) && array_key_exists($where['search_type'],$this->search_type)){
            $searchKey = $this->searchTypeMap[$where['search_type']];
            $where[$searchKey] = $where['search_value'];
        }
        unset($where['search_type']);
        unset($where['search_value']);
        if($isExport){
            $list = AllotDiffService::wmsList(0, 0, $where);
            if($list['data']){
                try {
                    $url = exportToExcel(config('file_header.allot_diff'),$list['data'],'调拨详情');
                }catch (\Exception $e){
                    throw new BusinessException('导出失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
                }
            }else{
                throw new BusinessException('无数据，导出失败！');
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
        }else{
            $list = AllotDiffService::wmsList($page, $limit, $where);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $limit]);
        }

    }

    /**
     * @RequestMapping(path="/allot/reg_list", methods="get,post")
     * @return mixed
     */
    public function regList(RequestInterface $request)
    {
        if ($this->isAjax()){
            $where = $this->validate($request->all(),'reg_list');
            $page = intval($request->input('page',1));
            $limit = intval($request->input('limit',30));
            $isExport = intval($request->input('is_export',0));
            if (!empty($where['reg_range_time'])){
                $dateRangeArray = explode(' - ',$where['reg_range_time']);
                $where['reg_start_time'] = $dateRangeArray[0];
                $where['reg_end_time'] = date('Y-m-d 23:59:59',strtotime($dateRangeArray[1]));
            }
            logger()->debug('list_where:end',$where);

            if (isset($where['unique_code']) && count($where['unique_code']) > 3000){
                $where['unique_code'] = array_chunk($where['unique_code'],3000)[0];
            }
            if (isset($where['box_no']) && count($where['box_no']) > 3000){
                $where['box_no'] = array_chunk($where['box_no'],3000)[0];
            }
            if (isset($where['source_no']) && count($where['source_no']) > 3000){
                $where['source_no'] = array_chunk($where['source_no'],3000)[0];
            }
            if($isExport){
                $list = GoodsPackingService::regList(0, 0, $where);
                if($list['data']){
                    try {
                        $url = exportToExcel(config('file_header.allot_reg_list'),$list['data'],'调拨详情');
                    }catch (\Exception $e){
                        throw new BusinessException('导出失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
                    }
                }else{
                    throw new BusinessException('无数据，导出失败！');
                }
                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
            }else{
                $list = GoodsPackingService::regList($page, $limit, $where);
                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $limit]);
            }
        }
        //获取仓库信息
        $warehouse_list = $this->WarehouseService->getWarehouses([],['id','name']);
        $adminList = GoodsPackingService::getRegAdminList();
        $data = [
            'in_list' => $warehouse_list,
            'out_list' => $warehouse_list,
            'admin_list' => $adminList,
        ];

        return $this->show('allot/regList', $data);

    }

    /**
     * @RequestMapping(path="/allot/diff_result", methods="get,post")
     * @return mixed
     */
    public function diffResult(RequestInterface $request)
    {
        $where = $this->validate($request->all(),'diff_list');
        logger()->debug('list_where:start',$where);
        $page = intval($request->input('page',1));
        $limit = intval($request->input('limit',30));
        $isExport = intval($request->input('is_export',0));
        if (!empty($where['search_type']) && array_key_exists($where['search_type'],$this->search_type)){
            $searchKey = $this->searchTypeMap[$where['search_type']];
            $where[$searchKey] = $where['search_value'];
        }
        unset($where['search_type']);
        unset($where['search_value']);
        logger()->debug('list_where:end',$where);
        if($isExport){
            $list = AllotDiffService::resultList(0, 0, $where);
            if($list['data']){
                try {
                    $url = exportToExcel(config('file_header.allot_diff_result'),$list['data'],'调拨详情');
                }catch (\Exception $e){
                    throw new BusinessException('导出失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
                }
            }else{
                throw new BusinessException('无数据，导出失败！');
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
        }else{
            $list = AllotDiffService::resultList($page, $limit, $where);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $limit]);
        }

    }


    /**
     * @RequestMapping(path="/allot/export", methods="get,post")
     * @return mixed
     */
    public function export(RequestInterface $request)
    {
        $wIdOptions = $this->getWidOptions();
        $where = $this->searchParams($request->all(),$wIdOptions);
        $userInfo = $this->getUserInfo();
        $header = config('file_header.allot_export');
        //添加任务
        $data['name'] = '调拨导出' . '-' . date('YmdHis');
        $data['where'] = json_encode($where, JSON_UNESCAPED_UNICODE);
        $data['is_excel'] = 1;
        $data['system'] = 'wms';
        $data['serial_no'] = 'DB'.time().rand(0,99);
        $data['status'] = 1;
        $data['admin_id'] = $userInfo['uid'];
        $data['admin_name'] = $userInfo['nickname'];
        $data['header'] = json_encode(['fields'=>array_keys($header),'names' => array_values($header)], JSON_UNESCAPED_UNICODE);
        $data['type'] = 2;
        $data['service'] = 'allot/synclist';
        $data['is_limit'] = 0;

        $task_id = TaskService::addTask($data);
        return $this->returnApi(ResponseCode::SUCCESS, '调拨异步下载已提交', ['task_id' => $task_id]);
    }

    /**
     * @RequestMapping(path="/allot/export_multi_detail", methods="get,post")
     * @return mixed
     */
    public function exportMultiDetail(RequestInterface $request)
    {
        $wIdOptions = $this->getWidOptions();
        $where = $this->searchParams($request->all(),$wIdOptions);
        $url = AllotService::exportDetailList( $where);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
    }

    /**
     * 详情页列表
     * @RequestMapping(path="/allot/export_detail_list", methods="get,post")
     * @return mixed
     */
    public function exportDetailList(RequestInterface $request){
        $where = $this->validate($request->all(),'detail_search');
        $id = $request->input('id');
        $result = AllotService::info($id);
        if(!$result['info']){
            throw new BusinessException('调拨任务不存在');
        }
        $info = $result['info'];
        if ($info['status'] == 2){//待接单状态，直接显示调拨单数据
            $list = AllotService::detailList((int) $id,0 ,0);
            $fileHeaders = config('file_header.allot_detail_list');
        }else{
            $outTaskInfo = OutStoreService::getOutStore(['order_no'=>$info['serial_no'],'last_data'=>1]);
            $where['out_store_id']  = $outTaskInfo['id'];
            unset($where['id']) ;
            $list = OutStoreService::detailGroupList($where,0 ,0);
            $fileHeaders = config('file_header.allot_detail_list_wms');
        }

        if($list['data']){
            try {
                $url = exportToExcel($fileHeaders,$list['data'],'调拨详情');
            }catch (\Exception $e){
                throw new BusinessException('导出失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
            }
        }else{
            throw new BusinessException('无数据，导出失败！');
        }

        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
    }

    /**
     * 数据验证
     * @param array $data
     * @return array
     */
    protected function validate(array $data,$secne='default'){

        $message = [
            'multi_boxno.array' => '箱号参数有误!',
            'multi_boxno.required' => '箱号不能为空!',
            'box_ids.required' => '箱号不能为空!',
            'box_ids.string' => '箱号参数有误!',
            'send_type.numeric' => '承运方式参数有误!',
            'send_type.required' => '承运方式不能为空!',
            'express_id.numeric' => '物流公司参数有误!',
            'reg_admin_id.numeric' => '登记人参数有误!',
            'express_id.required' => '物流公司不能为空!',
            'express_code.string' => '物流单号格式有误!',
            'express_code.required' => '物流单号不能为空!',
            'remark.string' => '备注格式有误!',
            'image_list.array' => '附件格式有误!',
            'date_range.string' => '时间格式有误!',
            'reg_range_time.string' => '时间格式有误!',
            'send_date_range.string' => '时间格式有误!',
            'show_date_range.string' => '下发时间格式有误!',
            'search_type.numeric' => '查询类型有误!',
            'search_value.string' => '查询值有误!',
            'content.string' => '编码类型有误!',
            'serial_no.string' => '调拨单号有误!',
            'out_w_ids.array' => '发货仓库参数有误!',
            'in_w_ids.array' => '收货仓库参数有误!',
            'unique_code.array' => '店内码参数有误!',
            'box_no.array' => '箱号参数有误!',
            'allot_no.array' => '调拨单号参数有误!',
            'source_no.array' => '调拨单号参数有误!',
            'status.numeric' => '状态参数有误!',
            'diff_id.numeric' => '差异id不能为空!',
            'handle_num.required' => '处理数量不能为空!',
            'handle_num.numeric' => '处理数量格式有误!',
            'handle_num.min' => '处理数量必须大于1!',
            'diff_reason.required' => '差异原因不能为空!',
            'diff_reason.in' => '差异原因有误!',
            'liabler.required' => '归属方不能为空!',
            'liabler.in' => '归属方有误!',
            'detail_id.required' => '差异明细id不能为空!',
        ];
        $rules = [
            'express_id' => 'required_unless:send_type,2|numeric',
            'send_type' => 'required|numeric',
            'express_code' => 'required_unless:send_type,2|string',
            'out_w_ids' => 'string',
            'out_w_id' => 'array',
            'in_w_id' => 'array',
            'unique_code' => 'array',
            'box_no' => 'array',
            'allot_no' => 'array',
            'source_no' => 'array',
            'in_w_ids' => 'string',
            'reg_range_time' => 'string',
            'serial_no' => 'string',
            'status' => 'numeric',
            'out_store_id' => 'numeric',
            'id' => 'numeric',
            'allot_id' => 'numeric',
            'reg_admin_id' => 'numeric',
            'send_gt_sign' => 'numeric',
            'apply_gt_send' => 'numeric',
            'sign_gt_in' => 'numeric',
            'transfer_gt' => 'numeric',
            'out_gt_send' => 'numeric',
            'search_type' => 'numeric',
            'status_type' => 'numeric',
            'search_value' => 'string',
            'content' => 'string',
            'date_range' => 'string',
            'send_date_range' => 'string',
            'show_date_range' => 'string',
            'sign_time' => 'string',
            'multi_boxno' => 'required|array',
            'box_ids' => 'required|string',
            'diff_id' => 'required|numeric',
            'result_id' => 'required|numeric',
            'freight_price' => 'numeric',
            'express_time' => 'date',
            'remark' => 'string',
            'image_list' => 'array',
            'handle_num' => 'required|numeric|min:1',
            'diff_reason' => [
                'required',
                Rule::in([1,2,3,4])
            ],
            'liabler' => [
                'required',
                Rule::in([1,2])
            ],
            'detail_id' => 'required|numeric',
            'transfer_batch_no' => 'string',
            'transfer' => 'numeric',
        ];
        /**
         * 根据调拨单号获取差异详请
         * @param string $params [
         *   "handle_num" => 处理数量
         *   "diff_reason" => 差异原因
         *   "liabler" => 归属方
         *   "detail_id" => 差异明细id
         * ]
         * @return array
         */
        $secnes = [
            'send' => ['id','send_type','box_ids','express_id','express_code','remark','image_list','freight_price','express_time'],
            'finishSend' => ['id'],
            'search' => ['transfer_batch_no','transfer','out_w_ids','out_gt_send','transfer_gt','send_gt_sign','sign_gt_in','in_w_ids','serial_no','status','search_type','search_value','date_range','send_date_range','show_date_range','sign_time','apply_gt_send'],
            'warn_search' => ['out_w_ids','in_w_ids'],
            'detail_search' => ['id','status_type','search_type','content'],
            'diff_list' => ['diff_id','search_type','search_value'],
            'reg_list' => ['in_w_id','out_w_id','unique_code','box_no','source_no','reg_admin_id','reg_range_time'],
            'handle_diff' => ['diff_id','handle_num','diff_reason','liabler','detail_id'],
            'cancel_result' => ['diff_id','result_id'],
            'over_diff' => ['diff_id'],
        ];
        $useRule = [];
        if(isset($secnes[$secne])){
            foreach ($secnes[$secne] as $item){
                $useRule[$item] = $rules[$item];
            }
        }else{
            throw new BusinessException('验证场景值有误');
        }

        $validator = validate()->make(
            $data,
            $useRule,
            $message
        );

        return $validator->validate(); //验证数据有效性
    }

    public function getWidOptions(){
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $options = AllotService::ownWidOptions($wIds);
        $options['w_ids'] = $wIds;
        return $options;
    }
}