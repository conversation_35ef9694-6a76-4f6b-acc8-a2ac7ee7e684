<?php
declare(strict_types=1);

namespace App\JsonRpc;

interface OrderServiceServiceInterface
{
    /**
     * 退货处理列表 - wms系统
     * @param int $page 子单 页码
     * @param int $pageLimit 子单 每页数
     * @param array $search 子单+明细 搜索
     * @param int $detailPage 明细 页码
     * @param int $detailLimit 明细 每页数
     * @return mixed
     */
    public function wmsOrderList(int $page, int $pageLimit, array $search, int $detailPage, int $detailLimit);

    /**
     * 退货处理记录 - wms系统
     * @param int $export
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return array
     */
    public function signList(int $export, int $page, int $pageLimit, array $search);

    /**
     * 售后单 - 查多个
     * @param array $where
     * @param array|string[] $filed
     * @return array
     */
    public function getOrders(array $where, array $filed = ['*']);

    // 签收 - 查单个
    public function getSign(array $where, array $filed = ['*']);

    // 签收 - 查多个
    public function getSignS(array $where = [], array $filed = ['*']);

    // 质检 - 查
    public function getQuality(array $where, array $filed = ['*']);

    /**
     * 质检 - 修改（单个）
     * @param array $where 质检查询条件
     * @param array $data 质检更新数据
     * @param array $produceAreaData 当操作回仓时，则进入生产区，回仓动作仅只操作一次
     * @param array $extend [admin_id、admin_name]
     * @return bool
     */
    public function updateQuality(array $where, array $data, array $produceAreaData = [], array $extend = []);

    // 发货 - 查
    public function getLogistics(array $where, array $filed = ['*']);

    // 发货 - 编辑
    public function updateLogistics(array $where, array $data);

    /**
     * 退货处理
     * @param array $data
     * @return array
     */
    public function batchDeal(array $data);

    /**
     * 获取售后单的处理结果
     * @param array $search
     * @return array
     */
    public function orderServiceProcessingData(array $search);
    
    /**
     * 获取售后单和对应的订单信息
     */
    public function getServiceList(array $params, int $perPage = 10, int $currentPage = 1);

    /**
     * 售后单 - 查单个
     * @param array $where
     * @param array|string[] $filed
     * @return array
     */
    public function getOrder(array $where, array $filed = ['*']);
}