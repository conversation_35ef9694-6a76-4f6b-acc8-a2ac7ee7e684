<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Controller;

use App\Exception\BusinessException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use App\Constants\ResponseCode;
use App\Constants\PublicCode;
use App\Library\Facades\AdminService;
use function Composer\Autoload\includeFile;
use App\Library\Facades\AmqpProducer;
use App\Amqp\Producer\OrderLogisticsNoticeProducer;


/**
 * @Controller()
 */
class DeliveryController extends AbstractController
{
    /**
     * @Inject()
     * @var \App\JsonRpc\WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject()
     * @var \App\JsonRpc\LogisticsServiceInterface
     */
    private $LogisticsService;

    /**
     * @Inject()
     * @var \App\JsonRpc\OrderDeliveryServiceInterface
     */
    private $OrderDeliveryService;

    /**
     * @Inject()
     * @var \App\JsonRpc\OrderLogisticsServiceInterface
     */
    private $OrderLogisticsService;

    /**
     * @Inject()
     * @var \App\JsonRpc\OrderServiceInterface
     */
    private $OrderService;

    /**
     * @Inject()
     * @var \App\JsonRpc\SerialNoServiceInterface
     */
    private $SerialNoService;

    /**
     * @Inject()
     * @var \App\JsonRpc\ProduceAreaServiceInterface
     */
    private $ProduceAreaService;

    /**
     * @Inject()
     * @var \App\JsonRpc\InStoreServiceInterface
     */
    private $InStoreService;

    /**
     * @Inject()
     * @var \App\JsonRpc\PurchaseOrderServiceInterface
     */
    private $PurchaseOrderService;

    /**
     * @Inject()
     * @var \App\JsonRpc\ContractServiceInterface
     */
    private $ContractService;

    /**
     * @Inject()
     * @var \App\JsonRpc\PurchasePriceServiceInterface
     */
    private $PurchasePriceService;

    /**
     * @Inject()
     * @var \App\JsonRpc\WareBatchServiceInterface
     */
    private $WareBatchService;

    /**
     * @Inject()
     * @var \App\JsonRpc\StockServiceInterface
     */
    private $StockService;

    /**
     * @Inject()
     * @var \App\JsonRpc\SupplierServiceInterface
     */
    private $SupplierService;

    /**
     * @Inject()
     * @var \App\JsonRpc\OutStoreServiceInterface
     */
    private $OutStoreService;

    /**
     * @Inject()
     * @var \App\JsonRpc\CategoryServiceInterface
     */
    private $CategoryService;

    /**
     * @RequestMapping(path="/delivery/list", methods="get,post")
     * @return mixed
     */
    public function list()
    {
        $data['title'] = "快递交接记录";

        //获取仓库信息
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $warehouse_list = $this->WarehouseService->getWarehouses(['ids' => $wIds, 'type' => 1], ['id', 'name']);
        $data['warehouse_list'] = $warehouse_list;
        $warehouse_info = [];
        if (!empty($warehouse_list)) {
            $warehouse_info = array_column($warehouse_list, 'name', 'id');
        }

        //获取快递公司
        $logistics_company = $this->LogisticsService->list(1, 1000, []);
        $logistics_company_list = [];
        if (!empty($logistics_company['data'])) {
            foreach ($logistics_company['data'] as $c_item) {
                $logistics_company_list[$c_item['id']] = [
                    'id' => $c_item['id'],
                    'name' => $c_item['name']
                ];
            }
        }
        $data['logistics_company_list'] = $logistics_company_list;

        if ($this->isAjax()) {
            $where = $this->request->all();
            $where['page_size'] = $where['limit'] ?? $this->pageLimit();
            $where['export'] = isset($where['export']) ? $where['export'] : 0;

            $delivery_where = [
                'group_serial_no' => true,
                'order_by_id_desc' => true
            ];
            //仓库
            if (!isset($where['warehouse']) || empty($where['warehouse'])) {
                $delivery_where['w_ids'] = $wIds;
            } else {
                $delivery_where['w_id'] = $where['warehouse'];
            }
            //快递公司
            if (isset($where['logistics_company']) && !empty($where['logistics_company'])) {
                $delivery_where['company_id'] = $where['logistics_company'];
            }
            //创建时间
            if (isset($where['date']) && !empty($where['date'])) {
                $date_time = explode(' - ', $where['date']);
                $delivery_where['start_time'] = $date_time[0] . ' 00:00:00';
                $delivery_where['end_time'] = $date_time[1] . ' 23:59:59';
            }
            //快递单号
            if (isset($where['express_codes']) && !empty($where['express_codes'])) {
                $express_codes = explode("\n", $where['express_codes']);
                $delivery_where['express_codes'] = $express_codes;
            }
            //子订单号
            if (isset($where['branch_serial_nos']) && !empty($where['branch_serial_nos'])) {
                $branch_serial_nos = explode("\n", $where['branch_serial_nos']);
                $delivery_where['branch_serial_nos'] = $branch_serial_nos;
            }

            $result = $this->OrderDeliveryService->deliveryList($delivery_where, intval($where['limit']), intval($where['page']));
            $list = [];
            if (!empty($result['list'])) {
                foreach ($result['list'] as $item) {
                    $list[] = [
                        'id' => $item['id'],
                        'serial_no' => $item['serial_no'],
                        'warehouse_name' => $warehouse_info[$item['w_id']],
                        'logistics_company_name' => $logistics_company_list[$item['logistics_company_id']]['name'],
                        'express_num' => $item['express_num'],
                        'admin_id' => $item['admin_id'],
                        'admin_name' => $item['admin_name'],
                        'created_at' => $item['created_at']
                    ];
                }
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list, ['count' => $result['total'], 'limit' => $where['page_size'], 'export' => $where['export']]);
        }

        return $this->show('delivery/list', $data);
    }

    /**
     * @RequestMapping(path="/delivery/orderLogisticsExports", methods="get,post")
     * @return mixed
     */
    public function orderLogisticsExports()
    {
        //获取仓库信息
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $where = $this->request->all();

        $delivery_where = [];

        //仓库
        if (!isset($where['warehouse']) || empty($where['warehouse'])) {
            $delivery_where['w_ids'] = $wIds;
        } else {
            $delivery_where['w_id'] = $where['warehouse'];
        }
        //快递公司
        if (isset($where['logistics_company']) && !empty($where['logistics_company'])) {
            $delivery_where['company_id'] = $where['logistics_company'];
        }
        //创建时间
        if (isset($where['date']) && !empty($where['date'])) {
            $date_time = explode(' - ', $where['date']);
            $delivery_where['start_time'] = $date_time[0] . ' 00:00:00';
            $delivery_where['end_time'] = $date_time[1] . ' 23:59:59';
        }else{
            return $this->returnApi(ResponseCode::SERVER_ERROR, '请选择时间');
        }
        //快递单号
        if (isset($where['express_codes']) && !empty($where['express_codes'])) {
            $express_codes = explode("\n", $where['express_codes']);
            $delivery_where['express_codes'] = $express_codes;
        }
        //子订单号
        if (isset($where['branch_serial_nos']) && !empty($where['branch_serial_nos'])) {
            $branch_serial_nos = explode("\n", $where['branch_serial_nos']);
            $delivery_where['branch_serial_nos'] = $branch_serial_nos;
        }

        $header = [
            'warehouse_name' => '仓库',
            'logistics_company_name' => '快递公司',
            'logistics_serial_no' => '快递单号',
            'created_at' => '交接时间',
            'branch_serial_no' => '订单号',
            'first_category' => '一级类目',
            'second_category' => '二级类目',
            'three_category' => '三级类目',
            'num' => '货品数量',
        ];

        $result = $this->OrderDeliveryService->orderLogisticsExports($delivery_where);
        $data = [];
        if (!empty($result)) {
            $warehouse_list = $this->WarehouseService->getWarehouses(['ids' => $wIds, 'type' => 1], ['id', 'name']);
            $warehouse_info = [];
            if (!empty($warehouse_list)) {
                $warehouse_info = array_column($warehouse_list, 'name', 'id');
            }

            //获取分类数据
            $categoryIds = array_unique(array_column($result, 'category_id'));
            $categoryInfos = $this->CategoryService->getCategoryNamePathByBatch($categoryIds);
            $categoryMaps = collect($categoryInfos)->pluck('name_path', 'id');

            //组织数据
            foreach ($result as $item) {
                $item['warehouse_name'] = $warehouse_info[$item['w_id']];
                $item['first_category'] = '';
                $item['second_category'] = '';
                $item['three_category'] = '';
                if (isset($categoryMaps[$item['category_id']])) {
                    $category = explode("/", $categoryMaps[$item['category_id']]);
                    $item['first_category'] = $category[0];
                    $item['second_category'] = $category[1];
                    $item['three_category'] = $category[2];
                }
                $data[] = $item;
            }
        }
        $info = exportCsv($header, $data, '快递交接明细数据');
        return $info;
    }
    /**
     * @RequestMapping(path="/delivery/down_list", methods="get,post")
     * @return mixed
     */
    public function down_list()
    {
        //获取仓库信息
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $warehouse_list = $this->WarehouseService->getWarehouses(['ids' => $wIds, 'type' => 1], ['id', 'name']);
        $warehouse_info = [];
        if (!empty($warehouse_list)) {
            $warehouse_info = array_column($warehouse_list, 'name', 'id');
        }

        //获取快递公司
        $logistics_company = $this->LogisticsService->list(1, 1000, []);
        $logistics_company_info = [];
        if (!empty($logistics_company['data'])) {
            $logistics_company_info = array_column($logistics_company['data'], 'name', 'id');
        }

        $where = $this->request->all();

        $delivery_where = [
            'order_by_detail_id_desc' => true
        ];
        //仓库
        if (!isset($where['warehouse']) || empty($where['warehouse'])) {
            $delivery_where['w_ids'] = $wIds;
        } else {
            $delivery_where['w_id'] = $where['warehouse'];
        }
        //快递公司
        if (isset($where['logistics_company']) && !empty($where['logistics_company'])) {
            $delivery_where['company_id'] = $where['logistics_company'];
        }
        //创建时间
        if (isset($where['date']) && !empty($where['date'])) {
            $date_time = explode(' - ', $where['date']);
            $delivery_where['start_time'] = $date_time[0] . ' 00:00:00';
            $delivery_where['end_time'] = $date_time[1] . ' 23:59:59';
        }
        //快递单号
        if (isset($where['express_codes']) && !empty($where['express_codes'])) {
            $express_codes = explode("\n", $where['express_codes']);
            $delivery_where['express_codes'] = $express_codes;
        }
        //子订单号
        if (isset($where['branch_serial_nos']) && !empty($where['branch_serial_nos'])) {
            $branch_serial_nos = explode("\n", $where['branch_serial_nos']);
            $delivery_where['branch_serial_nos'] = $branch_serial_nos;
        }

        $header = [
            'serial_no' => '批次号',
            'warehouse_name' => '仓库',
            'logistics_company_name' => '快递公司',
            'express_code' => '快递单号',
            'goods_num' => '货品数量',
            'admin_name' => '操作人',
            'created_at' => '操作时间'
        ];
        $data = [];
        $page_id = 1;
        $current_page = 100;
        while (true) {
            $result = $this->OrderDeliveryService->deliveryList($delivery_where, $current_page, $page_id);
            if (empty($result['list'])) {
                break;
            }
            $page_id++;
            foreach ($result['list'] as $item) {
                $data[] = [
                    'serial_no' => $item['serial_no'],
                    'warehouse_name' => $warehouse_info[$item['w_id']],
                    'logistics_company_name' => $logistics_company_info[$item['logistics_company_id']],
                    'express_code' => '`'.$item['express_code'],
                    'goods_num' => $item['goods_num'],
                    'admin_name' => $item['admin_name'],
                    'created_at' => $item['created_at']
                ];
            }
        }

        $info = exportCsv($header, $data, '快递交接列表');
        return $info;
    }

    /**
     * @RequestMapping(path="/delivery/detail/{id}", methods="get,post")
     */
    public function detail()
    {
        $id = $this->request->route('id');
        $data['title'] = ' 快递交接明细';

        //获取快递单详情信息
        $delivery_data = $this->OrderDeliveryService->deliveryOne(['id' => $id]);
        if (!empty($delivery_data)) {
            $warehouse_info = $this->WarehouseService->getWarehouse(['id' => $delivery_data['w_id']], ['id', 'name']);

            $logistics = $this->LogisticsService->getLogistics(['id' => $delivery_data['logistics_company_id']], ['id', 'name']);
            $data['id'] = $delivery_data['id'];
            $data['serial_no'] = $delivery_data['serial_no'];
            $data['w_id'] = $delivery_data['w_id'];
            $data['w_name'] = $warehouse_info['name'] ?? '未知';
            $data['express_num'] = $delivery_data['express_num'];
            $data['logistics_company'] = $logistics['name'] ?? '未知';
            $data['admin_name'] = $delivery_data['admin_name'];
            $data['created_at'] = $delivery_data['created_at'];
            return $this->show('delivery/detail', $data);
        }
    }

    /**
     * 快递交接详情单号列表
     * @RequestMapping(path="/delivery/detail_list/{order_delivery_id}", methods="get,post")
     */
    public function detail_list()
    {
        $order_delivery_id = $this->request->route('order_delivery_id');

        $where = $this->request->all();
        if (empty($order_delivery_id)) {
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', [], ['count' => 0, 'limit' => 0]);
        }

        //获取交接单详情
        $detail_where = [
            'order_delivery_id' => $order_delivery_id,
            'order_by_id_desc' => true
        ];
        if (isset($where['express_code']) && !empty($where['express_code'])) {
            $detail_where['express_code'] = $where['express_code'];
        }

        $delivery_detail = $this->OrderDeliveryService->deliveryDetailList($detail_where, intval($where['limit']), intval($where['page']));
        if (empty($delivery_detail['list'])) {
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', [], ['count' => 0, 'limit' => 0]);
        }

        $data = [];
        foreach ($delivery_detail['list'] as $key => $item) {
            $data[] = [
                'key' => ($where['page'] - 1) * $where['limit'] + $key + 1,
                'express_code' => $item['express_code'],
                'goods_num' => $item['goods_num'],
                'address' => $item['province'] . $item['city'] . $item['area']
            ];
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $data, ['count' => $delivery_detail['total'], 'limit' => $where['limit']]);

    }

    /**
     * 导出快递交接详情
     * @RequestMapping(path="/delivery/export_detail_list", methods="get,post")
     */
    public function export_detail_list()
    {
        $where = $this->request->all();

        $header = [
            'express_code' => '快递单号',
            'goods_num' => '货品数量',
            'address' => '收货地址',
        ];
        $data = [];

        if (empty($where['order_delivery_id'])) {
            $info = exportCsv($header, $data, '快递交接详情');
            return $info;
        }

        //获取订单商品
        $detail_where = [
            'order_delivery_id' => $where['order_delivery_id'],
            'order_by_id_desc' => true
        ];
        if (isset($where['express_code']) && !empty($where['express_code'])) {
            $detail_where['express_code'] = $where['express_code'];
        }

        $page_id = 1;
        $current_page = 100;
        while (true) {
            $delivery_detail = $this->OrderDeliveryService->deliveryDetailList($detail_where, $current_page, $page_id);
            if (empty($delivery_detail['list'])) {
                break;
            }
            $page_id++;

            foreach ($delivery_detail['list'] as $key => $item) {
                $data[] = [
                    'express_code' => $item['express_code'],
                    'goods_num' => $item['goods_num'],
                    'address' => $item['province'] . $item['city'] . $item['area']
                ];
            }
        }
        $info = exportCsv($header, $data, '快递交接详情');
        return $info;
    }

    /**
     * 打印快递交接单
     * @RequestMapping(path="/delivery/print/{id}", methods="get,post")
     */
    public function print()
    {
        $id = $this->request->route('id');
        $data['title'] = '快递交接单';

        //获取快递单详情信息
        $delivery_data = $this->OrderDeliveryService->deliveryOne(['id' => $id]);
        if (!empty($delivery_data)) {
            $warehouse_info = $this->WarehouseService->getWarehouse(['id' => $delivery_data['w_id']], ['id', 'name']);

            $logistics = $this->LogisticsService->getLogistics(['id' => $delivery_data['logistics_company_id']], ['id', 'name']);
            $data['id'] = $delivery_data['id'];
            $data['serial_no'] = $delivery_data['serial_no'];
            $data['w_id'] = $delivery_data['w_id'];
            $data['w_name'] = $warehouse_info['name'] ?? '未知';
            $data['express_num'] = $delivery_data['express_num'];
            $data['logistics_company'] = $logistics['name'] ?? '未知';
            $data['admin_name'] = $delivery_data['admin_name'];
            $data['created_at'] = $delivery_data['created_at'];
            $data['join_at'] = date('Y-m-d H:i');

            //获取交接单详情
            $detail_where = [
                'order_delivery_id' => $id,
                'order_by_id_desc' => true
            ];

            $delivery_detail_data = [];
            $delivery_detail = $this->OrderDeliveryService->deliveryDetailList($detail_where, $delivery_data['express_num'], 1);
            if (!empty($delivery_detail['list'])) {
                foreach ($delivery_detail['list'] as $key => $item) {
                    $delivery_detail_data[] = [
                        'key' => $key + 1,
                        'express_code' => $item['express_code'],
                        'goods_num' => $item['goods_num'],
                        'address' => $item['province'].' '.$item['city'].' '.$item['area']
                    ];
                }
            }

            $data['delivery_detail_data'] = $delivery_detail_data;
        }

        return $this->show('delivery/print', $data);
    }

    /**
     * 创建快递交接
     * @RequestMapping(path="/delivery/create", methods="get,post")
     */
    public function create(){
        $data['title'] = '快递交接';

        //获取仓库信息
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $warehouse_list = $this->WarehouseService->getWarehouses(['ids' => $wIds, 'type' => 1], ['id', 'name']);
        $data['warehouse_list'] = $warehouse_list;

        //获取快递公司

        $logistics_company = $this->LogisticsService->list(1, 1000, ['type' => 1]);
        $logistics_company_list = [];
        if (!empty($logistics_company['data'])) {
            foreach ($logistics_company['data'] as $c_item) {
                $logistics_company_list[$c_item['id']] = [
                    'id' => $c_item['id'],
                    'name' => $c_item['name']
                ];
            }
        }
        $data['logistics_company_list'] = $logistics_company_list;

        return $this->show('delivery/create', $data);
    }

    /**
     * 校验快递单号
     * @RequestMapping(path="/delivery/submit_express", methods="get,post")
     */
    public function submit_express(){
        $params = $this->request->all();
        $where['c_id'] = $params['c_id'];
        $where['w_id'] = $params['w_id'];
        if (isset($params['logistics_serial_no']) && !empty($params['logistics_serial_no'])){
            $where['logistics_serial_nos'] = [$params['logistics_serial_no']];
        }
        if (isset($params['logistics_serial_nos']) && !empty($params['logistics_serial_nos'])){
            $where['logistics_serial_nos'] = explode("\n", $params['logistics_serial_nos']);
        }

        $error_list = $this->check_express_code($where);

        if (!empty($error_list)){
            if($params['type'] == 'only'){#检测单个快递单号
                return $this->returnApi(ResponseCode::SERVER_ERROR, '操作异常', $error_list[0]);
            }else{#批量校验
                return $this->returnApi(ResponseCode::SERVER_ERROR, '操作异常', $error_list);
            }
        }else{
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }
    }

    /**
     * 交接
     * @RequestMapping(path="/delivery/add_take", methods="get,post")
     */
    public function add_take(){
        $params = $this->request->all();

        $rules = [
            'logistics_serial_nos' => 'required|string',
            'c_id' => 'required|integer',
            'w_id' => 'required|integer'
        ];

        $message = [
            'logistics_serial_nos.required' => '快递单号不能为空!',
            'logistics_serial_nos.string' => '快递单号类型不符!',
            'w_id.required' => '仓库不能为空!',
            'w_id.integer' => '仓库参数类型不符!',
            'c_id.required' => '快递公司不能为空!',
            'c_id.integer' => '快递公司参数类型不符!',
        ];

        $validator = validate()->make(            $params,            $rules,            $message        );
        if ($validator->fails()) {
            $messages = $validator->errors()->first();
            return $this->returnApi(ErrorCode::SERVER_ERROR, $messages, false);
        }

        try {
            $where['c_id'] = $params['c_id'];
            $where['w_id'] = $params['w_id'];
            if (isset($params['logistics_serial_nos']) && !empty($params['logistics_serial_nos'])){
                $where['logistics_serial_nos'] = explode("\n", $params['logistics_serial_nos']);
            }
            $error_list = $this->check_express_code($where);
            if (!empty($error_list)){
                return $this->returnApi(ResponseCode::SERVER_ERROR, '操作异常', $error_list);
            }
            $params['user_info'] = $this->session->get('userInfo');
            $order_delivery_id = $this->OutStoreService->onlineOrderDelivery($params);
            return $this->returnApi(ResponseCode::SUCCESS, '交接成功', $order_delivery_id);
        }catch (\Exception $e){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '交接失败');
        }
    }

    /**
     * 校验快递单号
     */
    private function check_express_code(array $where){
        $error_list = [];

        //检测是否重复扫描
        $unique_serial_nos = array_unique($where['logistics_serial_nos']);
        $diff_serial_no = array_diff($where['logistics_serial_nos'], $unique_serial_nos);
        if (!empty($diff_serial_no)){
            foreach ($diff_serial_no as $serial_no){
                $error_list[] = [
                    'express_code' => $serial_no,
                    'error_info' => '重复扫描'
                ];
            }
        }

        $logistics_where = $delivery_where = [];
        if (isset($where['c_id']) && !empty($where['c_id'])){
            $logistics_where['logistics_company_id'] = $where['c_id'];
            $delivery_where['company_id'] = $where['c_id'];
        }
        if (isset($where['w_id']) && !empty($where['w_id'])){
            $delivery_where['w_id'] = $where['w_id'];
            $logistics_where['w_id'] = $where['w_id'];
        }
        $where['logistics_serial_nos'] = array_filter($where['logistics_serial_nos']);
        if (isset($where['logistics_serial_nos']) && !empty($where['logistics_serial_nos'])){
            $delivery_where['express_codes'] = $where['logistics_serial_nos'];
            $logistics_where['logistics_serial_nos'] = $where['logistics_serial_nos'];
        }

        //获取快递交接信息
        $order_delivery_list = $this->OrderDeliveryService->deliveryList($delivery_where, count($delivery_where['express_codes']), 1);
        if (!empty($order_delivery_list['list'])){
            foreach ($order_delivery_list['list'] as $d_item) {
                $error_list[] = [
                    'express_code' => $d_item['express_code'],
                    'error_info' => '快递重复交接'
                ];
            }
        }

        //获取订单发货物流信息
        $logistics_where['status'] = 1;
        $order_logistics_list = $this->OrderLogisticsService->getLogisticsOrder($logistics_where);
        if (!empty($order_logistics_list)){#订单发货物流信息存在
            #快递单号数组
            $order_logistics_codes = array_column($order_logistics_list, 'logistics_serial_no');
            #子订单号与快递单号对应关系
            $order_logistics_map = array_column($order_logistics_list, 'branch_serial_no', 'logistics_serial_no');
            foreach ($where['logistics_serial_nos'] as $l_code){
                if (!in_array($l_code, $order_logistics_codes)){
                    $error_list[] = [
                        'express_code' => $l_code,
                        'error_info' => '快递单号不存在'
                    ];
                }
            }

            foreach ($order_logistics_list as $l_item) {
                if (empty($l_item['branch_serial_no'])){
                    $error_list[] = [
                        'express_code' => $l_item['logistics_serial_no'],
                        'error_info' => '绑定的订单号不能为空'
                    ];
                }

                //校验快递公司
                if ($l_item['logistics_company_id'] != $where['c_id']){
                    $error_list[] = [
                        'express_code' => $l_item['logistics_serial_no'],
                        'error_info' => '快递公司错误'
                    ];
                }
            }

            //获取订单信息
            $branch_serial_nos = array_unique(array_column($order_logistics_list, 'branch_serial_no'));
            if (!empty($branch_serial_nos)){
                $order_list = $this->OrderService->getOrderBranchS(['branch_serial_no' => $branch_serial_nos], ['*']);
                if (!empty($order_list)){
                    foreach ($order_list as $order_info){
                        if ($order_info['status'] == -1){
                            $error_list[] = [
                                'express_code' => array_search($order_info['branch_serial_no'], $order_logistics_map),
                                'error_info' => '订单取消'
                            ];
                        }elseif ($order_info['exception_status'] == 2 || $order_info['exception_status'] == 3){
                            $error_list[] = [
                                'express_code' => array_search($order_info['branch_serial_no'], $order_logistics_map),
                                'error_info' => '订单异常'
                            ];
                        }
                    }
                }else{#订单不存在
                    foreach ($branch_serial_nos as $order_code){
                        $error_list[] = [
                            'express_code' => array_search($order_code, $order_logistics_map),
                            'error_info' => '订单不存在'
                        ];
                    }
                }
            }

            //获取订单商品项信息
            $order_branch_detail_ids = array_unique(array_column($order_logistics_list, 'order_detail_id'));
            if (!empty($order_branch_detail_ids)){
                $order_branch_detail_list = $this->OrderService->getOrderBranchDetailS(['ids' => $order_branch_detail_ids]);
                if (!empty($order_branch_detail_list)){
                    foreach ($order_branch_detail_list as $detail_info){
                        if ($detail_info['status'] == -1 || $detail_info['exception_status'] == 2){
                            $error_list[] = [
                                'express_code' => array_search($detail_info['branch_serial_no'], $order_logistics_map),
                                'error_info' => '订单取消'
                            ];
                        }
                    }
                }else{
                    foreach ($branch_serial_nos as $order_code){
                        $error_list[] = [
                            'express_code' => array_search($order_code, $order_logistics_map),
                            'error_info' => '订单对应的商品详情不存在'
                        ];
                    }
                }
            }

        }else{#订单发货物流信息不存在时
            foreach ($where['logistics_serial_nos'] as $l_code){
                $error_list[] = [
                    'express_code' => $l_code,
                    'error_info' => '快递单号不存在'
                ];
            }
        }

        return $error_list;
    }
}