<?php
declare(strict_types=1);

namespace App\Engine;

use Hyperf\View\Engine\EngineInterface;
use Hyperf\View\Engine\BladeEngine;

class TemplateEngine implements EngineInterface
{
    public function render($template, $data, $config): string
    {
        $viewConfig = config('view');
        // 实例化对应的模板引擎的实例
        $engine = new BladeEngine();
        // 并调用对应的渲染方法
        $config = $config ?? $viewConfig['config'];
        return $engine->render($template, $data, $config);
    }
}