<?php


namespace App\JsonRpc;


interface UniqueCodeLogServiceInterface
{
    public function operationLog(string $mess_id,array $data,$tp=true);

    public function getOperationLogList(array $params,int $page=0,int $prePage=20);
    /**
     *
     * @param string $operation_id 操作记录ID
     * @param array $data
     * @return bool
     */
    public function batchOperationLog (string $operation_id, array $data);

    /**
     *
     * @param string $operation_id 批量记录店内码操作日志，每个店内码详细操作描述均可自定义记录
     * @param array $data
     * @return bool
     */
    public function batchOperationLogByDetailInfo (string $operation_id, array $data);
}