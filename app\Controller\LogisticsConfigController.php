<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use App\Constants\ErrorCode;
use App\JsonRpc\AreaServiceInterface;
use App\JsonRpc\LogisticsServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use App\Library\Facades\AdminService;
use FFI\Exception;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;

/**
 * @Controller()
 */
class LogisticsConfigController extends AbstractController
{
    /**
     * @Inject ()
     * @var LogisticsServiceInterface
     */
    private $LogisticsService;

    /**
     * @Inject ()
     * @var AreaServiceInterface
     */
    private $AreaService;

    /**
     * @Inject ()
     * @var WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * 新建
     * @RequestMapping(path="/LogisticsConfig/add", methods="get,post")
     */
    public function add(RequestInterface $request)
    {
        if ($request -> isMethod("post")) {
            $params = $request -> all();
            // 校验参数
            $validator = validate()->make($params, [
                'lg_id'             => 'required|numeric',
                'cosigner'          => 'required|string',
                'cosigner_phone'    => 'required|string',
                'deliver_province'  => 'required|string',
                'deliver_city'      => 'required|string',
                'deliver_area'      => 'required|string',
                'deliver_detail'    => 'required|string',
            ],[
                'lg_id.required' => '请选择快递公司',
                'cosigner.required' => '发货人姓名必填',
                'cosigner_phone.required' => '发货人电话必填',
                'deliver_province.required' => '请选择发货人地址-省',
                'deliver_city.required' => '请选择发货人地址-市',
                'deliver_area.required' => '请选择发货人地址-区',
                'deliver_detail.required' => '请填写发货人详细地址',
            ]);
            if ($validator->fails()) {
                return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
            }

            // 快递公司数据
            $lgData = [
                'contact' => isset($params['contact']) && !empty($params['contact']) ? $params['contact'] : '',
                'phone' => isset($params['phone']) && !empty($params['phone']) ? $params['phone'] : '',
            ];
            try {
                // 更新快递公司信息
                if (!empty($params['lg_id']) && (!empty($lgData['contact']) || !empty($lgData['phone']))) {
                    $this -> LogisticsService -> update((int)$params['lg_id'],$lgData);
                }
                // 添加快递关联
                $result = $this -> LogisticsService ->addLogisticsConfig($params);
                return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
            } catch (\Exception $exception) {
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
            }

        }
        return $this->show('logisticsConfig/add');
    }

    /**
     * 编辑
     * @RequestMapping(path="/LogisticsConfig/edit", methods="get,post")
     */
    public function edit(RequestInterface $request)
    {
        $params = $request -> all();
        $id = $params['id'] ?? 0;

        if ($request -> isMethod("post")) {
            var_dump('edit-post=',$params);
            // 快递公司数据
            $lgData = [
                'contact' => isset($params['contact']) && !empty($params['contact']) ? $params['contact'] : '',
                'phone' => isset($params['contact_phone']) && !empty($params['contact_phone']) ? $params['contact_phone'] : '',
            ];
            try {
                // 更新快递公司信息
                if (!empty($params['lg_id']) && (!empty($lgData['contact']) || !empty($lgData['phone']))) {
                    $this -> LogisticsService -> update((int)$params['lg_id'],$lgData);
                }
                // 添加快递关联
                $result = $this -> LogisticsService ->updateLogisticsConfig((int)$id , $params);
                return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
            } catch (\Exception $exception) {
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
            }
        }
        return $this->show('logisticsConfig/edit',['id' => $id]);

    }

    /**
     * 编辑
     * @RequestMapping(path="/LogisticsConfig/getOne", methods="get,post")
     */
    public function getOne(RequestInterface $request)
    {
        $params = $request -> all();
        $id = $params['id'] ?? 0;
        try {
            $result = $this -> LogisticsService -> getLogisticsConfigOne(['id' => $id]);
            var_dump(333,$result);
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 获取所有下拉框数据
     * @RequestMapping(path="/LogisticsConfig/getSelectData", methods="get,post")
     */
    public function getSelectData()
    {
        try {
            // 仓库
            // 仓库
            //$warehouse = getWarehouses();
            $wIds = AdminService::organizeWareHouseData($this->getUserId());
            //获取仓库信息
            $warehouse = $this->WarehouseService->getWarehouses(['ids'=>$wIds], ['id', 'name']);
            //$warehouse = getWarehouses();
            // 地区
            $area = getAreaAll();
            // 快递公司
            $expresses = $this -> LogisticsService -> getLogisticsS(['type' => 3]); // 类型：1快递 2物流 3菜鸟快递公司
            $result = [
                'areas' => $area,
                'warehouses' => $warehouse,
                'expresses' => $expresses
            ];
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }

    }

    /**
     * 列表
     * @RequestMapping(path="/LogisticsConfig/list", methods="get,post")
     */
    public function list(RequestInterface $request)
    {
        if ($request -> isMethod('post')) {
            $params = $request -> all();
            $page = $params['page'] ?? 1;
            $limit= $params['limit'] ?? 10;
            $where= $params['where'] ?? [];
            try {
                $result = $this -> LogisticsService -> getLogisticsConfigList((int)$page, (int)$limit, $where);
                return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
            } catch (\Exception $exception) {
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
            }
        }
        return $this->show('logisticsConfig/list');
    }

    /**
     * 根据仓库获取快递
     * @RequestMapping(path="/LogisticsConfig/getLogisticsByWId", methods="get,post")
     */
    public function getLogisticsByWId(RequestInterface $request)
    {
        $params = $request -> all();
        // 校验参数
        $validator = validate()->make($params, [
            'w_id'             => 'required|numeric',
            'type'          => 'required|numeric',
        ],[
            'w_id.required' => '请选择快递公司',
            'type.required' => '发货人姓名必填',
        ]);
        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        try {
            $where = [
                'w_id' => $params['w_id'],
                'logistics_type' => $params['type'],
                'logistics_status' => 1, // 状态（0：禁用 1启用）
                ];
            $result = $this -> LogisticsService -> getLogisticsByWId($where);
            logger() -> info('快递信息：',$result);
            $return = [];
            $wareList = $result[$params['w_id']];
            if (!empty($result) && $wareList) {
                foreach ($wareList as $v) {
                    array_push($return,[
                        'id' => $v['l_id'],
                        'name' => $v['l_name'],
                    ]);
                }
            }
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$return);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }

    }

}
