<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * 物流管理消费者
 * Interface LogisticsCompanyServiceInterface
 * @package App\JsonRpc
 */
interface LogisticsServiceInterface
{
    /**
     * 列表
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return array
     */
    public function list(int $page, int $pageLimit, array $search);

    /**
     * 添加
     * @param array $data
     * @return bool
     */
    public function create(array $data);

    /**
     * 查单个
     * @param array $where
     * @param array|string[] $filed
     */
    public function getLogistics(array $where, array $filed = ['*']);

    /**
     * 查多个
     * @param array $where
     * @param array|string[] $filed
     */
    public function getLogisticsS(array $where = [], array $filed = ['*']);

    /**
     * 编辑
     * @param int $id
     * @param array $data
     * @return int
     */
    public function update(int $id, array $data);

    /**
     * 查询仓库绑定的物流
     * @param array $where
     * @return array
     */
    public function getLogisticsByWId(array $where);

    /**
     * 判断是否有仓库已绑定
     * @param int $id
     * @return bool
     */
    public function checkLogisticsWarehouseExist(int $id);

    /**
     * wms-添加快递公司配置信息
     * @param $data
     * @return mixed
     */
    public function addLogisticsConfig($data);

    /**
     * wms-获取快递公司配置列表
     * @param $data
     */
    public function getLogisticsConfigList(int $page, int $limit, array $where , array $fields = []);

    /**
     * wms-获取快递公司配置列表
     * @param $data
     */
    public function getLogisticsConfigOne(array $where);

    /**
     * wms-获取快递公司配置列表
     * @param $data
     */
    public function updateLogisticsConfig(int $id, array $data);

    /**
     * 仓库 - 物流配置
     * @param int $w_id
     * @return array
     */
    public function getLogisticsConfigByWId(int $w_id);

    /**
     * 根据条件获取所有快递关联信息
     * @param array $where
     * @return mixed
     */
    public function getLogisticsConfigs(array $where);
}