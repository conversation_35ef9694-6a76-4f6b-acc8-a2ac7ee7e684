<?php

declare(strict_types=1);

namespace App\Amqp\Producer;

use Hyperf\Amqp\Annotation\Producer;
use Hyperf\Amqp\Message\ProducerMessage;

/**
 * 仓库绩效
 * @Producer(exchange="bigoffs.direct", routingKey="achievement")
 */
class AchievementProducer extends ProducerMessage
{
    /**
     * AchievementProducer constructor.
     * @param array $params
     * // 店内码类型参数
     * [
     *      w_id => 仓库id
     *      type => 单据类型
     *      source_no => 单据号
     *      admin_id => 操作人id,
     *      admin_name => 操作人名称,
     *      handle_at => 操作时间,
     *      code_type => 1店内码
     *      code_list => [
     *          'aaa',
     *          'bbb'
     * ]
     * // 条形码类型参数
     * [
     *      w_id => 仓库id
     *      type => 单据类型
     *      source_no => 单据号
     *      admin_id => 操作人id,
     *      admin_name => 操作人名称,
     *      handle_at => 操作时间,
     *      code_type => 2条码
     *      code_list => [
     *          ['code' => 'aaa', 'num' => 2],
     *          ['code' => 'bbb', 'num' => 3],
     * ]
     */
    public function __construct(array $params)
    {
        $this->payload = $params;
    }
}


