<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use App\Library\Facades\GuzzleClientFactory;
use App\Model\Admin;
use Gregwar\Captcha\CaptchaBuilder;
use <PERSON>war\Captcha\PhraseBuilder;
use GuzzleHttp\Client;
use Hyperf\HttpMessage\Cookie\Cookie;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\ResponseInterface;

/**
 * @Controller()
 */
class LoginController extends AbstractController
{
    /**
     * 登录
     * @RequestMapping(path="/login", methods="get,post")
     */
    public function index()
    {
        if ($this->isAjax()){
            $data = $this->request->all();
            $check_code = $this->session->get('check_code','-');
            /*if(strtolower($check_code) != strtolower($data['captcha'])){
                return $this->returnApi('500','验证码输入错误！');
            }*/
            $client = GuzzleClientFactory::gatewayClient();
            $response = $client->request('POST', '/login',[
                'json' => [
                    'appid'=> env('APP_TYPE','wms'),
                    'username'=> $data['username'],
                    'password'=>$data['password'],
                ],
                'headers' => ['Content-Type' => 'application/json']
            ]);
            $code = $response->getStatusCode();
            if($code != 200){
                return $this->returnApi($code,'网关错误！');
            }

            $body = json_decode($response->getBody()->getContents(),true);
            logger()->debug('login_result',['body'=>$body,'data'=>$data]);
            if(!$body ){
                return $this->returnApi($code,'网关数据错误！');
            }
            if($body['code'] == 200){
                $this->session->set('token', $body['data']['token']);
                $this->session->set('userInfo', $body['data']['userInfo']);
                //$cookie = new Cookie('cookie_x-rbac-token', 'value111',time()+30000,'/',env('DOMAIN',''),false,true);
//                $cookie = new Cookie('cookie_x-rbac-token', 'value111',0,'/',env('DOMAIN',''),false,true);
                $msg = '登录成功';
//                return $this->returnApi($code,$msg,['uid' => $body['data']['userInfo']['uid']])->withStatus(200)->withCookie($cookie);
                return $this->returnApi($code,$msg,['uid' => $body['data']['userInfo']['uid']])->withStatus(200);
            }else{
                $msg = $body['msg'];
            }
            return $this->returnApi($code,$msg);
        }
//        $port = env('APP_PORT',80);
//        logger()->debug('login_url',['port'=>$port,'host'=>$this->request->getUri()->getHost(),$this->request->fullUrl()]);
//        if($port != 80){
//            $port = ':'.$port;
//        }else{
//            $port = '';
//        }
//        $login_url = env('LOGIN_URL','https://x-erp.bigoffs.cn/#/login/login').'?r=//'.urlencode($this->request->getUri()->getHost().$port);
//
        //通过服务进行登录验证
        return $this->show('login/index',['login_url'=>getLoginUrl($this->request)]);
    }

    /**
     * 骑证码
     * @RequestMapping(path="/verify", methods="get")
     */
    public function verify(){
        $phrase = new PhraseBuilder();
        $code = $phrase->build(4);
        $builder = new CaptchaBuilder($code, $phrase);
        $builder->setBackgroundColor(220, 210, 230);
        $builder->setMaxAngle(25);
        $builder->setMaxBehindLines(0);
        $builder->setMaxFrontLines(0);
        //可以设置图片宽高及字体
        $builder->build($width = 115, $height = 40, $font = null);

        $phrase = strtolower($builder->getPhrase());
        $this->session->set('check_code', $phrase);
        //生成图片
        return $this->response->raw($builder->inline());
    }

    /**
     * 退出
     * @RequestMapping(path="/logout", methods="get")
     */
    public function logout(){
        $this->session->clear();
        $cookie = new Cookie('cookie_x-rbac-token', '',time() - 3600,'/',env('DOMAIN','.bigoffs.cn'),false,true);
        return $this->response->withCookie($cookie)->redirect(getLoginUrl($this->request));
    }
}
