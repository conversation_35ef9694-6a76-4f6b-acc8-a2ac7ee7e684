<?php

namespace App\Library\Facades;


use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\TemplateSetServiceInterface;
use Hyperf\Guzzle\ClientFactory;

class TemplateSetService extends Facade
{
    const DOWN_URL = '/excel/exportTpl';

    const TYPE_INSTORE = 'import_instore';
    const TYPE_ALLOT_INSTORE = 'import_allot_instore';
    const TYPE_BATCH_ALLOT_IN = 'batch_allot_in';
    const TYPE_ALLOT_PACK_SIGN = 'allot_pack_sign';
    const TYPE_CHANGE_BARCODE = 'change_barcode';
    const TYPE_BAT_CHANGE_BARCODE = 'bat_change_barcode';
    const TYPE_BATCH_ALLOT_SIGN = 'bat_allot_sign';
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor ()
    {
        return TemplateSetServiceInterface::class;
    }

    /**
     * 获取模板列表
     * @param $name
     */
    public static function tplList($name){
        if(!$name){
            return [];
        }
        $result = static::getTemplate($name);
        if(!$result){
            return [];
        }
        $table_tpl_list = json_decode($result['table_header'],true);
        $tpl_list = [];
        foreach ($table_tpl_list as $key=>$table_tpl) {
            $tpl_list[$key] = [
                'title' => $table_tpl['tpl_name'],
                'down_url' => self::DOWN_URL.'?'.\http_build_query(['type'=>$key,'name'=>$name]),
                'remark' => $table_tpl['val']
            ];
        }
        return $tpl_list;
    }
}
