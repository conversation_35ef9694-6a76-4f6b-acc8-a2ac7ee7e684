<?php


namespace App\JsonRpc;


interface SendServiceInterface
{
    /**
     * 短信消息发送
     * @param array $params['mobile'=>手机号,'temp_id'=>模板id,'args'=>[参数键值对]]
     *        temp_id: 1为注册验证码(参数code)，2业务通知通用类消息(待开通)，3活动通知通用类消息(待开通)
     * @return mixed
     */
    public function sms(array $params);

    /**
     * 钉钉消息发送
     * @param array $params['to_target'=>送达人，多个用逗号分隔,'context'=>内容,'c_type'=>'text','time'=>日期]
     * @return mixed
     */
    public function dingtalk(array $params);

    /**
     * 钉钉消息发送
     * @param array $content
     * @return mixed
     */
    public function errorNotify($content);

    /**
     * 发送邮件
     * @param array $params
     * @return mixed
     */
    public function email(array $params);
}