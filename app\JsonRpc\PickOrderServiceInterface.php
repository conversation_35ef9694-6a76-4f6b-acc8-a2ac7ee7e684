<?php
declare(strict_types=1);

namespace App\JsonRpc;

interface PickOrderServiceInterface
{
    /**
     * 获取拣货单列表
     * @param array $params
     * @return mixed
     */
    public function getPickOrderLists (array $params);

    /**
     * 获取拣货单详情
     * @param int $id
     * @return mixed
     */
    public function getPickOrderDetail (int $id);

    /**
     * 根据拣货单号获取拣货单详情
     * @param string $serialNo
     * @param array|string[] $field
     * @return mixed
     */
    public function getPickOrderBySerialNoDetail (string $serialNo, array $field = ['*']);

    /**
     * 获取拣货单详情明细列表
     * @param array $params
     * @return mixed
     */
    public function getPickTaskDetailLists (array $params);

    /**
     * 获取详情列表
     * @param $where
     * @return array|\Hyperf\Database\Model\Builder[]|\Hyperf\Database\Model\Collection
     */
    public function getPickOrderDetailLists ($where);

    /**
     * 拣货单出库
     * $data = [
     *  'pick_order_data' => [],
     *  'pick_order_detail' => [],
     *  'out_store_detail' => []
     * ]
     */
    public function pickOrderOut(array $params);

    /**
     * 修改拣货单状态
     * @param int $id
     * @param array $data
     * @return mixed
     */
    public function updatePickOrder (int $id, array $data);

    /**
     * 创建拣货单
     * @param array $outIds
     * @param array $stockArea
     * @param int $outNumber
     * @param array $extras
     * @return mixed
     */
    public function createPickOrder (array $outIds, array $stockArea, int $outNumber, array $extras);

    /**
     * 获取唯一码相关数据
     * @param array $where
     * @return mixed
     */
    public function getOrderSaleUniqueInfo(array $where);

    /**
     * 获取条码相关数据
     * @param array $where
     * @return mixed
     */
    public function getOrderSaleBarcodeInfo(array $where);

    /**
     * 获取订单货架号信息
     * @param array $where
     * @return mixed
     */
    public function getOrderSaleShelfCodeInfo(array $where);

    /**
     * 创建拣货单
     * @param array $data
     * @return mixed
     */
    public function create(array $data);

    /**
     * 修复拣货和生成出库任务并发时导致店内码状态异常问题
     * @param $data
     * @return mixed
     */
    public function repairPickUniqueCodes($data);

    /**
     * 导出拣货单明细
     * @param $data
     * @return mixed
     */
    public function exportListDetails($data);
}