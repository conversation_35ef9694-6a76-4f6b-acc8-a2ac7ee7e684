<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use App\Constants\ErrorCode;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Constants\SerialType;
use App\Exception\BusinessException;
use App\Exception\ValidateException;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\CategoryServiceInterface;
use App\JsonRpc\CrontabServiceInterface;
use App\JsonRpc\InStoreServiceInterface;
use App\JsonRpc\SerialNoServiceInterface;
use App\JsonRpc\ShelfGoodsCodeMapServiceInterface;
use App\JsonRpc\ShelfServiceInterface;
use App\JsonRpc\SkuBarcodeServiceInterface;
use App\JsonRpc\StockAdjustServiceInterface;
use App\JsonRpc\StServiceInterface;
use App\JsonRpc\WmsStocktakingServiceInterface;
use App\Library\Facades\StService;
use App\Library\St\Export\StResult;
use App\Library\St\StCommon;
use App\Library\St\Tally\StTally;
use App\Library\Tally\TallyConstants;
use App\Model\StDeploy;
use App\Service\HashService;
use Hyperf\Amqp\Producer;
use Hyperf\Amqp\RpcClient;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Utils\Context;
use Psr\Http\Message\ServerRequestInterface;

/**
 * @Controller()
 */
class StController extends AbstractController
{
    const EXPORT_BATCH_SIZE = 6000;

    /**
     * @Inject ()
     * @var SkuBarcodeServiceInterface
     */
    private $SkuBarcodeService;

    /**
     * @Inject ()
     * @var ShelfServiceInterface
     */
    private $ShelfService;

    /**
     * @Inject ()
     * @var HashService
     */
    private $HashService;


    /**
     * @Inject ()
     * @var ShelfGoodsCodeMapServiceInterface
     */
    private $ShelfGoodsCodeMapService;
    /**
     * @Inject()
     * @var \App\JsonRpc\WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject()
     * @var \App\JsonRpc\BrandServiceInterface
     */
    private $brnadService;

    /**
     * @Inject ()
     * @var StockAdjustServiceInterface
     */
    private $StockAdjustService;

    /**
     * @Inject
     * @var CategoryServiceInterface
     */
    private $CategoryService;

    /**
     * @Inject ()
     * @var Producer
     */
    private $Producer;
    /**
     * @Inject ()
     * @var InStoreServiceInterface
     */
    private $InStoreService;

    /**
     * @Inject ()
     * @var CrontabServiceInterface
     */
    private $CrontabService;

    /**
     * @Inject ()
     * @var HashService
     */
    private $hashService;

    /**
     * @Inject ()
     * @var AdminServiceInterface
     */
    private $AdminService;

    /**
     * @Inject ()
     * @var WmsStocktakingServiceInterface
     */
    private $WmsStocktakingService;

    /**
     * @Inject ()
     * @var StServiceInterface
     */
    private $StService;

    /**
     * @Inject ()
     * @var SerialNoServiceInterface
     */
    private $SerialNoService;

    const UPLOAD_SUFFIX = '_spc';

    /**
     * 盘点任务列表
     * @RequestMapping(path="/st/taskList", methods="get,post")
     */
    public function getTaskList(RequestInterface $request)
    {
        // 删除防重复点击token
        $adminInfo = $this->session->get('userInfo');
        delIdempotenceToken($adminInfo['uid']);

        $authWIds = getAuthWIds();
        if ($request -> isMethod('post')) {
            $params = $request -> all();
            // 登录用户
            $userInfo = $this->session->get('userInfo');

            try {
                $where = $params['where'] ?? [];
                $page = $params['page'] ?? 1;
                $limit = $params['limit'] ?? 10;
                //$where = $this -> getTempData();

                $ret = $this -> StService -> getStocktakingTaskList($authWIds,(int)$page, (int)$limit, $where);
                $ret['token'] = getIdempotenceToken($userInfo['uid']);

                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
            } catch (\Exception $exception) {
                $ret = $exception -> getMessage();
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
            }
        }
        //获取仓库信息
        $warehouse_list = $this->WarehouseService->getWarehouses(['ids'=>$authWIds],['id','name']);
        //品牌信息
        $brand_list = $this->brnadService->getBrands();
        $data = [
            'shelf_list' => [],
            'brand_list' => $brand_list,
            'cate_list' => [],
        ];

        return $this -> show("/st/taskList",$data);

    }

    /**
     * 新建盘点任务
     * @RequestMapping(path="/st/getDemoData", methods="get,post")
     *
     */
    public function getDemoData(RequestInterface $request)
    {
        $data = json_decode('[
	{ 
		"id":1,
		"name":"父节点1 - 展开",
		"open":true,
		"children": [
			{ 
				"id":2,
				"name":"父节点11 - 展开",
				"open":true,
				"children": [
					{ 
						"id":3,
						"name":"叶子节点111"
					},
					{ 
						"id":4,
						"name":"叶子节点112"
					},
					{ 
						"id":5,
						"name":"叶子节点113"
					},
					{ 
						"id":6,
						"name":"叶子节点114"
					}
				]
			},
			{ 
				"id":7,
				"name":"父节点12 - 折叠",
				"children": [
					{ 
						"id":8,
						"name":"叶子节点121"
					},
					{ 
						"id":9,
						"name":"叶子节点122"
					},
					{ 
						"id":10,
						"name":"叶子节点123"
					},
					{ 
						"id":11,
						"name":"叶子节点124"
					}
				]
			},
			{ 
				"id":12,
				"name":"父节点13 - 没有子节点",
				 "isParent":true
			}
		]
	},
	{ 
		"id":13,
		"name":"父节点2 - 折叠",
		"children": [
			{ 
				"id":14,
				"name":"父节点21 - 展开", 
				"open":true,
				"children": [
					{ 
						"id":15,
						"name":"叶子节点211"
					},
					{ 
						"id":16,
						"name":"叶子节点212"
					},
					{ 
						"id":17,
						"name":"叶子节点213"
					},
					{ 
						"id":18,
						"name":"叶子节点214"
					}
				]
			},
			{ 
				"id":19,
				"name":"父节点22 - 折叠",
				"children": [
					{ 
						"id":20,
						"name":"叶子节点221"
					},
					{ 
						"id":21,
						"name":"叶子节点222"
					},
					{ 
						"id":22,
						"name":"叶子节点223"
					},
					{ 
						"id":23,
						"name":"叶子节点224"
					}
				]
			},
			{ 
				"id":24,
				"name":"父节点23 - 折叠",
				"children": [
					{ 
						"id":25,
						"name":"叶子节点231"
					},
					{ 
						"id":26,
						"name":"叶子节点232"
					},
					{ 
						"id":27,
						"name":"叶子节点233"
					},
					{ 
						"id":28,
						"name":"叶子节点234"
					}
				]
			}
		]
	},
	{ 
		"id":29,
		"name":"父节点3 - 没有子节点", 
		"isParent":true
	}

]',true);
        return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$data);
    }

    /**
     * 新建盘点任务
     * @RequestMapping(path="/st/addTask", methods="get,post")
     *
     */
    public function addTask(RequestInterface $request)
    {
        if ($request -> isMethod("post")) {
            $params = $request -> all();
            $validator = validate()->make($params, [
                'name' => 'required|string',
                'w_id' => 'required|numeric|gt:0',
                'st_range_key_id' => 'required|numeric|gt:0',
//                'value_id' => 'required|numeric|gt:0',
                'st_type' => 'required|numeric|in:1,2,3',
                'start_time' => 'required|string',
                'end_time' => 'required|string',
//                'info_ids' => 'required|string',
            ],[
                'name.required'        => '盘点名称必填',
                'name.string'         => '盘点名称必须字符串',
                'start_time.required'        => '盘点申请开始时间必填',
                'start_time.string'         => '盘点申请开始时间必须是字符串',
                'end_time.required'        => '盘点申请结束始时间必填',
                'end_time.string'         => '盘点申请结束时间必须是字符串',
                'w_id.required'        => '仓库ID必填',
                'st_range_key_id.required'        => '抽盘范围key_id必填',
//                'value_id.required'        => '抽盘范围value_id值必填',
                'w_id.integer'         => '仓库ID必须为正整数',
                'st_type.required'        => '盘点类型必填',
                'st_type.integer'         => '盘点类型必须为正整数',
//                'info_ids.required'         => '请传入盘点范围数据',
//                'info_ids.string'         => '盘点范围数据只能是字符串',
            ]);

            if ($validator->fails()) {
                return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
            }

            // 登录用户
            $userInfo = $this->session->get('userInfo');
            if(!$userInfo) {
                return $this->returnApi( ErrorCode::REQUEST_ERROR, '请登录' );
            }

            try {
                $params['admin_id'] = $userInfo['uid'];
                $params['admin_name'] = $userInfo['nickname'];
                $params['st_no'] = $this -> SerialNoService -> generate(SerialType::I,(int)$params['st_type']);

                $handledInfoIds = $this -> handleInfoIds($params['info_ids'],$params['value_id']);

                //$data['st_status'] = empty($data['st_status']) || !isset($data['st_status']) ? 1 : $data['st_status'] ;
                $params['info_ids'] = $handledInfoIds;
                $ret = $this -> StService -> addTask($params);

                /********************************************记录日志****************************************************/
                // 当前登录账户数据
                $userInfo = $this->session->get('userInfo');
                // 记录日志数据
                $logData = [
                    'snow_id' => $this->request->getAttribute('snow_id'),
                    'op_id' => $ret['st_id'],
                    'op_type' => '创建新盘点任务', // 操作类型
                    'op_content' => '盘点ID：'.$ret['st_id'], // 操作内容
                    'op_time' => date('Y-m-d H:i:s'), // 操作时间
                    'model_name' => 'st', // 操作模块
                    'st_status' => 1,  // 盘点状态: 1=初盘中，2=复盘中，3=待审核，4=已完成，5=已作废，6=驳回
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                    'remark' => "创建盘点任务，盘点ID：".$ret['st_id']
                ];
                wlog((string)$logData['snow_id'], $logData);
                logger() -> debug('创建任务日志记录：', [$logData]);
                /********************************************记录日志****************************************************/
                return $this -> returnApi(ResponseCode::SUCCESS,$ret['msg'],$ret['result']);
            } catch (\Exception $exception) {
                $ret = $exception -> getMessage();
                logger() -> debug('创建任务失败：', [$ret]);
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
            }

        }
        return $this -> show("/stocktaking/addTask");
    }

    /**
     * 完成盘点
     * @param RequestInterface $request
     * @RequestMapping(path="/st/updateStatus", methods="get,post")
     */
    public function updateStatus(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'st_id' => 'required|gt:0',
            'st_status' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        // 当前登录账户数据
        $userInfo = $this->session->get('userInfo');
        $stId = $params['st_id'] ?? 0;
        $stStatus = $params['st_status'] ?? 0;
        $opType = $this -> getOpType($stStatus);
        try {
            $detail = $this -> StService -> getTaskOne(intval($stId));
            logger() ->debug('detail=======',[$detail]);
            if(empty($detail)){
                return $this->returnApi( ErrorCode::REQUEST_ERROR, '盘点任务不存在');
            }

            // 如果完成复盘时，盘点盈亏都是0，则直接将盘点状态改为已完成 st_status = 5

            $stResult = [];
            $pNumTotal = 0;
            $lNumTotal = 0;
            $stResultC = collect();
            if ($stStatus == 4) {
                // 查询盘点任务综合结果
                $stResult = $this -> StService -> getStResultSummaryByIdForStockAdjust($stId);
                $stResultC = collect($stResult);
                $pNumTotal = $stResultC -> sum('p_num_total');
                $lNumTotal = $stResultC -> sum('l_num_total');
                logger() -> debug('改状态前查看盘点盈亏',[$stResult]);
                logger() -> debug('盘点盈亏总和',[$pNumTotal,$lNumTotal]);
                if (($pNumTotal == 0 && $lNumTotal == 0) || 0 == $detail['is_adjust']){
                    logger() -> debug('改状态前-盈亏数据===均为0==');
                    $stStatus = 5;
                    $opType = $this -> getOpType($stStatus);
                }
            }
            // 更改盘点状态
            $ret = $this -> StService -> updateStatus($stId,['st_status' => $stStatus]);
            logger() -> debug('调整单创建前更改状态',[$stStatus]);

            //创建库存调整单: st_status=4 即 只有在状态是 复盘完成时，才生成库存调整单
            if (1 == $detail['is_adjust']) { // 是否进行库存调整 1=是，0=否
                logger() -> debug('库存调整开启',[$stResult]);

                if(4==$stStatus && $stResult && ($pNumTotal > 0 || $lNumTotal > 0)){
                    if ($detail['st_type'] != 2) { // 条码不走此逻辑
                        // 盘盈店内码若在生产区，抛出提示
                        $pData = $stResultC -> where('st_result_type',2) -> first();
                        if ($pData) {
                            logger() -> debug('盘盈店内码',[$pData['goods_codes']]);
                            $produceAreaCodes = $this -> StService -> checkProductArea($pData['goods_codes']);
                            if ($produceAreaCodes) {
                                throw new BusinessException(sprintf('生产区未完成店内码[（%s）]禁止盘盈',join(',', $produceAreaCodes)));
                            }
                        }

                        // 校验是否存在实时状态为锁定的店内码
                        // st_result_type： 2 ：盘盈，3：盘亏
                        $lData = $stResultC -> where('st_result_type',3) -> first();
                        if ($lData) {
                            logger() -> debug('盘亏店内码',[$lData['goods_codes']]);
                            $lockUniques = $this -> ShelfGoodsCodeMapService -> getUniqueCodes(['status'=>[2,3],'unique_codes'=>$lData['goods_codes']]);
                            $lockUniques = array_column($lockUniques,'unique_code');
                            logger() -> debug('盘亏锁定/出库店内码',[$lockUniques]);
                            if (!empty($lockUniques)) {
                                throw new BusinessException(sprintf('盘亏锁定/出库店内码（%s），请先解锁/动盘盘亏店内码',join(',', $lockUniques)));
                            }
                        }
                        // 如果镜像有新品采购差异，需要将其sku_id刷成店内码表中的最新sku_id
                        $this -> StService -> updateStBillSkuId((int)$stId);
                    }
                    StCommon::createStockAdjust($stId,$detail);
                }
            }

            // 如果走库存调整，则串位理货,- 理货抛异常记录日志并自动跳过，不要卡着复盘没法完成 - 20230208 yj - 20230208 yj
            if (1 == $detail['is_adjust'] && in_array($stStatus,[4,5])) {
                try {
                    logger() -> debug('串位理货');
                    StTally::forUnique([
                        'st_id' => $stId,
                        'w_id'=>$detail['w_id'],
                        'admin_info' => [
                            'admin_id' => 0,//$detail['admin_id'],
                            'admin_name' => '系统-盘点',//$detail['admin_name']
                            'tally_from' => TallyConstants::TALLY_FROM_ST
                        ]
                    ]);
                } catch (\Exception $exception) {
                    // 串位理货失败，记录日志
                    $logData = [
                        'snow_id' => $this->request->getAttribute('snow_id'),// newSnowId(),
                        'op_id' => $stId,
                        'op_type' => '盘点串位理货', // 操作类型
                        'op_content' => '盘点串位理货失败：'.$exception -> getMessage(), // '盘点ID：'.$ret['st_id'], // 操作内容
                        'op_time' => date('Y-m-d H:i:s'), // 操作时间
                        'model_name' => 'st', // 操作模块
                        'st_status' => $params['st_status'], // 创建任务，默认盘点中 new 盘点状态: 0=草稿,1=即将开始，2=正在预盘，3=正在复盘，4=复盘完成，5=已完成，6=已作废  // 盘点状态: 1=待盘点，2=盘点中，3=待审核，4=已完成，5=已作废，6=驳回
                        'admin_id' => $userInfo['uid'],
                        'admin_name' => $userInfo['nickname'],
                        'remark' => '盘点复盘完成后串位理货失败：'.$exception -> getMessage()
                    ];
                    logger() -> info('串位理货失败记录日志',[$logData]);
                }
            }

            /********************************************记录日志****************************************************/
            // 记录日志数据
            $logData = [
                'snow_id' => $this->request->getAttribute('snow_id'),
                'op_id' => $stId,
                'op_type' => $opType, // 操作类型
                'op_content' => '', // '盘点ID：'.$ret['st_id'], // 操作内容
                'op_time' => date('Y-m-d H:i:s'), // 操作时间
                'model_name' => 'st', // 操作模块
                'st_status' => $params['st_status'], // 创建任务，默认盘点中 new 盘点状态: 0=草稿,1=即将开始，2=正在预盘，3=正在复盘，4=复盘完成，5=已完成，6=已作废  // 盘点状态: 1=待盘点，2=盘点中，3=待审核，4=已完成，5=已作废，6=驳回
                'admin_id' => $userInfo['uid'],
                'admin_name' => $userInfo['nickname'],
                'remark' => '' // $ret ? '成功' : '失败' // PublicCode::ST_STATUS[$params['st_status']]
            ];
            wlog((string)$logData['snow_id'], $logData);
            logger() -> debug('操作成功记录日志数据',[$logData]);
            /********************************************记录日志****************************************************/

            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            logger() -> info('店内码更新状态失败',[$exception ->getMessage()]);
            // 记录日志数据
            $logData = [
                'snow_id' => $this->request->getAttribute('snow_id'),
                'op_id' => $stId,
                'op_type' => $opType, // 操作类型
                'op_content' => '操作失败：'.$ret,
                'op_time' => date('Y-m-d H:i:s'), // 操作时间
                'model_name' => 'st', // 操作模块
                'st_status' => $params['st_status'], // 创建任务，默认盘点中 new 盘点状态: 0=草稿,1=即将开始，2=正在预盘，3=正在复盘，4=复盘完成，5=已完成，6=已作废  // 盘点状态: 1=待盘点，2=盘点中，3=待审核，4=已完成，5=已作废，6=驳回
                'admin_id' => $userInfo['uid'],
                'admin_name' => $userInfo['nickname'],
                'remark' => '操作失败：'.$ret
            ];
            wlog((string)$logData['snow_id'], $logData);
            logger() -> info('记录失败日志数据',[$logData]);
            // 有异常，盘点状态回滚，并抛出异常
            StService::updateStatus($stId,['st_status' => 3]);
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    public function handleInfoIds($infoIds,$stRangeValueId)
    {
        //1 货架
        //2	品牌
        //3	类目
        //4	条码
        //5	店内码
        if (
            $stRangeValueId == PublicCode::ST_VALUE_ID['shelf_line'] ||
            $stRangeValueId == PublicCode::ST_VALUE_ID['brand'] ||
            $stRangeValueId == PublicCode::ST_VALUE_ID['category']
        ) {
            return explode(',',$infoIds);
        }

        return explode(PHP_EOL,$infoIds);
    }

    /**
     * 任务详情 - 货架分布
     * @RequestMapping(path="/st/shelfDistributionList", methods="get,post")
     */
    public function getShelfDistributionList(RequestInterface $request)
    {
        $params = $request -> all();
        $page = $params['page'] ?? 1;
        $limit= $params['limit'] ?? 10;
        $stId = $params['st_id'] ?? 0;
        try {
            $result = $this -> StService -> getShelfDistributionList($page, $limit, $stId);
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 任务详情 - 货架分布
     * @RequestMapping(path="/st/shelfDistributionAll", methods="get,post")
     */
    public function shelfDistributionAll(RequestInterface $request)
    {
        $params = $request -> all();
        $stId = $params['st_id'] ?? 0;
        try {
            $result = $this -> StService -> getShelfDistributionAll($stId);
            foreach ($result as &$v) {
                $v['name'] = $v['shelf_line'];
                $v['value'] = $v['shelf_line'];
            }
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 任务部署
     * @RequestMapping(path="/st/deploy", methods="get,post")
     */
    public function deploy(RequestInterface $request)
    {
        $params = $request->all();

        $validator = validate()->make($params, [
            'st_id' => 'required|numeric|gt:0',
            'shelf_codes' => 'required|string',
            'first_admin_id' => 'required|numeric|gt:0',
            'first_admin_name' => 'required|string',
            'second_admin_id' => 'required|numeric|gt:0',
            'second_admin_name' => 'required|string',
        ], [
            'st_id.required' => '盘点ID必填',
            'st_id.integer' => '盘点ID必须为正整数',
            'shelf_codes.required' => '货架排必填',
            'shelf_codes.integer' => '货架排必须为字符串',
            'first_admin_id.required' => '预盘人ID必填',
            'first_admin_id.integer' => '预盘人ID正整数',
            'first_admin_name.required' => '预盘人名称必填',
            'first_admin_name.integer' => '预盘人名称必须为字符串',
            'second_admin_id.required' => '复盘人ID必填',
            'second_admin_id.integer' => '复盘人ID必须为正整数',
            'second_admin_name.required' => '复盘人名称必填',
            'second_admin_name.integer' => '复盘人名称必须为字符串',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        try {
//            $params['shelf_codes'] = explode(",",$params['shelf_codes']);
            $params['deploy_no'] = $this -> SerialNoService -> generate(SerialType::I);
            $ret = $this->StService->deploy($params);
            return $this->returnApi(ErrorCode::SUCCESS, "操作成功", $ret);
        } catch (\Exception $exception) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
        }

    }

    /**
     * 任务部署编辑
     * @RequestMapping(path="/st/deployEdit", methods="get,post")
     */
    public function deployEdit(RequestInterface $request)
    {
        $params = $request->all();

        $validator = validate()->make($params, [
            'id' => 'required|numeric|gt:0',
            'st_id' => 'required|numeric|gt:0',
            'shelf_codes' => 'required|string',
            'first_admin_id' => 'required|numeric|gt:0',
            'first_admin_name' => 'required|string',
            'second_admin_id' => 'required|numeric|gt:0',
            'second_admin_name' => 'required|string',
        ], [
            'id.required' => '部署ID必填',
            'id.integer' => '部署ID必须为正整数',
            'st_id.required' => '盘点ID必填',
            'st_id.integer' => '盘点ID必须为正整数',
            'shelf_codes.required' => '货架号必填',
            'shelf_codes.string' => '货架号必须为字符串',
            'first_admin_id.required' => '预盘人ID必填',
            'first_admin_id.integer' => '预盘人ID正整数',
            'first_admin_name.required' => '预盘人名称必填',
            'first_admin_name.integer' => '预盘人名称必须为字符串',
            'second_admin_id.required' => '复盘人ID必填',
            'second_admin_id.integer' => '复盘人ID必须为正整数',
            'second_admin_name.required' => '复盘人名称必填',
            'second_admin_name.integer' => '复盘人名称必须为字符串',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        try {
            $ret = $this->StService->editDeploy($params);
            return $this->returnApi(ErrorCode::SUCCESS, "操作成功", $ret);
        } catch (\Exception $exception) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
        }
    }
    /**
     * 任务部署
     * @RequestMapping(path="/st/deployList", methods="get,post")
     */
    public function deployList(RequestInterface $request)
    {
        $params = $request->all();
        $validator = validate()->make($params, [
            'st_id' => 'required|numeric|gt:0',
        ], [
            'st_id.required' => '盘点ID必填',
            'st_id.integer' => '盘点ID必须为正整数',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        try {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;
            $ret = $this->StService->deployList($params['st_id'],$page,$limit);
            return $this->returnApi(ErrorCode::SUCCESS, "操作成功", $ret);
        } catch (\Exception $exception) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
        }
    }

    /**
     * 获取盘点统计数据
     * @RequestMapping(path="/st/statistics", methods="get,post")
     */
    public function statistics(RequestInterface $request)
    {
        $params = $request->all();
        $validator = validate()->make($params, [
            'id' => 'required|numeric|gt:0',
        ], [
            'id.required' => '盘点ID必填',
            'id.integer' => '盘点ID必须为正整数',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        try {
            $statistics = $this->StService->statistics($params['id']);
            $statistics['process'] = ($statistics['process']*100).'%';
            $statistics['diff_proportion'] = ($statistics['diff_proportion']*100).'%';

            return $this->returnApi(ErrorCode::SUCCESS, "操作成功", $statistics);
        } catch (\Exception $exception) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
        }

    }
    /**
     * 任务详情
     * @RequestMapping(path="/st/detail", methods="get,post")
     */
    public function detail(RequestInterface $request)
    {
        $params = $request->all();
        $validator = validate()->make($params, [
            'id' => 'required|numeric|gt:0',
        ], [
            'id.required' => '盘点ID必填',
            'id.integer' => '盘点ID必须为正整数',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        try {
            $detail = $this->StService->detail($params['id']);
            if ($request->isMethod("post")) {
                return $this->returnApi(ErrorCode::SUCCESS, "操作成功", $detail);
            }

            return $this->show('st/detail',[
                'detail' => $detail,
                //'statistics' => $statistics
            ]);
//            return $this->returnApi(ErrorCode::SUCCESS, "操作成功", $ret);
        } catch (\Exception $exception) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
        }

    }

    /**
     * 获取任务清单列表
     * @RequestMapping(path="/st/billList", methods="get,post")
     */
    public function billList(RequestInterface $request)
    {
        $params = $request->all();
        $validator = validate()->make($params, [
            'st_id' => 'required|numeric|gt:0',
        ], [
            'st_id.required' => '盘点ID必填',
            'st_id.integer' => '盘点ID必须为正整数',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;
        try {
            $ret = $this->StService->billList($params['st_id'],$page,$limit);
            return $this->returnApi(ErrorCode::SUCCESS, "操作成功", $ret);
        } catch (\Exception $exception) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
        }
    }


    /**
     * 获取单据统计摘要
     * @RequestMapping(path="/st/statistics", methods="get,post")
     */
//    public function statistics(RequestInterface $request)
//    {
//        $params = $request->all();
//        $validator = validate()->make($params, [
//            'st_id' => 'required|numeric|gt:0',
//        ], [
//            'st_id.required' => '盘点ID必填',
//            'st_id.integer' => '盘点ID必须为正整数',
//        ]);
//
//        if ($validator->fails()) {
//            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
//        }
//
//        try {
//            $ret = $this->StService->statistics($params['st_id']);
//            return $this->returnApi(ErrorCode::SUCCESS, "操作成功", $ret);
//        } catch (\Exception $exception) {
//            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
//        }
//    }

    /**
     * 获取单据统计摘要
     * @RequestMapping(path="/st/changeAdmin", methods="get,post")
     */
    public function changeAdmin(RequestInterface $request)
    {
        $params = $request->all();
        $validator = validate()->make($params, [
            'deploy_id' => 'required|numeric|gt:0',
            'admin_type' => 'required|in:1,2',
            'admin_id' => 'required|numeric|gt:0',
            'admin_name' => 'required|string',
        ], [
            'deploy_id.required' => '部署ID必填',
            'deploy_id.integer' => '部署ID必须为正整数',
            'admin_type.required' => '管理人员类型必填',
            'admin_type.in' => '管理人员类型必须为1或2',
            'admin_id.required' => '管理人员ID必填',
            'admin_id.integer' => '管理人员ID必须为正整数',
            'admin_name.required' => '管理人员姓名必填',
            'admin_name.string' => '管理人员姓名必须为字符串',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        try {
            $ret = $this->StService->changeAdmin($params['deploy_id'],$params['admin_type'],$params['admin_id'],$params['admin_name']);
            return $this->returnApi(ErrorCode::SUCCESS, "操作成功", $ret);
        } catch (\Exception $exception) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
        }
    }

    /**
     * 接单
     * @RequestMapping(path="/st/acceptTask", methods="get,post")
     */
    public function acceptTask(RequestInterface $request)
    {
        $params = $request->all();
        $validator = validate()->make($params, [
            'deploy_id' => 'required|numeric|gt:0',
            'admin_type' => 'required|in:1,2',
            'admin_id' => 'required|numeric|gt:0',
        ], [
            'deploy_id.required' => '部署ID必填',
            'deploy_id.integer' => '部署ID必须为正整数',
            'admin_type.required' => '管理人员类型必填',
            'admin_type.in' => '管理人员类型必须为1或2',
            'admin_id.required' => '管理人员ID必填',
            'admin_id.integer' => '管理人员ID必须为正整数',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        try {
            $ret = $this->StService->acceptTask($params['deploy_id'],$params['admin_type'],$params['admin_id']);
            return $this->returnApi(ErrorCode::SUCCESS, "操作成功", $ret);
        } catch (\Exception $exception) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
        }
    }

    /**
     * 上传盘点数据
     * @RequestMapping(path="/st/uploadStData", methods="get,post")
     */
    public function uploadStData(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'w_id' => 'required|integer',
            'st_id' => 'required|integer',
            'st_type' => 'required|integer',
        ],[
            'w_id.required'=>'仓库ID（w_id）必须',
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_type.required'=>'盘点类型（st_type）必须',
        ]);

        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        // 校验st_type是否与盘点任务匹配
        $task = $this -> StService -> getTaskOne($params['st_id']);
        if (!$task) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,"盘点任务不存在");
        }

        if ($task['st_type'] != $params['st_type']) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,"盘点任务类型不匹配");
        }

        $userInfo = $this -> session -> get('userInfo');
        $redisKey = $this -> _makeRedisKey($params,$userInfo);
        $tplKey = $redisKey['tpl_key'];
        $redisDataKey = $redisKey['data_key'];
        $redisNormalDataKey = $redisKey['normal_data_key'];
        $redisEDataKey = $redisKey['e_data_key'];
        $redisEDataAllKey = $redisKey['e_data_all_key'];
        $redisInfoKey = $redisKey['info_key'];
        // 先清除上次的数据
        $redis = redis();
        $this -> _clearRedisData ($redis,$redisDataKey,$redisNormalDataKey,$redisEDataKey,$redisEDataAllKey,$redisInfoKey);

        try {

            if (isset($params['st_type']) && 5 == $params['st_type'] && 1 != $params['st_way']) {
                return $this -> returnApi(ErrorCode::REQUEST_ERROR,"【按店内码】盘点的录入数据类型必须为【店内码】",'');
            }

            if (isset($params['st_type']) && 6 == $params['st_type']) {
                return $this -> returnApi(ErrorCode::REQUEST_ERROR,"【按RFID】盘点的数据请通过PDA系统录入",'');
            }

            if (isset($params['st_type']) && 7 == $params['st_type'] && 2 != $params['st_way']) {
                return $this -> returnApi(ErrorCode::REQUEST_ERROR,"【按条形码】盘点的录入数据类型必须为【条形码】",'');
            }

            if (!$request->hasFile('file')) {
                return $this->returnApi(ErrorCode::REQUEST_FILE_ERROR, ErrorCode::getMessage(ErrorCode::REQUEST_FILE_ERROR));
            }

            $txtData = $this -> _getTxtData($request,$tplKey);

            // 检查操作人是否都存在，不存在的话，终止执行
            $adminIdsFromUpload = array_column($txtData,'admin_id');
            $admins = $this -> AdminService -> idsToNameList($adminIdsFromUpload);
            $diffs = array_diff($adminIdsFromUpload,array_keys($admins));
            if (!empty($diffs)) {
                return $this -> returnApi(ErrorCode::REQUEST_ERROR,'id为：'.join('，',array_unique($diffs)).'的理货人不存在','');
            }

            $handledData = $this -> handleData($params['w_id'],$params['st_type'],$txtData);
            $this -> _saveDataToRedis($redis,$redisInfoKey,$redisDataKey,$redisNormalDataKey,$redisEDataKey,$handledData);

            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功");
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    private function _getTxtData($request,$tplKey) {
        $file = $request->file('file');
        $file -> getPathInfo();

        $metaData = getTemplateInfo($tplKey);

        $metaData = explode(',',$metaData['key']);
        try {
            return readTxt($file, $metaData);
        } catch (ValidateException $e) {
            throw new BusinessException('txt文件校验失败');
        }
    }

    private function _makeRedisKey($params,$userInfo) {
        $tplKey = ''; // 上传模板key
        $redisDataKey = '';
        $redisNormalDataKey = '';
        $redisEDataKey = '';
        $redisEDataAllKey = '';
        $redisInfoKey = '';

        switch ($params['st_type']) {
            case 1:
                $redisDataKey = $userInfo['uid'].'_st_data_unique_code'.'_'.$params['st_id'];
                $redisNormalDataKey = $userInfo['uid'].'_st_normal_data_unique_code'.'_'.$params['st_id'];
                $redisEDataKey = $userInfo['uid'].'_st_e_data_unique_code'.'_'.$params['st_id'];
                $redisEDataAllKey = $userInfo['uid'].'_st_e_data_all_unique_code'.'_'.$params['st_id'];
                $redisInfoKey = $userInfo['uid'].'_st_info_unique_code'.'_'.$params['st_id'];
                $tplKey = 'stocktaking_unique_code_txt';
                break;
            case 2:
                $redisDataKey = $userInfo['uid'].'_st_data_barcode'.'_'.$params['st_id'];
                $redisNormalDataKey = $userInfo['uid'].'_st_normal_data_barcode'.'_'.$params['st_id'];
                $redisEDataKey = $userInfo['uid'].'_st_e_data_barcode'.'_'.$params['st_id'];
                $redisEDataAllKey = $userInfo['uid'].'_st_e_data_all_barcode'.'_'.$params['st_id'];
                $redisInfoKey = $userInfo['uid'].'_st_info_barcode'.'_'.$params['st_id'];
                $tplKey = 'stocktaking_barcode_txt';
                break;
            case 3:
                $redisDataKey = $userInfo['uid'].'_st_data_epc_code'.'_'.$params['st_id'];
                $redisNormalDataKey = $userInfo['uid'].'_st_normal_data_epc_code'.'_'.$params['st_id'];
                $redisEDataKey = $userInfo['uid'].'_st_e_data_barcode'.'_'.$params['st_id'];
                $redisEDataAllKey = $userInfo['uid'].'_st_e_data_all_epc_code'.'_'.$params['st_id'];
                $redisInfoKey = $userInfo['uid'].'_st_info_epc_code'.'_'.$params['st_id'];
                $tplKey = 'stocktaking_epc_code_txt';
                break;
        }

        return [
            'tpl_key' => $tplKey,
            'data_key' => $redisDataKey,
            'normal_data_key' => $redisNormalDataKey,
            'e_data_key' => $redisEDataKey,
            'e_data_all_key' => $redisEDataAllKey,
            'info_key' => $redisInfoKey,
        ];
    }

    private function _clearRedisData ($redis,$redisDataKey,$redisNormalDataKey,$redisEDataKey,$redisEDataAllKey,$redisInfoKey){
        $redis -> unlink($redisDataKey);
        $redis -> unlink($redisNormalDataKey);
        $redis -> unlink($redisEDataKey);
        $redis -> unlink($redisEDataAllKey);
        $redis -> unlink($redisInfoKey);
    }

    private function _saveDataToRedis ($redis,$redisInfoKey,$redisDataKey,$redisNormalDataKey,$redisEDataKey,$handledData) {

        $redis -> set($redisInfoKey,json_encode($handledData),60 * 30);
        // 保存总数-all
        $this -> hashService -> saveDataToHash($redisDataKey,$handledData['data'],60 * 30);
        $this -> saveDataToHashForSpecifiedKey($redisDataKey.self::UPLOAD_SUFFIX,$handledData['data'],60 * 60 * 24 * 7); // 保存7天供下载
        // 保存正常数据-normal
        $this -> hashService -> saveDataToHash($redisNormalDataKey,$handledData['normal_data'],60 * 30);
        // 保存异常数据-exception
        $this -> hashService -> saveDataToHash($redisEDataKey,$handledData['exception_data'],60 * 30);
    }

    private function handleData($wId,$tType,$data)
    {
        if (1 == $tType) { // 店内码
            return $this -> _uniqueCodeHandle($wId,$tType,$data);
        }
        if (2 == $tType) { // 条码
            return $this -> _barcodeHandle($wId,$tType,$data);
        }
        if (3 == $tType) { // epc码
            return $this -> _epcCodeHandle($wId,$tType,$data);
        }
        return [];
    }
    private function _uniqueCodeHandle($wId,$tType,$data)
    {
        $total = count($data); // 数据总数
        // 获取操作人员信息
        $adminInfo = $this -> AdminService -> idsToNameList(array_column($data,'admin_id'));
        //$userInfo = $this -> session -> get('userInfo');
        // 获取店内码对应的原货架信息
        $uniqueCodes = array_column($data,'goods_code');
        $uniqueCodesWithUnique = array_unique($uniqueCodes);
        $shelfCodes = array_column($data,'shelf_code');
        $shelfCodesWithUnique = array_unique($shelfCodes);

        // 比对新旧货架
        $shelves = $this -> ShelfGoodsCodeMapService -> getShelfCodeByUniqueCodes($uniqueCodes);
        $shelves = array_column($shelves,NULL,'unique_code'); // 原货架信息

        // 校验店内码是否有重复并标记
        $data = $this -> markGoodsRepeat($data,$uniqueCodes,PublicCode::GOODS_CODE_TYPE['unique_code']);

        // 校验店内码是否存在，是否在指定仓库 - 不再校验 不存在，不在本仓 的数据，进入实盘结果体现 - 20230203 yj
        // $data = $this -> checkUniqueIfInSpecifiedWarehouse ($data,$uniqueCodes,$uniqueCodesWithUnique,$wId);

        // 校验货架号是否存在
        // $data = $this -> _checkIfShelfPresent($data,$shelfCodes);

        // 校验货架号是否在当前仓库
         $data = $this -> checkShelfCodeIfInWarehouse($data,$shelfCodesWithUnique,$wId,$shelfCodes);

        // 批量校验店内码是否出库: 出库 锁定
        // $data = $this -> checkUniqueOutStore ($data,$uniqueCodes);

        // 校验店内码库存是否充足
        // $data = $this -> checkUniqueStock($wId,$uniqueCodesWithUnique,$data);

        // 组装校验结果
        $finalResult = $this -> handleFinalUData ($data,$shelves,$tType,$adminInfo,$total);
        return [
            'total' => $finalResult['total'],
            'data' => $finalResult['data'],
            'normal_num' => $finalResult['total']-$finalResult['exception_num'],
            'normal_data' => $finalResult['normal_data'],
            'exception_num' => $finalResult['exception_num'],
            'exception_data' => $finalResult['exception_data'],
        ];
    }

    // 标记重复店内码
    private function markGoodsRepeat($data,$goodsCodes,$goodsCodeType) {
        $ac = array_count_values($goodsCodes);
        foreach ($ac as $goodsCode => $count) {
            if ($count >= 1) {
                // 将异常店内码异常信息写进$data数据里
                $eMsg = '';
                switch ($goodsCodeType) {
                    case 1:
                        $eMsg = '店内码重复';
                        break;
                    case 3:
                        $eMsg = 'epc码重复';
                        break;
                }
                $keys = array_search3($data,'goods_code',$goodsCode); // 可能搜索目标不唯一，搜索结果是多个key
                foreach ($keys as $i => $key) {
                    // 标记重复店内码，只保留最后一个重复店内码(若总数为1，则最后一个index就是0)
                    $lastItemIndex = count($keys) == 1 ? 0 : count($keys) - 1;
                    // 是最后一个的话，标记是最后一个重复的店内码
                    if ($i == $lastItemIndex) {
                        $data[$key]['last_repeat'] = true;
                    } else {
                        $data[$key]['exception'][] = $eMsg;
                        $data[$key]['is_repeat'] = true;
                    }
                }
            }
        }
        return $data;
    }

    // 校验店内码是否在指定的仓库
    private function checkUniqueIfInSpecifiedWarehouse ($data,$uniqueCodes,$uniqueCodesWithUnique,$wId) {
        $checkRes = $this -> ShelfGoodsCodeMapService -> getUniqueCodeWarehouseMap($uniqueCodes);
        if (empty($checkRes)) {
            $eMsg = '店内码不存在';
            foreach ($data as $key => $v) {
                // 去重后的店内码
                if (in_array($v['goods_code'],$uniqueCodesWithUnique)) {
                    $data[$key]['exception'][] = $eMsg;
                }
            }
        } else {
            // 获取结果不为空，则过滤不在本仓的店内码

            // 取出不存在的店内码，标记异常
            $sysUniqueCodes = array_column($checkRes,'unique_code');
            // 批量取出来的店内码数组转大写
            $sysUniqueCodes = array_change_value_case($sysUniqueCodes);

            $unExistsUniqueCodes = array_diff($uniqueCodes,$sysUniqueCodes);

            // 店内码不存在的
            $eMsg = '店内码不存在';
            foreach ($data as $key => $v) {
                // 去重后的店内码
                if (in_array($v['goods_code'],$unExistsUniqueCodes)) {
                    $data[$key]['exception'][] = $eMsg;
                }
            }

            // 仓库ID不同的
            foreach ($checkRes as $k => $v) {
                $eMsg = '店内码不在指定仓库';
                if ($wId != $v['w_id']) {
                    // 仓库不同，拿该内码到原数据里标记异常
                    foreach ($data as $key => $v2) {
                        if ($v['unique_code'] == $v2['goods_code']) {
                            $data[$key]['exception'][] = $eMsg;
                        }
                    }
                }
            }
        }

        return $data;
    }

    // 校验货架是否在仓库
    private function checkShelfCodeIfInWarehouse ($data,$shelfCodesWithUnique,$wId,$shelfCodes) {
        $checkRes = $this -> ShelfService -> getShelfS(['w_id' => $wId,'shelf_codes'=>$shelfCodes]);
        // 结果为空，不存在，将所有店内码及异常信息分别标记返回
        if (empty($checkRes)) {

            // 将异常店内码异常信息写进$data数据里
            $eMsg = '货架不在当前仓库';
            //$eMsg = '<span style="color: red">目标货架号不存在或不在本仓库~</span><br>';
            foreach ($data as $key => $v) {
                // 去重后的店内码
                if (in_array($v['shelf_code'],$shelfCodesWithUnique)) {
                    $data[$key]['exception'][] = $eMsg;
                }
            }
        } else {
            // 不为空，将不存在货架的数据标记异常信息
            $sysShelf = array_column($checkRes,'shelf_code');
            $unExistsShelves = array_unique(array_diff($shelfCodes,$sysShelf));
            // 不存在的条码，标记异常
            if (!empty($unExistsShelves)) {
                $eMsg = '货架不在当前仓库~~';
                //$eMsg = '<span style="color: red">目标货架号不存在或不在本仓库~~</span><br>';
                foreach ($data as $key => $v) {
                    // 如果货架号不存在，标记异常
                    if (in_array($v['shelf_code'],$unExistsShelves)) {
                        $data[$key]['exception'][] = $eMsg;
                    }
                }
            }
        }
        return $data;
    }

    // 组装店内码最终校验数据
    private function handleFinalUData ($data,$shelves,$tType,$adminInfo,$total) {
        $eNum = 0;// 异常数
        $normalData = [];
        $exceptionData = [];
        foreach ($data as $k => &$v) {
            $v['w_id'] = $shelves[$v['goods_code']]['w_id'] ?? 0; // 原货架仓库id
            $v['goods_code_type'] = $tType; // 商品码类型：1=店内码，2=条码
            $v['num'] = 1; // 商品码
            $v['admin_name'] = $adminInfo[$v['admin_id']]; // 理货人

            if (isset($v['exception'])) {
                $eNum++;
                array_push($exceptionData,$v);
            } else {
                if (isset($v['is_repeat'])) {
                    $eNum++;
                    $v['exception'][] = '店内码重复';
                    array_push($exceptionData,$v);
                } else {
                    $v['exception'][] = '通过';
                    array_push($normalData,$v);
                }
            }
        }

        return [
            'total'=> $total,
            'data' => $data,
            'normal_num' => $total-$eNum,
            'normal_data' => $normalData, // 正常数据和锁定数据都可以理货，故此处合并作为正常数据返回给前端
            'exception_num' => $eNum,
            'exception_data' => $exceptionData,
        ];
    }

    private function _barcodeHandle($wId,$tType,$data)
    {
        $total = count($data); // 数据总数

        // 校验：
        //货架不存在
        //货架不在本仓
        //货品不存在
        //货品重复

        // 获取理货人员信息
        $tallyAdminInfo = $this -> AdminService -> idsToNameList(array_column($data,'admin_id'));
        // 获取店内码对应的原货架信息
        $barCodes = array_column($data,'goods_code');
        $shelfCodes = array_column($data,'shelf_code');
        // 完善条码数据: t_admin_id，t_admin_name
        //$data = $this -> _perfectBarcodeData($data,$tallyAdminInfo);

        // 校验货架号是否存在
         $data = $this -> _checkIfShelfPresent($data,$shelfCodes);

        // 校验货架号是否在指定仓库
        // $data = $this -> _checkIfShelfInSameWarehouse($data,$wId,$shelfCodes);

        // 校验条码是否存在
         $data = $this -> _checkIfBarcodePresent($data,$barCodes);

        // 组装校验结果
        $finalResult = $this -> handleFinalBData ($data,$wId,$tType,$tallyAdminInfo,$total);
        return [
            'total' => $finalResult['total'],
            'data' => $finalResult['data'],
            'normal_num' => $finalResult['total']-$finalResult['exception_num'],
            'normal_data' => $finalResult['normal_data'],
            'exception_num' => $finalResult['exception_num'],
            'exception_data' => $finalResult['exception_data'],
        ];
    }

    // 校验新旧货架是否存在
    private function _checkIfShelfPresent($data,$shelfCodes) {
        $shelvesCheckResult = $this -> ShelfService -> checkShelfCodes(array_unique($shelfCodes));
        if (!empty($shelvesCheckResult['not_exists'])) {
            foreach ($shelvesCheckResult['not_exists'] as $shelfItem) {
                foreach ($data as $key => $v) {
                    if ($shelfItem == $v['shelf_code']) {
                        $data[$key]['exception'][] = '货架不存在';
                    }
                }
            }
        }
        return $data;
    }

    // 校验货架是否在指定仓库
    private function _checkIfShelfInSameWarehouse($data,$wId,$unionShelfCodes) {
        $shelvesWIdsResult = $this -> ShelfService -> getShelfS(['shelf_codes'=> $unionShelfCodes],['w_id','shelf_code']);
        $shelvesWIdsResultMap = collect($shelvesWIdsResult) -> keyBy(function ($item) {
            return $item['w_id'].'-'.$item['shelf_code'];
        });
        foreach ($data as $key => $dataItem) {
            $newWId = -1;

            $searchNewItem = $shelvesWIdsResultMap -> get($wId.'-'.$dataItem['shelf_code']);
            if ($searchNewItem) {
                $newWId = $searchNewItem['w_id']; // $searchNewItem['val']['w_id'];
            }

            if ($newWId != $wId) {
                $data[$key]['exception'][] = '货架不在当前仓库';
            }
        }

        return $data;
    }

    // 校验条码是否存在
    private function _checkIfBarcodePresent($data,$barCodes) {
        $checkBarcodeRes = $this -> SkuBarcodeService -> checkBarcodes($barCodes);
        if (!empty($checkBarcodeRes['not_exists'])) {
            foreach ($data as $key => $v) {
                if (in_array($v['goods_code'],$checkBarcodeRes['not_exists'])) {
                    $data[$key]['exception'][] = '条码不存在';
                }
            }
        }
        return $data;
    }

    // 组装店内码最终校验数据
    private function handleFinalBData ($data,$wId,$tType,$adminInfo,$total) {
        $eNum = 0;// 异常数
        $normalData = [];
        $exceptionData = [];
        foreach ($data as $k => &$v) {
            $v['goods_code_type'] = $tType; // 商品码类型：1=店内码，2=条码
            $v['admin_name'] = $adminInfo[$v['admin_id']]; // 理货人
            if (isset($v['exception'])) {
                $eNum++;
                array_push($exceptionData,$v);
            } else {
                $v['exception'][] = '通过';
                array_push($normalData,$v);
            }
        }

        return [
            'total'=> $total,
            'data' => $data,
            'normal_num' => $total-$eNum,
            'normal_data' => $normalData, // 正常数据和锁定数据都可以理货，故此处合并作为正常数据返回给前端
            'exception_num' => $eNum,
            'exception_data' => $exceptionData,
        ];
    }

    private function _epcCodeHandle($wId,$tType,$data) {
        $total = count($data); // 数据总数

        // 给每条数据标记原始key，便于后面校验查找原位置
        foreach ($data as $k => &$v) {
            $v['original_key'] = $k;
        }

        // 获取理货人员信息
        $adminInfo = $this -> AdminService -> idsToNameList(array_column($data,'admin_id'));
        //$userInfo = $this -> session -> get('userInfo');
        // 获取店内码对应的原货架信息
        $goodsCodes = array_column($data,'goods_code');
        $uniqueCodesWithUnique = array_unique($goodsCodes);

        // 比对货架
        $checkRes = $this -> ShelfGoodsCodeMapService -> getEpcCodesInfo($goodsCodes);
        $shelves = array_column($checkRes,NULL,'epc_code');

        // 集合：以epc为键
        $epcGroup = collect($data) -> groupBy('goods_code');

        // 校验epc码是否有重复并标记
        $data = $this -> markGoodsRepeat($data,$goodsCodes,PublicCode::GOODS_CODE_TYPE['epc_code']);

        // 校验epc码是否存在，是否在指定仓库
        // $data = $this -> checkEpcIfInSpecifiedWarehouse ($data,$goodsCodes,$uniqueCodesWithUnique,$wId,$checkRes,$epcGroup);

        // 如果epc绑定的是条码，则提示：当前仅支持绑定店内码的货品
        $data = $this -> _checkEpcIfBindBarcode($data,$checkRes,$epcGroup);

        // 组装校验结果
        $finalResult = $this -> handleFinalUData ($data,$shelves,$tType,$adminInfo,$total);
        return [
            'total' => $finalResult['total'],
            'data' => $finalResult['data'],
            'normal_num' => $finalResult['total']-$finalResult['exception_num'],
            'normal_data' => $finalResult['normal_data'],
            'exception_num' => $finalResult['exception_num'],
            'exception_data' => $finalResult['exception_data'],
        ];
    }

    // 校验店内码是否在指定的仓库
    private function checkEpcIfInSpecifiedWarehouse ($data,$goodsCodes,$uniqueCodesWithUnique,$wId,$checkRes,$epcGroup) {
        if (empty($checkRes)) {
            $eMsg = 'epc码不存在';
            foreach ($data as $key => $v) {
                // 去重后的店内码
                if (in_array($v['goods_code'],$uniqueCodesWithUnique)) {
                    $data[$key]['exception'][] = $eMsg;
                }
            }
        } else {
            // 获取结果不为空，则过滤不在本仓的店内码
            // 取出不存在的店内码，标记异常
            $sysUniqueCodes = array_column($checkRes,'epc_code');
            // 批量取出来的店内码数组转大写
            $sysUniqueCodes = array_change_value_case($sysUniqueCodes);

            $unExistsUniqueCodes = array_diff($goodsCodes,$sysUniqueCodes);

            // 店内码不存在的
            foreach ($data as $key => $v) {
                // 去重后的店内码
                if (in_array($v['goods_code'],$unExistsUniqueCodes)) {
                    $data[$key]['exception'][] = 'epc码不存在';
                }
            }

            // 仓库ID不同的
            foreach ($checkRes as $k => $v) {
                if ($wId != $v['w_id']) {
                    // 根据epc找到该epc分组数据，分别标记异常
                    $groupData = $epcGroup -> get($v['epc_code']);
                    foreach ($groupData as $groupDatum) {
                        $data[$groupDatum['original_key']]['exception'][] = 'epc码不在当前仓库';
                    }
                }
            }
        }

        return $data;
    }

    // 如果epc绑定的是条码，则提示：当前仅支持绑定店内码的货品
    private function _checkEpcIfBindBarcode($data,$checkRes,$epcGroup) {
        foreach ($checkRes as $checkItem) {
            // 未绑定店内码的，提示异常
            if (!$checkItem['unique_code']) {
                // 根据epc找到该epc分组数据，分别标记异常
                $groupData = $epcGroup -> get($checkItem['epc_code']);
                foreach ($groupData as $groupDatum) {
                    $data[$groupDatum['original_key']]['exception'][] = '当前不支持绑定条码的货品';
                }
            }
        }

        return $data;
    }
    /******************************************************* end *******************************************************/

    /**
     * 将上传数据以货架排为单位汇总统计
     * @param $data
     */
    private function handleStData($data,$stWay)
    {
        $adminInfo = $this -> AdminService -> idsToNameList(array_column($data,'admin_id'));
        foreach ($data as $k => $v) {
            // 详情数据追加采集人姓名
            $data[$k]['admin_name'] = $adminInfo[$v['admin_id']];
        }
        $result['detail_data'] = $data;
        return $result;
    }
//    private function handleStData($data,$stWay)
//    {
//        $adminInfo = $this -> AdminService -> idsToNameList(array_column($data,'admin_id'));
//        $summaryData = [];
//        $arr = [];
//        foreach ($data as $k => $v) {
//            $strArr = explode('-',$v['shelf_code']);
//            $shelfLine = $strArr[0].'-'.$strArr[1];
//            $searchRes = array_search2($summaryData,'shelf_line',$shelfLine); // 如果找到，则返回的结果是改元素的所在下标key
//            $arr['shelf_line'] = $shelfLine;
//            $arr['gather_admin_name'] = $adminInfo[$v['admin_id']];
//
//            if( 1 == $stWay ) {
//                if ($searchRes !== false) { // 存在，原来基础上数量+1
//                    $summaryData[$searchRes]['real_stock'] = $summaryData[$searchRes]['real_stock'] + 1;
//                } else { // 不存在
//                    $arr['real_stock'] = 1;
//                    array_push($summaryData,$arr);
//                }
//            } else if( 2 == $stWay ) {
//                if ($searchRes !== false) { // 存在，原来基础上数量+1
//                    $summaryData[$searchRes]['real_stock'] = $summaryData[$searchRes]['real_stock'] + $v['num'];
//                } else { // 不存在
//                    $arr['real_stock'] = $v['num'];
//                    array_push($summaryData,$arr);
//                }
//            }
//
//            // 详情数据追加采集人姓名
//            $data[$k]['admin_name'] = $adminInfo[$v['admin_id']];
//        }
//        $result['summary_data'] = $summaryData;
//        $result['detail_data'] = $data;
//        return $result;
//    }

    /**
     * 任务详情 - 从redis获取上传的盘点数据列表
     * @RequestMapping(path="/st/getUploadStData", methods="get,post")
     */
    public function getUploadStData(RequestInterface $request){
        $params = $request -> all();

        // 将上传的盘点数据存入redis
        $userInfo = $this -> session -> get('userInfo');
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 10;
        $redisKeys = $this -> _makeRedisKey($params,$userInfo);
        try {
            $dataKey = '';
            $infoKey = $redisKeys['info_key'];
            if ($params['show_type'] == 'all') {
                $dataKey = $redisKeys['data_key'];
            }
            if ($params['show_type'] == 'normal') {
                $dataKey = $redisKeys['normal_data_key'];
            }
            if ($params['show_type'] == 'exception') {
                $dataKey = $redisKeys['e_data_key'];
            }
            if (empty($dataKey)) {
                return $this -> returnApi(ErrorCode::SERVER_ERROR,"缓存key不能为空",[]);
            }
            //$infoKey = 1 == $params['st_type'] ? $userInfo['uid'].'_st_info_unique_code' : $userInfo['uid'].'_st_info_barcode';
            $dataResult = $this -> hashService -> getDataListFromHash($dataKey,(int)$page,(int)$limit);
            $infoResult = redis() -> get($infoKey);
            if (!$infoResult || !$dataResult) {
                return $this -> returnApi(ErrorCode::SERVER_ERROR,"没有数据",[]);
            }
            if ($params['export'] == 1) {
                $dataAll = $this -> hashService -> getDataAllFromHash($dataKey);
                $url = $this -> _exportUploadStData($dataAll);
                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),['url' => $url]);
            }
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",['token' => getIdempotenceToken($userInfo['uid']),'info' => json_decode($infoResult),'data' => $dataResult]);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    private function _exportUploadStData($dataResult){

        // 取出异常数据 理货结果类型：1=正常，2=异常
        if (empty($dataResult)) {
            throw new BusinessException('数据为空');
        }

        $head = [
            'goods_code_type_name' => '类型',
            'goods_code' => '商品码',
            'num' => '数量',
            'shelf_code' => '货架',
            'admin_name' => '操作人',
            'result' => '结果',
        ];
        $fileName = '盘点数据'.date('Y-m-d-H-i-s');
        $exportData = [];
        foreach ($dataResult as $k => $v) {
            $goodsCode = $v['goods_code'] ? $v['goods_code'] : '';
            $goodsCodeTypeName = '';
            if (1 == $v['goods_code_type']) {
                $goodsCodeTypeName = '店内码';
            }
            if (2 == $v['goods_code_type']) {
                $goodsCodeTypeName = '条码';
            }
            if (3 == $v['goods_code_type']) {
                $goodsCodeTypeName = 'EPC码';
            }

            array_push($exportData,[
                'goods_code_type_name' => $goodsCodeTypeName,
                'goods_code' => $goodsCode,
                'num' => $v['num'],
                'shelf_code' => $v['shelf_code'] ? $v['shelf_code'] : '',
                'admin_name' => $v['admin_name'] ? $v['admin_name'] : "",
                'result' => $v['exception'] ? join('，',$v['exception']) : '',
            ]);
        }
        return exportToExcel($head,$exportData,$fileName);

    }

    /**
     * 确定录入 - 保存上传的盘点数据
     * @RequestMapping(path="/st/saveSbData", methods="get,post")
     */
    public function saveSbData(RequestInterface $request)
    {
        $params = $request -> all();

        $validator = validate()->make($params,[
            'w_id' => 'required|integer',
            'st_id' => 'required|integer',
            'st_type' => 'required|integer',
        ],[
            'w_id.required'=>'仓库ID必须',
            'w_id.integer'=>'仓库ID必须是整数',
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_id.integer'=>'盘点ID（st_id）必须是整数',
            'st_type.required'=>'盘点类型必须',
            'st_type.integer'=>'盘点类型必须是整数',
        ]);

        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        $stId = $params['st_id'];

        $userInfo = $this -> session -> get('userInfo');
        if (!$userInfo['uid']) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, "请登录" );
        }

        // 防重复点击校验
        $checkRes = $this -> idempotenceCheck($userInfo['uid'],$params['token']);
        if (!$checkRes) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '请勿重复请求' );
        }

        $redisKeys = $this -> _makeRedisKey($params,$userInfo);
        $key = $redisKeys['info_key'];
        // 从redis取出数据
        // $stData = redis() -> get($key);
        $redis = redis();
        $stData = json_decode($redis -> get($key),true);
        if (empty($stData['normal_data'])) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,'无正确数据，无法录入',[]);
        }
        // 有异常数据，允许提交-20230202-yj
//        if (!empty($stData['exception_data'])) {
//            return $this -> returnApi(ErrorCode::SERVER_ERROR,'有异常数据，请修复后再上传',[]);
//        }
        //$stData = json_decode($stData,true)['detail_data'];
        $adminInfo = [
            'admin_id' => $userInfo['uid'],
            'admin_name' => $userInfo['nickname'],
        ];
        try {
            $params = [
                'w_id' => $params['w_id'],
                'st_id' => $stId,
                'st_type' => $params['st_type'],
                'admin_info' => $adminInfo,
                'from' => "wms",
            ];
            $result = $this -> StService -> saveSbDataNew($params,$stData['normal_data']);
            // $result = $this -> StService -> saveSbData($params['w_id'],$stId,$params['st_type'],$adminInfo,$from,$stData['normal_data']);

            $stInfo = $this -> StService -> getTaskOne($params['st_id']);
            // 删除缓存数据
            $redis -> del($key);

            /********************************************记录日志****************************************************/
            // 当前登录账户数据
            $userInfo = $this->session->get('userInfo');

            $opType = '';
            if (in_array($stInfo['st_status'],[1,2])) {
                $opType = '预盘';
            }
            if (in_array($stInfo['st_status'],[3])) {
                $opType = '复盘';
            }

            // 记录日志数据
            $logData = [
                'snow_id' => $this->request->getAttribute('snow_id'),
                'op_id' => $params['st_id'],
                'op_type' => $opType.'数据录入', // 操作类型
                'op_content' => '数量：'. count($stData['normal_data']),//'盘点ID：'.$ret['st_id'], // 操作内容
                'st_status' => 0,  //录入，默认无状态设置 // 盘点状态: 1=初盘中，2=复盘中，3=待审核，4=已完成，5=已作废，6=驳回
                'admin_id' => $userInfo['uid'],
                'admin_name' => $userInfo['nickname'],
                'op_time' => date('Y-m-d H:i:s'), // 操作时间
                'model_name' => 'st', // 操作模块
                'remark' => '数据录入：'. count($stData['normal_data']) // $ret ? '成功' : '失败' // PublicCode::ST_STATUS[$params['st_status']]
            ];
            wlog((string)$logData['snow_id'], $logData);
            /********************************************记录日志****************************************************/
            if ($result['result']) {
                return $this -> returnApi(ErrorCode::SUCCESS,$result['msg'],$result['result']);
            } else {
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$result['msg']);
            }

        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    private function getOpType($stStatus)
    {

        // 盘点状态: 0=草稿,1=即将开始，2=正在预盘，3=正在复盘，4=复盘完成，5=已完成，6=已作废,7=正在创建盘点单，8=创建盘点单失败
        switch ($stStatus) {
            case 3:
                $msg = '完成预盘';
                break;
            case 4:
                $msg = '完成复盘';
                break;
            case 5:
                $msg = '完成盘点';
                break;
            case 6:
                $msg = '作废盘点';
                break;
            default:
                $msg = '未知';
                break;
        }
//        switch ($stStatus) {
//            case 4:
//                $msg = '完成盘点';
//                break;
//            case 5:
//                $msg = '作废盘点';
//                break;
//            case 7:
//                $msg = '数据录入';
//                break;
//            default:
//                $msg = '';
//                break;
//        }

        return $msg;
    }

    /**
     * 获取某一任务下的实盘结果
     * @param RequestInterface $request
     * @RequestMapping(path="/st/getStResultListByStId", methods="get,post")
     */
    public function getStResultListByStId(RequestInterface $request)
    {
        $params = $request -> all();
        logger() -> debug('获取某一任务下的实盘结果参数：',[$params]);
        try {
            // 导出
            $export = $params['export'] ?? 0;

            $where = $params['where'] ?? [];
            $stId = $params['st_id'];

            if (isset($where['st_result_type'])) {
                $where['st_result_type'] = isset($where['st_result_type']) && in_array($where['st_result_type'],[0,1,2,3,4,5,6]) ? explode(',',$where['st_result_type']) : [];
            }
            if (isset($where['shelf_line'])) {
                $where['shelf_line'] = $where['shelf_line'] ? explode(',',$where['shelf_line']) : [];
            }

            if (isset($where['gather_shelf_line'])) {
                $where['gather_shelf_line'] = $where['gather_shelf_line'] ? explode(',',$where['gather_shelf_line']) : [];
            }

            logger() -> debug('获取某一任务下的实盘结果where参数：',[$where]);
            $userInfo = $this -> session -> get('userInfo');
            $adminId= $userInfo['uid'];
            if (in_array($export,[1,2])) {
                // 异步协程执行逻辑
                $request = Context::get(\Psr\Http\Message\ServerRequestInterface::class);
                co(function () use($stId,$adminId,$params,$where,$request) {
                    $request = $request->withAttribute('request_id', uuid(false));
                    // 重新绑定请求对象到上下文
                    Context::set(\Psr\Http\Message\ServerRequestInterface::class, $request);
                    $result = [];
                    $url = $this -> exportStResultBatch($stId,$where);
                    // 存入导出结果，供前端轮询获取
                    $result['url'] = $url;
                    $key = 'st_result:'.$params['st_id'].':'.$adminId;
                    StResult::saveResultToRedis($key,$result);
                });
                return $this -> returnApi(ResponseCode::SUCCESS,'正在处理，请稍候...');
            }
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 10;
            $ret = $this -> StService -> getStResultListByStId((int)$stId, (int)$page, (int)$limit, $where);

            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    private function exportStResultBatch($stId,$where) {
        logger() -> debug('分批次获取数据并逐次写入',[$stId,$where]);
        // 分批次获取数据并逐次写入
        $filename = '盘点结果导出_'.date( 'YmdHis' ) . '.xls';
        $writer = new \XLSXWriter();
        $sheet1 = 'sheet1';
        $limit = self::EXPORT_BATCH_SIZE;
        // 根据总数分批获取
        $dataTotal = 0;
        $header = [
            'shelf_code' => '系统货架位',
            'gather_shelf_code' => '采集货架位',
            'goods_code_type' => '类型',
            'goods_code' => '店内码/条形码',
            'book_stock' => '系统数',
            'remain_goods_num' => '待采数',
            'real_stock' => '采集数量',
            'p_num' => '盘盈数',
            'l_num' => '盘亏数',
            'unique_code_status_name' => '店内码状态',
            'st_result_type_name' => '结果',
            'last_tally_date' => '上次理货时间',
        ];
        if (!empty( $header )) {
            $writer->writeSheetRow( $sheet1, array_values( $header ) );
        }

        $page = 1;
        while (true) {
            logger() ->debug('第'.$page.'次导出');
            $t1 = microtime(true);
            $ret = container()->get(StServiceInterface::class) -> getStResultListByStIdExport((int)$stId, (int)$page, (int)$limit, $where);
//            $ret = $this -> StService -> getStResultListByStIdExport((int)$stId, (int)$page, (int)$limit, $where);
            // 取出的数据
            $data = $ret['data'];
            if (!$data && $page == 1) {
                throw new BusinessException('结果为空~');
            }
            if (!$data) {
                logger() -> debug('没数据了');
                break;
            }
            foreach ($data as $v) {
                $shelfCode = !empty($v['shelf_grid']) ? $v['shelf_grid'] : $v['shelf_seat'];
                $gatherShelfCode = !empty($v['gather_shelf_grid']) ? $v['gather_shelf_grid'] : $v['gather_shelf_seat'];
                // 商品码类型：1=店内码，2=条形码，3=epc码
                $goodsCodeType = '';
                if (1 == $v['goods_code_type']) {
                    $goodsCodeType = '店内码';
                }
                if (2 == $v['goods_code_type']) {
                    $goodsCodeType = '条码';
                }
                if (3 == $v['goods_code_type']) {
                    $goodsCodeType = 'epc码';
                }
                $writer->writeSheetRow( $sheet1, [
                    'shelf_code' => $shelfCode,
                    'gather_shelf_code' => $gatherShelfCode,
                    'goods_code_type' => $goodsCodeType,
                    'goods_code' => $v['goods_code_type'] == 3 ? ($v['epc_goods_code'] ? $v['epc_goods_code'] : $v['gather_epc_goods_code']) : ($v['goods_code'] ? $v['goods_code'] : $v['gather_goods_code']),
                    'book_stock' => $v['book_stock'] ?? '',
                    'remain_goods_num' => $v['remain_goods_num'] ?? '',
                    'real_stock' => $v['real_stock'] ?? '',
                    'p_num' => $v['p_num'] ?? 0,
                    'l_num' => $v['l_num'] ?? 0,
                    'unique_code_status_name' => $v['goods_code_type'] == 3 ? $v['epc_unique_code_status_name'] : $v['unique_code_status_name'],
                    'st_result_type_name' => $v['st_result_type_name'] ?? '',
                    'last_tally_date' => $v['last_tally_date'] ?? '',
                ] );
                $dataTotal++;
            }
            logger() ->debug('第'.$page.'次导出耗时：'.number_format(microtime(true)-$t1, 3, '.', '').' s');
            $page++;
        }
        // 临时路径
        $baseDir = 'runtime/';
        $filePath = $baseDir . $filename;
        // 输出二进制流到本地文件
        $t1 = microtime(true);
        $writer->writeToFile( $filePath );
        logger() ->debug('输出二进制流到本地文件数据总数：'.$dataTotal.'，耗时：'.number_format(microtime(true)-$t1, 3, '.', '').' s');

        $t1 = microtime(true);
        $url = upload( $filename, $filePath, 5 );
        logger() ->debug('上传至OSS耗时：'.number_format(microtime(true)-$t1, 3, '.', '').' s');

        unlink( $filePath );
        return $url;
    }

    private function exportStResult(array $data){

        $head = [
            'shelf_code' => '系统货架位',
            'gather_shelf_code' => '采集货架位',
            'goods_code_type' => '类型',
            'goods_code' => '店内码/条形码',
            'book_stock' => '系统数',
            'remain_goods_num' => '待采数',
            'real_stock' => '采集数量',
            'p_num' => '盘盈数',
            'l_num' => '盘亏数',
            'unique_code_status_name' => '店内码状态',
            'st_result_type_name' => '结果',
            'last_tally_date' => '上次理货时间',
        ];
        $fileName = '盘点结果 - '.date('Y-m-d-H-i-s');
        $eData = [];
        if (!empty($data)) {
            foreach ($data as $k => $v) {
                logger() -> debug('v============',[$v]);
                $shelfCode = !empty($v['shelf_grid']) ? $v['shelf_grid'] : $v['shelf_seat'];
                $gatherShelfCode = !empty($v['gather_shelf_grid']) ? $v['gather_shelf_grid'] : $v['gather_shelf_seat'];
                // 商品码类型：1=店内码，2=条形码，3=epc码
                $goodsCodeType = '';
                if (1 == $v['goods_code_type']) {
                    $goodsCodeType = '店内码';
                }
                if (2 == $v['goods_code_type']) {
                    $goodsCodeType = '条码';
                }
                if (3 == $v['goods_code_type']) {
                    $goodsCodeType = 'epc码';
                }
                array_push($eData,[
                    'shelf_code' => $shelfCode,
                    'gather_shelf_code' => $gatherShelfCode,
                    'goods_code_type' => $goodsCodeType,
                    'goods_code' => $v['goods_code_type'] == 3 ? ($v['epc_goods_code'] ? $v['epc_goods_code'] : $v['gather_epc_goods_code']) : ($v['goods_code'] ? $v['goods_code'] : $v['gather_goods_code']),
                    'book_stock' => $v['book_stock'] ?? '',
                    'remain_goods_num' => $v['remain_goods_num'] ?? '',
                    'real_stock' => $v['real_stock'] ?? '',
                    'p_num' => $v['p_num'] ?? 0,
                    'l_num' => $v['l_num'] ?? 0,
//                    'unique_code_status' => PublicCode::UNIQUE_CODE_STATUS_NAME[$v['unique_code_status']],
                    'unique_code_status_name' => $v['unique_code_status_name'],
                    'st_result_type_name' => $v['st_result_type_name'] ?? '',
                    'last_tally_date' => $v['last_tally_date'] ?? '',
                ]);
            }
        }
        return exportToExcel($head,$eData,$fileName);
    }

    /**
     * 多字段搜索
     * @param array $arr 待搜索集合
     * @param array $kvs 多字段kv键值对
     */
    public function _searchItemByMultiFields(array $arr,array $kvs)
    {
        return array_filter($arr,function ($item) use ($kvs) {
            $conditionResult = [];
            foreach ($kvs as $k => $v) {
                if ($item[$k] == $v) {
                    array_push($conditionResult,1);
                }
            }
            if (count($conditionResult) === count($kvs)) {
                return $item;
            }
            return false;
        });
    }

    /**
     * 前端轮询获取录入盘点数据的结果
     * @RequestMapping(path="/st/getSaveStDataResult", methods="get,post")
     */
    public function getSaveStDataResult(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params,[
            'st_id' => 'required|integer',
        ],[
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_id.integer'=>'盘点ID（st_id）必须是整数',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $userInfo = $this -> session -> get('userInfo');
        if (!$userInfo['uid']) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, "请登录" );
        }
        // 将结果写入redis，由前端轮询获取结果
        $from = 'wms';
        $ResKey = $from.'_save_st_data_'.$params['st_id'].'-'.$userInfo['uid'];
        //$ResEDataKey = $userInfo['uid'].'_save_st_e_data_'.$params['st_id'];
        $resultData = redis() -> rPop($ResKey);
        //$resultEData = redis() -> rPop($ResEDataKey);

        if ($resultData) {
            $resultData = json_decode($resultData,true);
            if ($resultData["result"]) {
                $returnData = [
                    'result' => "1",
                    'eData' => $resultData["eData"] ? $resultData["eData"] : [],
                ];
                return $this -> returnApi(ResponseCode::SUCCESS,'录入任务已完成，页面将自动刷新',$returnData);
            }
            $returnData = [
                'result' => "0",
                'eData' => $resultData["eData"] ? $resultData["eData"] : [],
            ];
            return $this -> returnApi(ResponseCode::SERVER_ERROR,$resultData["msg"],$returnData);
        }

        return $this -> returnApi(0,"无结果",'');
    }

    /**
     * 前端轮询获取创建任务结果
     * @RequestMapping(path="/st/getAddTaskResult", methods="get,post")
     */
    public function getAddTaskResult(RequestInterface $request)
    {
        $params = $request -> all();
//        $validator = validate()->make($params,[
//            'st_id' => 'required|integer|gt:0',
//        ],[
//            'st_id.required'=>'盘点ID（st_id）必须',
//            'st_id.integer'=>'盘点ID（st_id）必须是整数',
//        ]);
//        if ($validator->fails()) {
//            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
//        }
        $userInfo = $this -> session -> get('userInfo');
        if (!$userInfo['uid']) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, "请登录" );
        }
        // 将结果写入redis，由前端轮询获取结果
        $params['admin_id'] = $userInfo['uid'];
        $params['admin_name'] = $userInfo['nickname'];
        $ResKey = 'add_task_data_'.$params['admin_id'];
        //$ResEDataKey = $userInfo['uid'].'_save_st_e_data_'.$params['st_id'];
        $resultData = redis() -> rPop($ResKey);
        //$resultEData = redis() -> rPop($ResEDataKey);

        if ($resultData) {
            $resultData = json_decode($resultData,true);
            if ($resultData["result"]) {
                $returnData = [
                    'result' => "1",
                    'msg' => $resultData["msg"],
                    'data' => $resultData["data"] ? $resultData["data"] : [],
                    'eData' => $resultData["eData"] ? $resultData["eData"] : [],
                ];
                return $this -> returnApi(ResponseCode::SUCCESS,$resultData["msg"],$returnData);
            } else {
                $returnData = [
                    'result' => "0",
                    'msg' => $resultData["msg"],
                    'eData' => $resultData["eData"] ? $resultData["eData"] : [],
                ];
                return $this -> returnApi(ResponseCode::SERVER_ERROR,$resultData["msg"],$returnData);
            }
        }

        return $this -> returnApi(0,"无结果",'');
    }

    /**
     * 获取盘点任务下的所有货架号
     * @RequestMapping(path="/st/getShelfLinesByStId", methods="get,post")
     */
    public function getShelfLinesByStId(RequestInterface $request)
    {
        $data = $request -> all();
        $validator = validate()->make($data, [
            'st_id' => 'required|numeric|gt:0',
        ],[
            'st_id.required' => '部署子任务仓库ID必填',
            'st_id.numeric' => '部署子任务仓库ID必须是数字',
            'st_id.gt' => '部署子任务ID必须为大于0的正整数'
        ]);

        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        try {
            $ret = $this -> StService -> getShelfLinesByStId($data['st_id']);
            foreach ($ret as $k => &$v) {
                $v['name'] = $v['shelf_line'];
                $v['value'] = $v['shelf_line'];
            }
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    /**
     * 获取盘点日志列表
     * @param RequestInterface $request
     * @RequestMapping(path="/st/getStLog", methods="get,post")
     */
    public function getStLog(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'st_id' => 'required|gt:0',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $stId = $params['st_id'];
        $params = [
            'op_id' => $stId,
            'system' => 'wms',
            'model_name' =>'st',
        ];
        try{
            $ret = getLog($params);
            $retData = [];
            foreach ($ret['data'] as $k => $v) {
                //array_push($retData,json_decode($v['res_params'],true));
                array_push($retData,$v['res_params']);
            }

            $result = [
                'data' => $retData,
                'total' => $ret['total'],
            ];
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$result);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }

    }

    //////////////////////////////////////////盘点流程更改/////////////////////////////////////////
    /**
     * 创建盘点任务 - 最简版 - 20220823
     * @RequestMapping(path="/st/addTaskSimple", methods="get,post")
     *
     */
    public function addTaskSimple(RequestInterface $request)
    {

        $params = $request -> all();
        $validator = validate()->make($params, [
            'name' => 'required|string',
            'w_id' => 'required|numeric|gt:0',
            'st_range_key_id' => 'required|numeric|gt:0',
            'st_type' => 'required|numeric|in:1,2,3',
            'start_time' => 'required|string',
            'end_time' => 'required|string',
        ],[
            'name.required'        => '盘点名称必填',
            'name.string'         => '盘点名称必须字符串',
            'start_time.required'        => '盘点申请开始时间必填',
            'start_time.string'         => '盘点申请开始时间必须是字符串',
            'end_time.required'        => '盘点申请结束始时间必填',
            'end_time.string'         => '盘点申请结束时间必须是字符串',
            'w_id.required'        => '仓库ID必填',
            'st_range_key_id.required'        => '抽盘范围key_id必填',
            'w_id.integer'         => '仓库ID必须为正整数',
            'st_type.required'        => '盘点类型必填',
            'st_type.integer'         => '盘点类型必须为正整数',
        ]);

        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        $userInfo = $this -> session -> get('userInfo');

        // 防重复点击校验
//        $checkRes = $this -> idempotenceCheck($userInfo['uid'],$params['token']);
//        if (!$checkRes) {
//            return $this->returnApi( ErrorCode::REQUEST_ERROR, '请勿重复请求' );
//        }

        if ($params['st_type'] == PublicCode::ST_TYPE_MAP['unique_code'] &&
            $params['st_range_key_id'] == PublicCode::ST_RANGE_KEY_MAP['st_part'] &&
            $params['value_id'] == PublicCode::ST_RANGE_VALUE_MAP['barcode']) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '盘点类型为店内码，抽盘不能选择条码');
        }
        if ($params['st_type'] == PublicCode::ST_TYPE_MAP['barcode'] &&
            $params['st_range_key_id'] == PublicCode::ST_RANGE_KEY_MAP['st_part'] &&
            $params['value_id'] == PublicCode::ST_RANGE_VALUE_MAP['unique_code']) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '盘点类型为条码，抽盘不能选择店内码');
        }

        // 登录用户
        $userInfo = $this->session->get('userInfo');
        if(!$userInfo) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '请登录' );
        }

        $params['st_type_name'] = PublicCode::ST_TYPE[$params['st_type']];
        $params['st_status_name'] = PublicCode::NEW_ST_STATUS[1];
        $params['st_range_key_name'] = PublicCode::ST_RANGE_KEY[$params['st_range_key_id']];
        $params['st_range_value_name'] = PublicCode::ST_RANGE_VALUE[$params['value_id']];
        $params['admin_id'] = $userInfo['uid'];
        $params['admin_name'] = $userInfo['nickname'];
        $params['st_no'] = $this -> SerialNoService -> generate(SerialType::I,(int)$params['st_type']);
        $params['deploy_no'] = $this -> SerialNoService -> generate(SerialType::I);
        logger() -> debug('params_info_ids数据：',[$params['info_ids']]);
        $infoIds = [];
        if ($params['info_ids']) {
            $infoIds = $this -> handleInfoIds($params['info_ids'],$params['value_id']);
        }
        $params['info_ids'] = $infoIds;
        logger() -> debug('info_ids数据：',[$infoIds]);
        // 如果是RFID抽盘且是类目抽盘，传来的全是一级类目ID，需要获取一级类目id下的所有子类目id（含一级类目）
        if ($params['st_type'] == PublicCode::ST_TYPE_MAP['rfid'] &&
            $params['st_range_key_id'] == PublicCode::ST_RANGE_KEY_MAP['st_part'] &&
            $params['value_id'] == PublicCode::ST_RANGE_VALUE_MAP['category']) {
            $allChildren = $this -> StService -> getChildCategory($params['info_ids']);
            logger() -> debug('RFID抽盘且是类目抽盘,获取一级类目子类ID：',[$params['info_ids']]);
            $finalCateIds = array_merge($allChildren,$params['info_ids']);
            $params['info_ids'] = $finalCateIds;
        }
        logger() ->debug('RFID抽盘最终类目ID: ',[$params['info_ids']]);
        try {
            $adminInfo = [
                'admin_id' => $userInfo['uid'],
                'admin_name' => $userInfo['nickname'],
            ];
            $ret = $this -> StService -> addTaskSimple($params,$adminInfo);

            /********************************************记录日志****************************************************/
            // 当前登录账户数据
            $userInfo = $this->session->get('userInfo');
            // 记录日志数据
            $logData = [
                'snow_id' => $this->request->getAttribute('snow_id'),
                'op_id' => $ret['data']['st_id'],
                'op_type' => '创建新盘点任务', // 操作类型
                'op_content' => '盘点ID：'.$ret['data']['st_id'], // 操作内容
                'op_time' => date('Y-m-d H:i:s'), // 操作时间
                'model_name' => 'st', // 操作模块
                'st_status' => 1,  // 盘点状态: 1=初盘中，2=复盘中，3=待审核，4=已完成，5=已作废，6=驳回
                'admin_id' => $userInfo['uid'],
                'admin_name' => $userInfo['nickname'],
                'remark' => "创建盘点任务，盘点ID：".$ret['data']['st_id']
            ];
            wlog((string)$logData['snow_id'], $logData);
            logger() -> debug('创建任务日志记录：', [$logData]);
            /********************************************记录日志****************************************************/
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    /**
     * 临时存储盘点信息值redis
     * @RequestMapping(path="/st/saveStInfo", methods="get,post")
     *
     */
    public function saveStInfo(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'name' => 'required|string',
            'w_id' => 'required|numeric|gt:0',
            'st_range_key_id' => 'required|numeric|gt:0',
//                'value_id' => 'required|numeric|gt:0',
            'st_type' => 'required|numeric|in:1,2,3',
            'start_time' => 'required|string',
            'end_time' => 'required|string',
//                'info_ids' => 'required|string',
        ],[
            'name.required'        => '盘点名称必填',
            'name.string'         => '盘点名称必须字符串',
            'start_time.required'        => '盘点申请开始时间必填',
            'start_time.string'         => '盘点申请开始时间必须是字符串',
            'end_time.required'        => '盘点申请结束始时间必填',
            'end_time.string'         => '盘点申请结束时间必须是字符串',
            'w_id.required'        => '仓库ID必填',
            'st_range_key_id.required'        => '抽盘范围key_id必填',
//                'value_id.required'        => '抽盘范围value_id值必填',
            'w_id.integer'         => '仓库ID必须为正整数',
            'st_type.required'        => '盘点类型必填',
            'st_type.integer'         => '盘点类型必须为正整数',
//                'info_ids.required'         => '请传入盘点范围数据',
//                'info_ids.string'         => '盘点范围数据只能是字符串',
        ]);

        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        if ($params['st_type'] == PublicCode::ST_TYPE_MAP['unique_code'] &&
            $params['st_range_key_id'] == PublicCode::ST_RANGE_KEY_MAP['st_part'] &&
            $params['value_id'] == PublicCode::ST_RANGE_VALUE_MAP['barcode']) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '盘点类型为店内码，抽盘不能选择条码');
        }
        if ($params['st_type'] == PublicCode::ST_TYPE_MAP['barcode'] &&
            $params['st_range_key_id'] == PublicCode::ST_RANGE_KEY_MAP['st_part'] &&
            $params['value_id'] == PublicCode::ST_RANGE_VALUE_MAP['unique_code']) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '盘点类型为条码，抽盘不能选择店内码');
        }

        // 登录用户
        $userInfo = $this->session->get('userInfo');
        if(!$userInfo) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '请登录' );
        }

        $params['st_type_name'] = PublicCode::ST_TYPE[$params['st_type']];
        $params['st_status_name'] = PublicCode::NEW_ST_STATUS[1];
        $params['st_range_key_name'] = PublicCode::ST_RANGE_KEY[$params['st_range_key_id']];
        $params['st_range_value_name'] = PublicCode::ST_RANGE_VALUE[$params['value_id']];
        $params['admin_id'] = $userInfo['uid'];
        $params['admin_name'] = $userInfo['nickname'];
        $params['st_no'] = $this -> SerialNoService -> generate(SerialType::I,(int)$params['st_type']);
        $infoIds = [];
        if ($params['info_ids']) {
            $infoIds = $this -> handleInfoIds($params['info_ids'],$params['value_id']);
        }
        $params['info_ids'] = $infoIds;
        try {
            // 覆盖保存数据
            $redis = redis();
            $key = 'temp_save_st_info_'.$userInfo['uid'];
            $redis -> set($key,json_encode($params));
            $redis -> expire($key, 86400); // 过期时间一天

            // 删除临时部署任务
            $deployKey = 'temp_save_deploy_info_'.$userInfo['uid'];
            $redis -> unlink($deployKey);

            // 获取仓库联系人为初盘复盘人，添加部署任务
            $deployData = $this -> _makeDeployData($params['w_id'],$userInfo);
            $this -> _tempAddDeploy($userInfo,$deployData);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),[]);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    /**
     * 根据缓存中的盘点信息获取对应盘点货架排
     * @RequestMapping(path="/st/getShelfLinesByStInfo", methods="get,post")
     */
    public function getShelfLinesByStInfo(RequestInterface $request)
    {
        // 登录用户
        $userInfo = $this->session->get('userInfo');
        if(!$userInfo) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '请登录' );
        }
        try {
            // 根据盘点任务信息获取货架排
            $shelfLineList = $this -> _getShelfLinesByStInfo($userInfo['uid']);

            if (!$shelfLineList['result']) {
                return $this->returnApi( ErrorCode::REQUEST_ERROR, $shelfLineList['msg']);
            }

            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),makeMergedShelfLine($shelfLineList['data']));
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }

    }

    /**
     * 根据盘点任务信息获取货架排
     * @param $uid
     * @return array
     */
    public function _getShelfLinesByStInfo($uid)
    {
        $result = [
            'result' => false,
            'data' => [],
            'msg'=> '失败'
        ];
        $redis = redis();
        $key = 'temp_save_st_info_'.$uid;
        $stInfoFromRedis = $redis -> get($key);
        if (!$stInfoFromRedis) {
            $result['msg'] = '盘点任务数据已过期，请刷新页面后重试';
            return $result;
        }
        $stInfo = json_decode($stInfoFromRedis,true);

//        $handledInfoIds = $this -> handleInfoIds($stInfo['info_ids'],$stInfo['value_id']);
//        $stInfo['info_ids'] = $handledInfoIds;

        $res = $this -> StService -> getShelfLinesByStInfo($stInfo);
        if (empty($res)) {
            $result['msg'] = '盘点范围内无货架数据';
            return $result;
            //return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),[]);
        }
        $shelfLines = [];
        if ($stInfo['st_range_key_id'] == 2 && $stInfo['value_id'] == 1) {
            $shelfLines = $stInfo['info_ids'];
        } else {
            foreach ($res as $k => $v) {
                $shelfCode = $v['shelf_code'];
                if ($shelfCode) {
                    $shelfArr = explode('-',$shelfCode);
                    $shelfLine = $shelfArr[0].'-'.$shelfArr[1];
                    array_push($shelfLines,$shelfLine);
                }
            }
        }

        $shelfLines = array_unique($shelfLines);
        $shelfLineList = [];
        if ($shelfLines) {
            foreach ($shelfLines as $k => $v) {
                array_push($shelfLineList,[
                    'name' => $v,
                    'value' => $v,
                    'shelf_line' => $v,
                ]);
            }
        }

        $result['result'] = true;
        $result['data'] = $shelfLineList;
        $result['msg'] = '操作成功';

        return $result;
    }

    /**
     * 临时存储任务部署数据
     * @RequestMapping(path="/st/tempAddDeploy", methods="get,post")
     */
    public function tempAddDeploy(RequestInterface $request)
    {
        $params = $request->all();

        $validator = validate()->make($params, [
//            'st_id' => 'required|numeric|gt:0',
            'shelf_codes' => 'required|string',
            'first_admin_id' => 'required|numeric|gt:0',
            'first_admin_name' => 'required|string',
            'second_admin_id' => 'required|numeric|gt:0',
            'second_admin_name' => 'required|string',
        ], [
//            'st_id.required' => '盘点ID必填',
//            'st_id.integer' => '盘点ID必须为正整数',
            'shelf_codes.required' => '货架排必填',
            'shelf_codes.integer' => '货架排必须为字符串',
            'first_admin_id.required' => '预盘人ID必填',
            'first_admin_id.integer' => '预盘人ID正整数',
            'first_admin_name.required' => '预盘人名称必填',
            'first_admin_name.integer' => '预盘人名称必须为字符串',
            'second_admin_id.required' => '复盘人ID必填',
            'second_admin_id.integer' => '复盘人ID必须为正整数',
            'second_admin_name.required' => '复盘人名称必填',
            'second_admin_name.integer' => '复盘人名称必须为字符串',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }
        // 登录用户
        $userInfo = $this->session->get('userInfo');
        if(!$userInfo) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '请登录' );
        }
        try {
            $this -> _tempAddDeploy($userInfo,$params);
            return $this->returnApi(ErrorCode::SUCCESS, "操作成功", []);
        } catch (\Exception $exception) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
        }

    }

    // 添加临时部署
    private function _tempAddDeploy($userInfo,$params) {
        $redis = redis();
        $key = 'temp_save_deploy_info_'.$userInfo['uid'];

        $deployNo = $this -> SerialNoService -> generate(SerialType::I);
        $params['deploy_no'] = $deployNo;
        $redis -> hSet($key,$deployNo,json_encode($params));
    }

    /**
     * 临时任务部署编辑
     * @RequestMapping(path="/st/tempDeployEdit", methods="get,post")
     */
    public function tempDeployEdit(RequestInterface $request)
    {
        $params = $request->all();

        $validator = validate()->make($params, [
//            'id' => 'required|numeric|gt:0',
//            'st_id' => 'required|numeric|gt:0',
            'shelf_codes' => 'required|string',
            'deploy_no' => 'required|string',
            'first_admin_id' => 'required|numeric|gt:0',
            'first_admin_name' => 'required|string',
            'second_admin_id' => 'required|numeric|gt:0',
            'second_admin_name' => 'required|string',
        ], [
//            'id.required' => '部署ID必填',
//            'id.integer' => '部署ID必须为正整数',
//            'st_id.required' => '盘点ID必填',
//            'st_id.integer' => '盘点ID必须为正整数',
            'shelf_codes.required' => '货架号必填',
            'shelf_codes.string' => '货架号必须为字符串',
            'deploy_no.required' => '部署任务编号必填',
            'deploy_no.string' => '部署任务编号必须为字符串',
            'first_admin_id.required' => '预盘人ID必填',
            'first_admin_id.integer' => '预盘人ID正整数',
            'first_admin_name.required' => '预盘人名称必填',
            'first_admin_name.integer' => '预盘人名称必须为字符串',
            'second_admin_id.required' => '复盘人ID必填',
            'second_admin_id.integer' => '复盘人ID必须为正整数',
            'second_admin_name.required' => '复盘人名称必填',
            'second_admin_name.integer' => '复盘人名称必须为字符串',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        // 登录用户
        $userInfo = $this->session->get('userInfo');
        if(!$userInfo) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '请登录' );
        }

        try {
            $redis = redis();
            $key = 'temp_save_deploy_info_'.$userInfo['uid'];
            $deployNo = $params['deploy_no'];
            $redis -> hSet($key,$deployNo,json_encode($params));
            return $this->returnApi(ErrorCode::SUCCESS, "操作成功", []);
        } catch (\Exception $exception) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
        }
    }

    /**
     * 临时任务部署删除
     * @RequestMapping(path="/st/delTempDeployByNo", methods="get,post")
     */
    public function delTempDeployByNo(RequestInterface $request)
    {
        $params = $request->all();

        $validator = validate()->make($params, [
            'deploy_no' => 'required|string',
        ], [
            'deploy_no.required' => '部署任务编号必填',
            'deploy_no.string' => '部署任务编号必须为字符串',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        // 登录用户
        $userInfo = $this->session->get('userInfo');
        if(!$userInfo) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '请登录' );
        }

        try {
            $redis = redis();
            $key = 'temp_save_deploy_info_'.$userInfo['uid'];
            $deployNo = $params['deploy_no'];
            $redis -> hDel($key,$deployNo);
            return $this->returnApi(ErrorCode::SUCCESS, "操作成功", []);
        } catch (\Exception $exception) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
        }
    }

    /**
     * 临时任务部署列表
     * @RequestMapping(path="/st/tempDeployList", methods="get,post")
     */
    public function tempDeployList(RequestInterface $request)
    {
        $params = $request->all();
//        $validator = validate()->make($params, [
//            'st_id' => 'required|numeric|gt:0',
//        ], [
//            'st_id.required' => '盘点ID必填',
//            'st_id.integer' => '盘点ID必须为正整数',
//        ]);
//
//        if ($validator->fails()) {
//            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
//        }

        // 登录用户
        $userInfo = $this->session->get('userInfo');
        if(!$userInfo) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '请登录' );
        }

        try {
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;
            // 获取已部署货架排
            $deployedShelfLines = $this -> _getTempDeployList($userInfo['uid']);
            if (!$deployedShelfLines['result']) {
                return $this->returnApi( ErrorCode::REQUEST_ERROR, $deployedShelfLines['msg'],[]);
            }
            $result = [
                'data' => $deployedShelfLines['data'],
                'total' => count($deployedShelfLines['data'])
            ];
            return $this->returnApi(ErrorCode::SUCCESS, "操作成功", $result);
        } catch (\Exception $exception) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
        }
    }

    /**
     * 获取临时部署任务列表
     * @param $uid
     * @return array
     */
    public function _getTempDeployList($uid)
    {
        $result = [
            'result' => false,
            'data' => [],
            'msg'=> '失败'
        ];
        $redis = redis();
        $key = 'temp_save_deploy_info_'.$uid;
        $deployList = $redis -> hGetAll($key);
        if (!$deployList) {
            $result['msg'] = '未找到部署任务数据';
            return $result;
        }
        $list = [];
        foreach ($deployList as $k => $v) {
            $deployItem = json_decode($v,true);
            array_push($list,$deployItem);
        }
        $result['result'] = true;
        $result['data'] = $list;
        $result['msg'] = '操作成功';
        return $result;
    }

    /**
     * 清空临时盘点任务数据
     * @RequestMapping(path="/st/clearTempStInfoData", methods="get,post")
     */
    public function clearTempStInfoData(RequestInterface $request)
    {
        // 登录用户
        $userInfo = $this->session->get('userInfo');
        if(!$userInfo) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '请登录' );
        }

        try {
            $redis = redis();
            $key = 'temp_save_st_info_'.$userInfo['uid'];
            $redis -> del($key);
            return $this->returnApi(ErrorCode::SUCCESS, "操作成功", []);
        } catch (\Exception $exception) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
        }
    }

    /**
     * 清空临时任务部署
     * @RequestMapping(path="/st/clearTempDeployData", methods="get,post")
     */
    public function clearTempDeployData(RequestInterface $request)
    {
        // 登录用户
        $userInfo = $this->session->get('userInfo');
        if(!$userInfo) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '请登录' );
        }

        try {
            $redis = redis();
            $key = 'temp_save_deploy_info_'.$userInfo['uid'];
            $redis -> del($key);
            return $this->returnApi(ErrorCode::SUCCESS, "操作成功", []);
        } catch (\Exception $exception) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
        }
    }

    /**
     * 清空上传盘点数据时的临时数据
     * @RequestMapping(path="/st/clearTempSbData", methods="get,post")
     */
    public function clearTempSbData(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'st_id' => 'required|numeric|gt:0',
        ], [
            'st_id.required' => '盘点ID必填',
            'st_id.integer' => '盘点ID必须为正整数',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }
        // 登录用户
        $userInfo = $this->session->get('userInfo');
        if(!$userInfo) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '请登录' );
        }

        try {
            $uniqueKey = $userInfo['uid'].'_st_real_data_unique_code_'.$params['st_id']; // 店内码盘点
            $barcodeKey = $userInfo['uid'].'_st_real_data_barcode_'.$params['st_id']; // 条形码盘点
            $key = 1 == $params['st_type'] ? $uniqueKey : $barcodeKey;
            $redis = redis();
            $redis -> del($key);
            return $this->returnApi(ErrorCode::SUCCESS, "操作成功", []);
        } catch (\Exception $exception) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
        }
    }

    /**
     * 获取临时盘点任务详情
     * @RequestMapping(path="/st/getTempStInfo", methods="get,post")
     *
     */
    public function getTempStInfo(RequestInterface $request)
    {
        $params = $request -> all();

        // 登录用户
        $userInfo = $this->session->get('userInfo');
        if(!$userInfo) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '请登录' );
        }

        try {
            $result = $this -> _getTempStInfo($userInfo['uid']);
            logger() -> debug('临时盘点任务详情========',[$result]);
            if (!$result['result']) {
                return $this->returnApi( ErrorCode::REQUEST_ERROR, $result['msg']);
            }
            // 转换infoIds为对应name
            if ($result['data']['value_id'] == 2) { // 2=品牌，3=类目
                $result['data']['info_ids'] = $this -> StService -> convertInfoData($result['data']['info_ids'],'brand');
            }
            if ($result['data']['value_id'] == 3) { // 2=品牌，3=类目
                $result['data']['info_ids'] = $this -> StService -> convertInfoData($result['data']['info_ids'],'category');
            }

            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$result['data']);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    public function _getTempStInfo($uid)
    {

        $result = [
            'result' => false,
            'data' => [],
            'msg'=> '失败'
        ];
        $redis = redis();
        $key = 'temp_save_st_info_'.$uid;
        $res = $redis -> get($key);
        if (!$res) {
            $result['msg'] = '盘点任务数据已过期，请刷新页面后重试';
            return $result;
        }

        $result['result'] = true;
        $result['data'] = json_decode($res,true);
        $result['msg'] = '操作成功';

        return $result;
    }

    /**
     * 检测盘点任务是否已部署完毕
     * @RequestMapping(path="/st/checkIfDeployOk", methods="get,post")
     */
    public function checkIfDeployOk(RequestInterface $request)
    {
        // 登录用户
        $userInfo = $this->session->get('userInfo');
        if(!$userInfo) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '请登录' );
        }
        try {
            // 获取全部货架排
            $allShelfLinesRes = $this -> _getShelfLinesByStInfo($userInfo['uid']);
            if (!$allShelfLinesRes['result']) {
                return $this->returnApi( ErrorCode::REQUEST_ERROR, $allShelfLinesRes['msg']);
            }
            // 获取已部署货架排
            $deployedShelfLinesRes = $this -> _getTempDeployList($userInfo['uid']);
            if (!$deployedShelfLinesRes['result']) {
                return $this->returnApi( ErrorCode::REQUEST_ERROR, $deployedShelfLinesRes['msg']);
            }

            // 比对所有货架排与已部署货架排是否完全部署完毕
            $allShelfLines = array_column($allShelfLinesRes['data'],'value');
            $deployedShelfLines = array_column($deployedShelfLinesRes['data'],'shelf_codes');
            $deployedArr = [];
            foreach ($deployedShelfLines as $k => $v) {
                $shelfArr = explode(',',$v);
                foreach ($shelfArr as $v2) {
                    array_push($deployedArr,$v2);
                }
            }
            // 部署过的货架排去重
            if (!empty($deployedArr)) {
                $deployedArr = array_unique($deployedArr);
            }
            if (count($deployedArr) != count($allShelfLines)) {
                return $this->returnApi( ErrorCode::REQUEST_ERROR, '盘点任务尚未部署完毕');
            }

            return $this -> returnApi(ResponseCode::SUCCESS,'通过！',[]);

        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }

    }

    /**
     * 创建盘点任务并提交部署任务数据
     * @RequestMapping(path="/st/addTaskAndDeploy", methods="get,post")
     */
    public function addTaskAndDeploy(RequestInterface $request)
    {
        // 登录用户
        $userInfo = $this->session->get('userInfo');
        if(!$userInfo) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '请登录' );
        }
        try {
            // 从redis取出盘点任务数据
            $stInfoData = $this -> _getTempStInfo($userInfo['uid']);
            // 从redis取出部署任务数据
            $deployListData = $this -> _getTempDeployList($userInfo['uid']);

            $result = $this -> StService -> addTaskAndDeploy($stInfoData['data'],$deployListData['data']);

            return $this -> returnApi(ResponseCode::SUCCESS,$result['msg'],$result['result']);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 前端轮询获取创建任务结果
     * @RequestMapping(path="/st/getAddTaskResultNew", methods="get,post")
     */
    public function getAddTaskResultNew(RequestInterface $request)
    {
        $userInfo = $this -> session -> get('userInfo');
        if (!$userInfo['uid']) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, "请登录" );
        }
        // 由前端轮询获取结果
        $ResKey = $ResKey = 'add_task_and_deploy_data_'.$userInfo['uid'];
        $resultData = redis() -> rPop($ResKey);
        if ($resultData) {
            $resultData = json_decode($resultData,true);
            if ($resultData["result"]) {
                $returnData = [
                    'result' => "1",
                    'msg' => $resultData["msg"],
                    'data' => $resultData["data"] ? $resultData["data"] : [],
                    'eData' => $resultData["eData"] ? $resultData["eData"] : [],
                ];
                $stId = $resultData["data"]['st_id'];
                /********************************************记录日志****************************************************/
                // 当前登录账户数据
                $userInfo = $this->session->get('userInfo');
                // 记录日志数据
                $logData = [
                    'snow_id' => $this->request->getAttribute('snow_id'),
                    'op_id' => $stId,
                    'op_type' => '创建新盘点任务', // 操作类型
                    'op_content' => '盘点ID：'.$stId, // 操作内容
                    'op_time' => date('Y-m-d H:i:s'), // 操作时间
                    'model_name' => 'st', // 操作模块
                    'st_status' => 1,  // 盘点状态: 0=草稿,1=即将开始，2=正在预盘，3=正在复盘，4=复盘完成，5=已完成，6=已作废
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                    'remark' => "创建盘点任务，盘点ID：".$stId
                ];
                wlog((string)$logData['snow_id'], $logData);
                /********************************************记录日志****************************************************/

                return $this -> returnApi(ResponseCode::SUCCESS,$resultData["msg"],$returnData);
            } else {
                $returnData = [
                    'result' => "0",
                    'msg' => $resultData["msg"],
                    'eData' => $resultData["eData"] ? $resultData["eData"] : [],
                ];
                return $this -> returnApi(ResponseCode::SERVER_ERROR,$resultData["msg"],$returnData);
            }
        }

        return $this -> returnApi(0,"无结果",'');
    }

    private function _makeDeployData ($wId,$userInfo) {
        // 获取仓库联系人
        $wareInfo = $this -> WarehouseService ->getWarehouseOne ((int)$wId);
        if (!$wareInfo) {
            throw new BusinessException('找不到仓库信息');
        }

        // 初盘复盘人都是当前仓库联系人
        // 根据盘点任务信息获取货架排
        $shelfLineList = $this -> _getShelfLinesByStInfo($userInfo['uid']);
        if (!$shelfLineList['result']) {
            throw new BusinessException('盘点任务货架排为空');
        }
        return [
            'first_admin_id' => $wareInfo['contact_admin_id'],
            'first_admin_name' => $wareInfo['contact_name'],
            'second_admin_id' => $wareInfo['contact_admin_id'],
            'second_admin_name' => $wareInfo['contact_name'],
            'shelf_codes' => join(',',array_column($shelfLineList['data'],'shelf_line')),
            'st_id' => 0,
        ];
    }

    /**
     * 删除token
     * @RequestMapping(path="/st/delImpotenceToken", methods="get,post")
     */
    public function delImpotenceToken(RequestInterface $request)
    {
        // 登录用户信息
        $adminInfo = $this->session->get('userInfo');
        delIdempotenceToken($adminInfo['uid']);
        return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),'');
    }

    /**
     * 幂等性校验
     * @param $token
     */
    public function idempotenceCheck($adminId,$token)
    {
        $redis = redis();
        $key = $adminId.'_wms_'.PublicCode::IDEMPOTENCE_TOKEN_KEY;
        $res = $redis -> get($key);

        if ($res && $token == $res) {
            // 校验通过，即可删除
            $redis -> del($key);
            return true;
        }
        return false;
    }

    /**
     * 删除上传的采集缓存数据
     * @RequestMapping(path="/st/delRedisStData", methods="get,post")
     */
    public function delRedisStData(RequestInterface $request)
    {
        $params = $request -> all();

        $validator = validate()->make($params,[
            'w_id' => 'required|integer',
            'st_id' => 'required|integer',
            'st_type' => 'required|integer',
        ],[
            'w_id.required'=>'仓库ID必须',
            'w_id.integer'=>'仓库ID必须是整数',
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_id.integer'=>'盘点ID（st_id）必须是整数',
            'st_type.required'=>'盘点类型必须',
            'st_type.integer'=>'盘点类型必须是整数',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        $userInfo = $this -> session -> get('userInfo');
        $redisKey = $this -> _makeRedisKey($params,$userInfo);
        $tplKey = $redisKey['tpl_key'];
        $redisDataKey = $redisKey['data_key'];
        $redisNormalDataKey = $redisKey['normal_data_key'];
        $redisEDataKey = $redisKey['e_data_key'];
        $redisEDataAllKey = $redisKey['e_data_all_key'];
        $redisInfoKey = $redisKey['info_key'];
        // 先清除上次的数据
        $redis = redis();
        $this -> _clearRedisData ($redis,$redisDataKey,$redisNormalDataKey,$redisEDataKey,$redisEDataAllKey,$redisInfoKey);
        return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),'');

    }

    /**
     * 获取盘点任务未部署的货架
     * @RequestMapping(path="/st/getStUnDeployedShelfLines", methods="get,post")
     */
    public function getStUnDeployedShelfLines(RequestInterface $request)
    {
        $params = $request -> all();

        $validator = validate()->make($params,[
            'st_id' => 'required|integer',
        ],[
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_id.integer'=>'盘点ID（st_id）必须是整数',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        try {
            // 获取未部署的货架
            $result = $this -> StService -> getStUnDeployedShelfLines($params['st_id']);
            $shelfLines = [];
            if ($result) {
                // 去重
                $result = collect($result) -> unique() -> toArray();
                logger() -> debug('result=======================',[$result]);
                foreach ($result as $v) {
                    array_push($shelfLines,[
                        'name' => $v,
                        'value' => $v,
                    ]);
                }
            }
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$shelfLines);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }

    }

    /**
     * 保存部署任务（新增或编辑）
     * @RequestMapping(path="/st/saveDeploy", methods="get,post")
     */
    public function saveDeploy(RequestInterface $request)
    {
        $params = $request -> all();
        logger() -> debug('save_deploy=======',[$params]);

        $validator = validate()->make($params,[
            'st_id' => 'required|integer',
            'first_admin_id' => 'required',
            'second_admin_id' => 'required',
            'first_admin_name' => 'required',
            'second_admin_name' => 'required',
            'shelf_codes' => 'required',
        ],[
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_id.integer'=>'盘点ID（st_id）必须是整数',
            'first_admin_id.required'=>'初盘人id必须',
            'second_admin_id.required'=>'复盘人id必须',
            'first_admin_name.required'=>'初盘人姓名必须',
            'second_admin_name.required'=>'复盘人姓名必须',
            'shelf_codes.required'=>'货架号必须',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        try {
            // 获取未部署的货架
            if ((int)$params['deploy_id'] === 0) {
                // 新增，加上任务编号
                $deployNo = $this -> SerialNoService -> generate(SerialType::I);
                $params['deploy_no'] = $deployNo;
            }
            $result = $this -> StService -> saveDeploy($params);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$result);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }

    }

    /**
     * 保存部署任务（新增或编辑）
     * @RequestMapping(path="/st/delDeploy", methods="get,post")
     */
    public function delDeploy(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params,[
            'st_id' => 'required|integer',
            'deploy_id' => 'required|integer',
        ],[
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_id.integer'=>'盘点ID（st_id）必须是整数',
            'deploy_id.required'=>'部署ID（deploy_id）必须',
            'deploy_id.integer'=>'部署ID（deploy_id）必须是整数',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        try {
            $result = $this -> StService -> delDeploy($params['st_id'],$params['deploy_id']);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$result);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }

    }

    /**
     * 获取盘点任务下的货架排
     * @RequestMapping(path="/st/getShelfLinesBtStId", methods="get,post")
     */
    public function getShelfLinesBtStId(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params,[
            'st_id' => 'required|integer',
        ],[
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_id.integer'=>'盘点ID（st_id）必须是整数',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        try {
            $result = $this -> StService -> getShelfLinesBtStId($params['st_id']);
            if ($result) {
                $shelfLines = collect([]);
                foreach ($result as $v) {
                    $shelfLines -> push([
                        'name' => $v,
                        'value' => $v,
                    ]);
                }
            }
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$shelfLines);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }

    }

    /**
     * 上传异常累计导出
     * @RequestMapping(path="/st/getAcuUploadException", methods="get,post")
     */
    public function getAcuUploadException(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params,[
            'st_id' => 'required|integer',
            'st_type' => 'required|integer',
        ],[
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_id.integer'=>'盘点ID（st_id）必须是整数',
            'st_type.required'=>'盘点类型（st_type）必须',
            'st_type.integer'=>'盘点类型（st_type）必须是整数',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        $userInfo = $this -> session -> get('userInfo');
        $redisKey = $this -> _makeRedisKey($params,$userInfo);
        $redisDataKey = $redisKey['data_key'].self::UPLOAD_SUFFIX;
        logger() -> debug('data-key========================',[$redisDataKey]);

        try {
            $dataAll = $this -> hashService -> getDataAllFromHash($redisDataKey);
            if (!$dataAll) {
                throw new BusinessException('数据已过期或未上传过数据');
            }
            $url = $this -> _exportUploadStData($dataAll);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),['url' => $url]);
//            $result = $this -> hashService -> getDataAllFromHash($redisDataKey);
//            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$result);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }

    }

    /**
     * 将数据以hash结构存入redis
     */
    private function saveDataToHashForSpecifiedKey(string $key, array $data, int $expire)
    {
        logger() -> debug('data-redis===================',[$data]);
        // 使用管道批量发送命
        $pipe = redis() -> pipeline();
        $rowNum = 0;
        foreach ($data as $k => $v) {
            logger() -> debug('key=======',[$v['goods_code']]);
            $pipe -> hSet($key, $v['goods_code'], json_encode($v));
            $rowNum++;
        }

        // 按原来的顺序返回每条指令执行结果数组，1=成功，0=失败
        $ret = $pipe -> exec();

        // 设置过期时间
        redis() -> expire($key, $expire);

        return $ret;
    }

    /**
     * 盘点任务列表
     * @RequestMapping(path="/st/getToken", methods="get,post")
     */
    public function getToken(RequestInterface $request)
    {
        // 登录用户
        $userInfo = $this->session->get('userInfo');

        try {
            $ret['token'] = getIdempotenceToken($userInfo['uid']);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }

    }

    /**
     * 货架采集列表
     * @RequestMapping(path="/st/getShelfGatherList", methods="get,post")
     */
    public function getShelfGatherList(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params,[
            'st_id' => 'required|integer',
        ],[
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_id.integer'=>'盘点ID（st_id）必须是整数',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }
        $where = $params['where'] ?? [];
        if (isset($where['gather_shelf_line']) && !empty($where['gather_shelf_line'])) {
            $where['gather_shelf_line'] = explode(',',$where['gather_shelf_line']);
        }

        // 导出
        $export = $params['export'] ?? 0;

        logger() -> debug('货架采集列表-入参:',[$where]);
        try {
            if (1 == $export) { // 导出全部
                $page = 1;
                $limit = 10000000;
                $ret = $this -> StService -> getShelfGatherList($params['st_id'],$where,$page,$limit);
                if (!$ret['data']) {
                    return $this -> returnApi(ResponseCode::REQUEST_ERROR,'结果为空~~',[]);
                }
                $exportUrl= $this -> exportShelfGatherList($ret['data']);
                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),['url'=>$exportUrl]);
            }
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 10;
            $list = $this -> StService -> getShelfGatherList($params['st_id'],$where,$page,$limit);
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$list);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }

    }
    private function exportShelfGatherList(array $data){

        $head = [
            'gather_shelf_line' => '采集货架排',
            'book_stock_num' => '系统数',
            'remain_goods_num' => '待采集数',
            'real_stock_num' => '采集数',
            'p_num' => '盘盈数',
            'wrong_place_num' => '串位数',
            'inexistence_num' => '不存在',
            'not_in_ware_num' => '不在本仓',
        ];
        $fileName = '盘点货架采集结果 - '.date('Y-m-d-H-i-s');
        $eData = [];
        if (!empty($data)) {
            foreach ($data as $k => $v) {
                array_push($eData,[
                    'gather_shelf_line' => $v['gather_shelf_line'] ?? '',
                    'book_stock_num' => $v['book_stock_num'] ?? 0,
                    'remain_goods_num' => $v['remain_goods_num'] ?? 0,
                    'real_stock_num' => $v['real_stock_num'] ?? 0,
                    'p_num' => $v['p_num'] ?? 0,
                    'wrong_place_num' => $v['wrong_place_num'] ?? 0,
                    'inexistence_num' => $v['inexistence_num'] ?? 0,
                    'not_in_ware_num' => $v['not_in_ware_num'] ?? 0,
                ]);
            }
        }
        return exportToExcel($head,$eData,$fileName);
    }

    /**
     * 获取有实盘记录的采集货架排
     * @RequestMapping(path="/st/getGatherShelfLines", methods="get,post")
     */
    public function getGatherShelfLines(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params,[
            'st_id' => 'required|integer',
        ],[
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_id.integer'=>'盘点ID（st_id）必须是整数',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }
        $where = $params['where'] ?? [];
        if (isset($where['gather_shelf_line']) && !empty($where['gather_shelf_line'])) {
            $where['gather_shelf_line'] = explode(',',$where['gather_shelf_line']);
        }

        logger() -> debug('货架采集列表-入参:',[$where]);
        try {
            $list = $this -> StService -> getGatherShelfLines($params['st_id'],$where);
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$list);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }

    }

    /**
     * 重置采集货架下的采集数据
     * @RequestMapping(path="/st/resetGatherShelfLines", methods="get,post")
     */
    public function resetGatherShelfLines(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params,[
            'st_id' => 'required|integer',
            'gather_shelf_lines' => 'required|string',
        ],[
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_id.integer'=>'盘点ID（st_id）必须是整数',
            'gather_shelf_lines.required'=>'盘点ID（shelf_lines）必须',
            'gather_shelf_lines.string'=>'盘点ID（shelf_lines）必须是字符串',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        logger() -> debug('重置采集货架下的采集数据-入参:',[$params]);
        if (isset($params['gather_shelf_lines']) && !empty($params['gather_shelf_lines'])) {
            $params['gather_shelf_lines'] = explode(',',$params['gather_shelf_lines']);
        }
        try {
            $list = $this -> StService -> resetGatherShelfLines($params['st_id'],$params['gather_shelf_lines']);
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$list);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }

    }

    /**
     * 货架动盘
     * @RequestMapping(path="/st/shelfDynamicSt", methods="get,post")
     */
    public function shelfDynamicSt(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params,[
            'st_id' => 'required|integer',
            'gather_shelf_lines' => 'required|string',
        ],[
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_id.integer'=>'盘点ID（st_id）必须是整数',
            'gather_shelf_lines.required'=>'盘点ID（shelf_lines）必须',
            'gather_shelf_lines.string'=>'盘点ID（shelf_lines）必须是字符串',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        logger() -> debug('货架动盘-入参:',[$params]);
        if (isset($params['gather_shelf_lines']) && !empty($params['gather_shelf_lines'])) {
            $params['gather_shelf_lines'] = explode(',',$params['gather_shelf_lines']);
        }

        try {
            $list = $this -> StService -> shelfDynamicSt($params['st_id'],$params['gather_shelf_lines']);
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$list);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }

    }

    /**
     * 刷线盈亏成本
     * @RequestMapping(path="/st/refreshPlCost", methods="get,post")
     */
    public function refreshPlCost(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params,[
            'st_id' => 'required|integer',
        ],[
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_id.integer'=>'盘点ID（st_id）必须是整数',
        ]);

        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        // 登录用户
        $userInfo = $this->session->get('userInfo');
        if(!$userInfo) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, '请登录' );
        }

        $adminInfo = [
            'admin_id' => $userInfo['uid'],
            'admin_name' => $userInfo['nickname'],
        ];

        logger() -> debug('刷新盈亏成本-入参:',[$params]);
        try {
            $list = $this -> StService -> refreshPlCost($params['st_id'],$adminInfo);
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$list);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }

    }

    /**
     * 计算盈亏金额结果轮询
     * @RequestMapping(path="/st/getPlAmountResult", methods="get,post")
     */
    public function getPlAmountResult(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params,[
            'st_id' => 'required|integer',
        ],[
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_id.integer'=>'盘点ID（st_id）必须是整数',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $userInfo = $this -> session -> get('userInfo');
        if (!$userInfo['uid']) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, "请登录" );
        }

        // 将结果写入redis，由前端轮询获取结果
        $ResKey = 'pl_amount:'.$params['st_id'].':'.$userInfo['uid'];
        $resultData = redis() -> rPop($ResKey);

        if ($resultData) {
            $resultData = json_decode($resultData,true);
            if ($resultData["result"]) {
                $returnData = [
                    'result' => "1",
                    'eData' => $resultData["eData"] ? $resultData["eData"] : [],
                ];
                return $this -> returnApi(ResponseCode::SUCCESS,'计算完成，页面将自动刷新',$returnData);
            }
            $returnData = [
                'result' => "0",
                'eData' => $resultData["eData"] ? $resultData["eData"] : [],
            ];
            return $this -> returnApi(ResponseCode::SERVER_ERROR,$resultData["msg"],$returnData);
        }

        return $this -> returnApi(0,"无结果",'');
    }

    /**
     * 导出采集货架明细
     * @param RequestInterface $request
     * @RequestMapping(path="/st/exportGatherShelfDetail", methods="get,post")
     */
    public function exportGatherShelfDetail(RequestInterface $request)
    {
        $params = $request -> all();
        logger() -> debug('获取某一任务下的实盘结果参数：',[$params]);
        $params = $request -> all();
        $validator = validate()->make($params,[
            'st_id' => 'required|integer',
        ],[
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_id.integer'=>'盘点ID（st_id）必须是整数',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        try {
            $where = $params['where'] ?? [];
            $stId = $params['st_id'];

            if (!isset($where['gather_shelf_line'])) {
                return $this -> returnApi(ResponseCode::REQUEST_ERROR,'请传入货架',[]);
            }
            $where['gather_shelf_line'] = $where['gather_shelf_line'] ? explode(',',$where['gather_shelf_line']) : [];
            logger() -> debug('获取某一任务下的实盘结果where参数：',[$where]);

            $page = 1;
            $limit = -1;
//            // 按系统货架取
//            $sysRet = $this -> StService -> getStResultListByStId((int)$stId, (int)$page, (int)$limit, ['shelf_line' => $where['gather_shelf_line']]);
//            logger() ->debug('按系统货架取',$sysRet);
//            $sysRet = collect($sysRet['data']) -> keyBy('goods_code');
            // 按采集货架取 - final
//            $gatherRet = $this -> StService -> getStResultListByStIdExport((int)$stId, (int)$page, (int)$limit, ['gather_shelf_line' => $where['gather_shelf_line']]);
//            logger() ->debug('按采集货架取',$gatherRet);

//            foreach ($gatherRet['data'] as &$item) {
//                $sysInfo = $sysRet -> get($item['goods_code'],[]);
//                if ($sysInfo) {
//                    // 系统货架取出的系统数和采集货架取出的和
//                    $item['book_stock'] =  $sysInfo['book_stock']; // $item['book_stock'] + $sysInfo['book_stock'];
//                    $item['remain_goods_num'] =  $sysInfo['remain_goods_num']; // $item['remain_goods_num'] + $sysInfo['remain_goods_num'];
//                    $item['l_num'] =  $sysInfo['l_num']; // $item['l_num'] + $sysInfo['l_num'];
//                }
//            }
//            logger() ->debug('最终：',$gatherRet['data']);

            // 导出
//            if (!$gatherRet['data']) {
//                return $this -> returnApi(ResponseCode::REQUEST_ERROR,'结果为空~~',[]);
//            }
//            $exportUrl= $this -> exportStResult($gatherRet['data']);

            $where = ['gather_shelf_line' => $where['gather_shelf_line']];
            $url = $this -> exportStResultBatch($stId,$where);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),['url'=>$url]);

        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 前端轮询获取店内码查询结果
     * @RequestMapping(path="/st/getListNotifyResult", methods="get,post")
     */
    public function getListNotifyResult(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params,[
            'st_id' => 'required|integer',
        ],[
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_id.integer'=>'盘点ID（st_id）必须是整数',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        $userInfo = $this -> session -> get('userInfo');
        $adminId= $userInfo['uid'];

        $key = 'st_result:'.$params['st_id'].':'.$adminId;
        $notifyRet = StResult::getNotifyResult($key);
        logger() -> debug('获取redis结果',[$notifyRet]);
        if ($notifyRet) {
            logger() -> debug('redis取出实盘结果',[$notifyRet]);
            // 取出来完，就清理本次缓存的数据，避免在下载过程中轮询重复请求取出
            $returnData = [
                'result' => "1",
                'msg' => '处理完成，开始下载...',
                'data' => $notifyRet,
            ];
            return $this -> returnApi(ResponseCode::SUCCESS,$returnData['msg'],$returnData);
        } else {
            $returnData = [
                'result' => "0",
                'msg' => '无结果',
                'data' => [],
            ];
            return $this -> returnApi(ResponseCode::SERVER_ERROR,$returnData['msg'],$returnData);
        }

    }


}
