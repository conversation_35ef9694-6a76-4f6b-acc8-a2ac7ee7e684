<?php

namespace App\JsonRpc;


/**
 * epc码解绑日志
 */
interface SkuEpcUnbindLogServiceInterface
{
    /**
     *
     * @param string $operation_id 操作记录ID
     * @param array $data
     * @return bool
     */
    public function operationLog(string $operation_id, array $data);

    /**
     *
     * @param string $operation_id 操作记录ID
     * @param array $data
     * @return bool
     */
    public function batchOperationLog(string $operation_id, array $data);

    /**
     * @param array $where
     * @param int $page
     * @param int $prePage
     * @return array
     */
    public function getOperationLogList(array $where, int $page = 1, int $prePage = 20): array;
}