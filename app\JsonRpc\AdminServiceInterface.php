<?php
declare(strict_types=1);

namespace App\JsonRpc;


interface AdminServiceInterface
{

    /**
     * 根据用户id获取组织架构中相关门店id列表
     * @param int $adminId
     */
    public function organizeShopData (int $adminId);

    /**
     * 查看用户是否有指定权限
     * @param int $adminId 用户id
     * @param string $permissionName 权限名称
     * @param string $appType 系统类型
     * @return mixed
     */
    public function can (int $adminId, string $permissionName, string $appType);

    /**
     * 根据adminId获取用户名
     * @param $adminId
     * @return mixed
     */
    public function idToName ($adminId);


    /**
     * 获取所有用户（不含被禁用的）
     * @param $adminIds
     * @return mixed
     */
    public function users ($fields = ['id', 'username', 'real_name']);

    /**
     * 根据用户编码获取用户信息
     * @param string $code
     * @return string
     */
    public function codeToInfo (string $code);

    /**
     * 获取所有用户id
     */
    public function allIds ();

    /**
     * 根据用户id返回用户姓名列表
     * @param array $adminIds
     * @return mixed
     */
    public function idsToNameList (array $adminIds = []);

    /**
     * 根据用户id获取组织架构中相关仓库id列表
     * @param int $adminId
     */
    public function organizeWareHouseData (int $adminId);

    /**
     * 根据用户id获取组织架构中相关数据
     * @param int $adminId
     */
    public function organizeData (int $adminId);

    /**
     * 根据系统类型获取所有权限列表
     * @param string $appType
     * @return mixed
     */
    public function permissions (string $appType);

    /**
     * 根据仓库/门店id获取所有员工
     * @param int $data_type
     * @param int $rela_id
     * @return array
     */
    public function getAdminByRelaId(int $data_type, int $rela_id);

    /**
     * 根据用户id获取组织架构中相关数据
     * @param int $adminId
     */
    public function organizeUsers(int $adminId);

    // 获取用户
    public function getUsers(array $where = [], array $field = ['*']);
    
    /**
     * 根据用户id返回用户信息
     * @param array $adminIds 用户id
     * @return mixed
     */
    public function getAdminByIds(array $adminIds);
    
    /**
     * 根据手机号获取用户信息
     * @param string $mobile
     * @return array|\Hyperf\Database\Model\Builder|\Hyperf\Database\Model\Model|object
     */
    public function mobileToInfo(string $mobile);

    /**
     * 按管理员Id获取品牌Id
     * @param int $adminId
     * @return array
     */
    public function getBrandIdsByAdminId(int $adminId): array;

    public function tokenGetUserInfo($token);
}

