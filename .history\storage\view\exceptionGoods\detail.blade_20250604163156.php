<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <link href="/static/layui/css/layui.css" rel="stylesheet"/>
    <link href="/static/css/pearCommon.css" rel="stylesheet"/>
    <link href="/static/css/myui.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/static/css/myui.css"/>
    <script src="/static/layui/layui.js" charset="utf-8"></script>
    <style id="pearone-bg-color">
        html, body {
            overflow-y: auto; /* 允许垂直滚动 */
        }

        /* 图片查看器工具栏样式 */
        .image-toolbar {
            background: rgba(0, 0, 0, 0.7);
            border-radius: 5px;
            padding: 8px 10px;
            display: flex;
            align-items: center;
        }
        .image-toolbar span {
            color: white;
            font-size: 12px;
            margin-right: 10px;
            white-space: nowrap;
        }
        .image-toolbar .layui-btn {
            margin: 0 2px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            border: none;
            font-size: 14px;
            min-width: 35px;
        }
        .image-toolbar .layui-btn:hover {
            background: #fff;
            color: #000;
        }
    </style>
</head>
<body class="layui-layout-body pear-admin p-16 pd32">
<div class="mb-100 layui-row layui-col-space10">
    <div class="layui-panel">
        <div class="layui-card">
            <div class="layui-card-header">基本信息</div>
            <div class="layui-card-body row h_ard p-14">
                <table class="layui-table" lay-skin="nob">
                    <tbody>
                    <tr>
                        <td>仓店：{{$detail['ware_name']}}</td>
                        <td>异常类型：{{$detail['exception_type_name']}}</td>
                        <td>状态：{{$detail['status_name']}}</td>
                        <td>备注：{{$detail['remark']}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="layui-card">
            <div class="layui-tab" lay-filter="test-hash">
                <ul class="layui-tab-title">
                    <li class="layui-this" lay-id="11">货品信息</li>
                    <li lay-id="22">操作记录</li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show">
                        <table class="layui-hide" id="list" lay-filter="tableBar"></table>
                    </div>
                    <div class="layui-tab-item">
                        <table class="layui-hide" id="log_list" lay-filter="tableBar"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-card">
            <div class="layui-card-header">异常图1片</div>
            <div class="layui-card-body row p-14">
                @if(!empty($detail['imgs']['exception_img']))
                @foreach($detail['imgs']['exception_img'] as $image)
                <img src="{{$image['image']}}" style="width: 100px;height: 100px;margin-right: 10px;cursor: pointer" onclick="showImageViewer('{{$image['image']}}', '异常图片');">
                @endforeach
                @else
                暂无图片
                @endif
            </div>
        </div>
        <div class="layui-card">
            <div class="layui-card-header">买手备注</div>
            <div class="layui-card-body row p-14">
                <textarea class="layui-textarea" name="remark" id="remark" style="width: 100%;height: 100px;" disabled>{{$detail['buyer_remark']}}</textarea>
            </div>
        </div>
        <div class="layui-card">
            <div class="layui-card-header">去向图片</div>
            <div class="layui-card-body row p-14">
                @if(!empty($detail['imgs']['destination_img']))
                @foreach($detail['imgs']['destination_img'] as $image)
                <img src="{{$image['image']}}" style="width: 100px;height: 100px;margin-right: 10px;cursor: pointer" onclick="showImageViewer('{{$image['image']}}', '去向图片');">
                @endforeach
                @else
                暂无图片
                @endif
            </div>
        </div>
        <div class="layui-card">
            <div class="layui-card-header">仓店备注</div>
            <div class="layui-card-body row p-14 mb-100">
                <textarea class="layui-textarea" name="remark" id="remark" style="width: 100%;height: 100px;" disabled>{{$detail['shop_remark']}}</textarea>
            </div>
        </div>
    </div>
</div>
<div>
    <input type="hidden" name="cate1_name" value="{{$detail['cate1_name']}}">
    <input type="hidden" name="brand_name" value="{{$detail['brand_name']}}">
    <input type="hidden" name="spu_no" value="{{$detail['spu_no']}}">
    <input type="hidden" name="goods_code" value="{{$detail['goods_code']}}">
    <input type="hidden" name="sup_name" value="{{$detail['sup_name']}}">
    <input type="hidden" name="recommend_price" value="{{$detail['recommend_price']}}">
    <input type="hidden" name="destination_name" value="{{$detail['destination_name']}}">
    <input type="hidden" name="real_price" value="{{$detail['real_price']}}">
</div>
<script src="/static/layui/layui.js"></script>
<script>
    layui.config({
        base: '/static/js/' //自定义模块
    }).use(['form','dropdown','element','layer', 'table' ,'laydate','xmSelect','iframeTools','upload'], function () {
        var $ = layui.jquery;
        var form = layui.form;
        var table = layui.table;
        var iframeTools = layui.iframeTools;
        var closeTools = layui.closeTools;
        var xmSelect = layui.xmSelect; // 下拉多选
        var upload = layui.upload;

        var detail = {
            'cate1_name': $('input[name=cate1_name]').val(),
            'brand_name': $('input[name=brand_name]').val(),
            'spu_no': $('input[name=spu_no]').val(),
            'goods_code': $('input[name=goods_code]').val(),
            'sup_name': $('input[name=sup_name]').val(),
            'recommend_price': $('input[name=recommend_price]').val(),
            'destination_name': $('input[name=destination_name]').val(),
            'real_price': $('input[name=real_price]').val()
        }
        console.log('详情',detail);

        table.render({
            elem: "#list"
            ,data: [
                {
                    'cate1_name': detail['cate1_name'],
                    'brand_name': detail['brand_name'],
                    'spu_no': detail['spu_no'],
                    'goods_code': detail['goods_code'],
                    'sup_name': detail['sup_name'],
                    'recommend_price': detail['recommend_price'],
                    'destination_name': detail['destination_name'],
                    'real_price': detail['real_price']
                }
            ]
            , skin: 'line'
            , cellMinWidth: 80 //全局定义常规单元格的最小宽度
            , cols: [[
                {field: 'cate1_name', title: '一级类目'}
                , {field: 'brand_name', title: '品牌名'}
                , {field: 'spu_no', title: '货号'}
                , {field: 'goods_code', title: '店内码/条码'}
                , {field: 'sup_name', title: '供应商'}
                , {field: 'recommend_price', title: '建议售价'}
                , {field: 'destination_name', title: '货品去向'}
                , {field: 'real_price', title: '实际售价'}
            ]]
        });

        // 获取日志
        getLog('#log_list')
        function getLog(position) {

            table.render({
                elem: position
                , url: '/exceptionGoods/getLog'
                , method: "post"
                , where: {
                    id: {{$detail['id']}},
                }
                , parseData: function (res) { //res 即为原始返回的数据
                        console.log('返回结果==',res)
                        return {
                            "code": res.code, //解析接口状态
                            "msg": res.msg, //解析提示文本
                            "count": res.data.total, //解析数据长度
                            "data": res.data.data //解析数据列表
                        };
                    }
                , skin: 'line'
                , cellMinWidth: 80 //全局定义常规单元格的最小宽度
                , cols: [[
                    {title: '序号',type:"numbers"}// 自增序号
                    , {field: 'op_type',title: '操作类型'}
                    , {field: 'op_content',title: '操作内容'}
                    , {field: 'admin_name',title: '操作人'}
                    , {field: 'op_time',title: '操作时间'}
                ]]
                , page: true
            });
        }

    });

    // 增强的图片查看器函数（全局函数）
    function showImageViewer(imageSrc, title) {
        console.log('showImageViewer 被调用', imageSrc, title);

        var rotateAngle = 0; // 旋转角度
        var scale = 1; // 缩放比例
        var originalScale = 1; // 原始缩放比例

        layer.photos({
            photos: {
                title: title,
                data: [{
                    src: imageSrc
                }]
            },
            anim: 5,
            shade: 0.8, // 遮罩透明度
            shadeClose: true, // 点击遮罩关闭
                success: function(layero, index) {
                    console.log('图片查看器打开成功', layero);

                    // 添加简化的控制按钮（只有旋转和还原）
                    var uniqueId = 'toolbar-' + Date.now();
                    var toolbar = '<div class="image-toolbar" style="position: absolute; top: 10px; right: 10px; z-index: 999999; background: rgba(0, 0, 0, 0.8); border-radius: 5px; padding: 8px 10px; display: flex; align-items: center;">' +
                        '<span style="color: white; font-size: 12px; margin-right: 10px;">滚轮缩放</span>' +
                        '<button type="button" class="layui-btn layui-btn-sm rotate-left-btn" data-action="rotate-left" title="左旋转" style="margin: 0 2px; background: rgba(255, 255, 255, 0.9); color: #333; border: none; font-size: 14px; min-width: 35px;">↺</button>' +
                        '<button type="button" class="layui-btn layui-btn-sm rotate-right-btn" data-action="rotate-right" title="右旋转" style="margin: 0 2px; background: rgba(255, 255, 255, 0.9); color: #333; border: none; font-size: 14px; min-width: 35px;">↻</button>' +
                        '<button type="button" class="layui-btn layui-btn-sm reset-btn" data-action="reset" title="还原" style="margin: 0 2px; background: rgba(255, 255, 255, 0.9); color: #333; border: none; font-size: 12px; min-width: 45px;">还原</button>' +
                        '</div>';

                    layero.append(toolbar);
                    console.log('工具栏已添加');

                    var img = layero.find('img');

                    // 应用变换
                    function applyTransform() {
                        img.css({
                            'transform': 'rotate(' + rotateAngle + 'deg) scale(' + scale + ')',
                            'transition': 'transform 0.3s ease'
                        });
                    }

                    // 延迟绑定事件，确保DOM已渲染
                    setTimeout(function() {
                        console.log('开始绑定按钮事件');

                        // 按钮事件绑定
                        layero.find('.rotate-left-btn').on('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log('左旋转按钮点击');
                            rotateAngle -= 90;
                            applyTransform();
                        });

                        layero.find('.rotate-right-btn').on('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log('右旋转按钮点击');
                            rotateAngle += 90;
                            applyTransform();
                        });

                        layero.find('.reset-btn').on('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log('还原按钮点击');
                            rotateAngle = 0;
                            scale = originalScale;
                            applyTransform();
                        });

                        console.log('按钮事件绑定完成，找到按钮数量:', layero.find('.layui-btn').length);
                    }, 100);

                    // 鼠标滚轮缩放 - 改进兼容性
                    layero.on("mousewheel DOMMouseScroll wheel", function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        var delta = 0;
                        if (e.originalEvent.wheelDelta) {
                            delta = e.originalEvent.wheelDelta;
                        } else if (e.originalEvent.detail) {
                            delta = -e.originalEvent.detail;
                        } else if (e.originalEvent.deltaY) {
                            delta = -e.originalEvent.deltaY;
                        }

                        console.log('滚轮事件触发，delta:', delta);

                        if (delta > 0) {
                            // 向上滚动，放大
                            scale += 0.1;
                            console.log('放大，当前缩放:', scale);
                        } else if (delta < 0) {
                            // 向下滚动，缩小
                            if (scale > 0.1) {
                                scale -= 0.1;
                                console.log('缩小，当前缩放:', scale);
                            }
                        }
                        applyTransform();
                    });

                    // 双击还原
                    img.on('dblclick', function() {
                        console.log('双击还原');
                        rotateAngle = 0;
                        scale = originalScale;
                        applyTransform();
                    });

                    // 键盘快捷键（只保留旋转和还原）
                    $(document).on('keydown.imageViewer', function(e) {
                        console.log('键盘按下:', e.keyCode);
                        switch(e.keyCode) {
                            case 27: // ESC 键 - 关闭查看器
                                e.preventDefault();
                                console.log('ESC键 - 关闭查看器');
                                layer.close(index);
                                break;
                            case 37: // 左箭头 - 左旋转
                                e.preventDefault();
                                console.log('左箭头键 - 左旋转');
                                rotateAngle -= 90;
                                applyTransform();
                                break;
                            case 39: // 右箭头 - 右旋转
                                e.preventDefault();
                                console.log('右箭头键 - 右旋转');
                                rotateAngle += 90;
                                applyTransform();
                                break;
                            case 82: // R 键 - 还原
                                e.preventDefault();
                                console.log('R键 - 还原');
                                rotateAngle = 0;
                                scale = originalScale;
                                applyTransform();
                                break;
                        }
                    });

                    // 点击图片外的空白区域关闭（额外保障）
                    layero.on('click', function(e) {
                        // 如果点击的不是图片或工具栏，则关闭
                        if (e.target === layero[0] || $(e.target).hasClass('layui-layer-photos')) {
                            console.log('点击空白区域 - 关闭查看器');
                            layer.close(index);
                        }
                    });
                },
                end: function() {
                    // 移除键盘事件监听
                    $(document).off('keydown.imageViewer');
                }
            });
        }
</script>
</body>
</html>
