<?php


namespace App\Amqp\Consumer;


use Hyperf\Amqp\Builder;
use Hyperf\Amqp\Event\AfterConsume;
use Hyperf\Amqp\Event\BeforeConsume;
use Hyperf\Amqp\Event\FailToConsume;
use Hyperf\Amqp\Event\WaitTimeout;
use Hyperf\Amqp\Exception\MessageException;
use Hyperf\Amqp\Message\ConsumerMessageInterface;
use Hyperf\Amqp\Message\MessageInterface;
use Hyperf\Amqp\Message\Type;
use Hyperf\Amqp\Pool\PoolFactory;
use Hyperf\Amqp\Result;
use Hyperf\Contract\ConfigInterface;
use Hyperf\ExceptionHandler\Formatter\FormatterInterface;
use Hyperf\Process\ProcessManager;
use Hyperf\Utils\Coroutine\Concurrent;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Exception\AMQPTimeoutException;
use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Wire\AMQPTable;
use Psr\Container\ContainerInterface;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\Log\LoggerInterface;

/**
 * Class RichConsumer
 * @package App\Amqp\Consumer
 */
class RichConsumer extends Builder
{
    const QUEUE_TYPE_NORMAL = 1;
    const QUEUE_TYPE_RETRY_FAILED = 2;
    const QUEUE_TYPE_DELAY = 3;

    /**
     * @var bool
     */
    protected $status = true;

    /**
     * @var null|EventDispatcherInterface
     */
    protected $eventDispatcher;

    /**
     * @var LoggerInterface
     */
    private $logger;

    public function __construct(
        ContainerInterface $container,
        PoolFactory $poolFactory
    ) {
        parent::__construct($container, $poolFactory);
        //$this->logger = $logger;
        if ($container->has(EventDispatcherInterface::class)) {
            $this->eventDispatcher = $container->get(EventDispatcherInterface::class);
        }
    }

    /**
     * 声明重试队列
     * @param string $queueName
     * @param string $routingKey
     * @return string
     */
    private function declareRetryQueue(AMQPChannel $channel ,MessageInterface $message): string
    {
        $retryQueueName = $this->getRetryQueueName($message->getQueue());
        $channel->queue_declare(
            $retryQueueName,
            false,
            true,
            false,
            false,
            false,
            new AMQPTable([
                'x-dead-letter-exchange'    => $message->getExchange(),
                'x-dead-letter-routing-key' => $message->getRoutingKey(),
                'x-message-ttl'             => 3 * 1000,
            ])
        );
        $channel->queue_bind($retryQueueName, $this->exchangeRetryTopic($message->getExchange()), $message->getQueue());

        return $retryQueueName;
    }

    /**
     * 声明消费队列
     * @param string $queueName
     * @param string $routingKey
     * @return string
     */
    private function declareConsumeQueue(AMQPChannel $channel ,MessageInterface $message): void
    {
        $channel->queue_declare($message->getQueue(), false, true, false, false, false);
        $channel->queue_bind($message->getQueue(), $message->getExchange(), $message->getRoutingKey());
    }


    /**
     * 声明消费失败队列
     * @param string $queueName
     * @param string $routingKey
     * @return string
     */
    private function declareFailedQueue(AMQPChannel $channel ,MessageInterface $message): void
    {
        $failedQueueName = $this->getFailedQueueName($message->getQueue());
        $channel->queue_declare($failedQueueName, false, true, false, false, false);
        $channel->queue_bind(
            $failedQueueName,
            $this->exchangeFailedTopic($message->getExchange()),
            $message->getQueue()
        );
    }


    public function consume(ConsumerMessageInterface $consumerMessage): void
    {
        $pool = $this->getConnectionPool($consumerMessage->getPoolName());
        /** @var \Hyperf\Amqp\Connection $connection */
        $connection = $pool->get();
        $channel = $connection->getConfirmChannel();

        $this->declare($consumerMessage, $channel);
        $concurrent = $this->getConcurrent($consumerMessage->getPoolName());

        $maxConsumption = $consumerMessage->getMaxConsumption();
        $currentConsumption = 0;

        $channel->basic_consume(
            $consumerMessage->getQueue(),
            $consumerMessage->getConsumerTag(),
            false,
            false,
            false,
            false,
            function (AMQPMessage $message) use ($consumerMessage, $concurrent) {
                $callback = $this->getCallback($consumerMessage, $message);
                if (! $concurrent instanceof Concurrent) {
                    return parallel([$callback]);
                }

                $concurrent->create($callback);
            }
        );

        while ($channel->is_consuming() && ProcessManager::isRunning()) {
            try {
                $channel->wait(null, false, $consumerMessage->getWaitTimeout());
                if ($maxConsumption > 0 && ++$currentConsumption >= $maxConsumption) {
                    break;
                }
            } catch (AMQPTimeoutException $exception) {
                $this->eventDispatcher && $this->eventDispatcher->dispatch(new WaitTimeout($consumerMessage));
            }
        }

        while ($concurrent && ! $concurrent->isEmpty()) {
            usleep(10 * 1000);
        }

        $pool->release($connection);
    }

    public function declare(MessageInterface $message, ?AMQPChannel $channel = null, bool $release = false): void
    {
        if (! $message instanceof ConsumerMessageInterface) {
            throw new MessageException('Message must instanceof ' . ConsumerMessageInterface::class);
        }

        if (! $channel) {
            $pool = $this->getConnectionPool($message->getPoolName());
            /** @var \Hyperf\Amqp\Connection $connection */
            $connection = $pool->get();
            $channel = $connection->getChannel();
        }

        parent::declare($message, $channel,$release);

        $queueType = $message->queueType ?? self::QUEUE_TYPE_NORMAL;
        logger()->info('query pay status start'.$queueType,[$message]);
        if($queueType == self::QUEUE_TYPE_RETRY_FAILED){
            $this->initialize($channel, $message);
            $this->declareRetryQueue($channel, $message);
            $this->declareConsumeQueue($channel, $message);
            $this->declareFailedQueue($channel, $message);
        }

        if($queueType == self::QUEUE_TYPE_DELAY){

        }


        $builder = $message->getQueueBuilder();

        $channel->queue_declare($builder->getQueue(), $builder->isPassive(), $builder->isDurable(), $builder->isExclusive(), $builder->isAutoDelete(), $builder->isNowait(), $builder->getArguments(), $builder->getTicket());

        $routineKeys = (array) $message->getRoutingKey();
        foreach ($routineKeys as $routingKey) {
            $channel->queue_bind($message->getQueue(), $message->getExchange(), $routingKey);
        }

        if (empty($routineKeys) && $message->getType() === Type::FANOUT) {
            $channel->queue_bind($message->getQueue(), $message->getExchange());
        }

        if (is_array($qos = $message->getQos())) {
            $size = $qos['prefetch_size'] ?? null;
            $count = $qos['prefetch_count'] ?? null;
            $global = $qos['global'] ?? null;
            $channel->basic_qos($size, $count, $global);
        }

        if (isset($connection) && $release) {
            $connection->release();
        }
    }

    private function getFailedQueueName(string $queueName): string
    {
        return "{$queueName}@failed";
    }

    private function getRetryQueueName(string $queueName): string
    {
        return "{$queueName}@retry";
    }

    /**
     * 重试交换机Topic
     *
     * @return string
     */
    protected function exchangeRetryTopic(string $exchangeName): string
    {
        return $exchangeName . '.retry';
    }

    /**
     * 失败交换机Topic
     *
     * @return string
     */
    protected function exchangeFailedTopic(string $exchangeName): string
    {
        return $exchangeName . '.failed';
    }


    protected function getConcurrent(string $pool): ?Concurrent
    {
        $config = $this->container->get(ConfigInterface::class);
        $concurrent = (int) $config->get('amqp.' . $pool . '.concurrent.limit', 0);
        if ($concurrent > 1) {
            return new Concurrent($concurrent);
        }

        return null;
    }

    protected function getCallback(ConsumerMessageInterface $consumerMessage, AMQPMessage $message)
    {
        return function () use ($consumerMessage, $message) {
            $data = $consumerMessage->unserialize($message->getBody());
            /** @var AMQPChannel $channel */
            $channel = $message->delivery_info['channel'];
            $deliveryTag = $message->delivery_info['delivery_tag'];

            try {
                $this->eventDispatcher && $this->eventDispatcher->dispatch(new BeforeConsume($consumerMessage));
                $result = $consumerMessage->consumeMessage($data, $message);
                $this->eventDispatcher && $this->eventDispatcher->dispatch(new AfterConsume($consumerMessage, $result));
            } catch (Throwable $exception) {
                $this->eventDispatcher && $this->eventDispatcher->dispatch(new FailToConsume($consumerMessage, $exception));
                if ($this->container->has(FormatterInterface::class)) {
                    $formatter = $this->container->get(FormatterInterface::class);
                    logger()->error($formatter->format($exception));
                } else {
                    logger()->error($exception->getMessage());
                }

                $result = Result::DROP;
            }

            if ($result === Result::ACK) {
                logger()->debug($deliveryTag . ' acked.');
                return $channel->basic_ack($deliveryTag);
            }
            if ($result === Result::NACK) {
                logger()->debug($deliveryTag . ' uacked.');
                return $channel->basic_nack($deliveryTag);
            }
            if ($consumerMessage->isRequeue() && $result === Result::REQUEUE) {
                logger()->debug($deliveryTag . ' requeued.');
                return $channel->basic_reject($deliveryTag, true);
            }

            logger()->debug($deliveryTag . ' rejected.');
            return $channel->basic_reject($deliveryTag, false);
        };
    }

    /**
     * Initialize
     *
     * @return void
     */
    protected function initialize(AMQPChannel $channel, MessageInterface $message)
    {
        // 重试交换机
        $channel->exchange_declare($this->exchangeRetryTopic($message->getExchange()), 'topic', false, true, false);
        // 失败交换机
        $channel->exchange_declare($this->exchangeFailedTopic($message->getExchange()), 'topic', false, true, false);
    }
}