<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Controller;

use App\Constants\ErrorCode;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Exception\ValidateException;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\EpcServiceInterface;
use App\JsonRpc\PickTaskServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use Hyperf\Contract\SessionInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\Utils\Collection;

/**
 * @Controller()
 */
class PickTaskController extends AbstractController
{
    /**
     * @Inject()
     * @var WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject
     * @var PickTaskServiceInterface
     */
    private $PickTaskService;

    /**
     * @Inject()
     * @var SessionInterface
     */
    protected $session;

    /**
     * @Inject
     * @var AdminServiceInterface
     */
    private $AdminService;

    /**
     * @Inject
     * @var EpcServiceInterface
     */
    private $EpcService;

    /**
     * @var string[]
     */
    private $gender = [
        '1' => '男',
        '2' => '女',
        '3' => '儿童',
    ];

    /**
     *
     * @return \Psr\Http\Message\ResponseInterface
     * @RequestMapping(path="/pickTask/lists",methods="post,get")
     */
    public function lists ()
    {
        $warehouse = [];
        $wIds = $this->AdminService->organizeWareHouseData( $this->getUserId() );
        if (!empty( $wIds )) {
            $warehouse['ids'] = $wIds;
        }
        $warehouse_info = collect( $this->WarehouseService->getWarehouses( $warehouse ) )->pluck( 'name', 'id' )->all();
        $pick_task_status = PublicCode::pick_task_status;
        $pick_task_type = PublicCode::pick_task_type;
        if ($this->isAjax()) {
            $where = $this->request->all();
            $where['export'] = $where['export'] ?? 0;
            $where['page_size'] = $where['limit'] ?? $this->pageLimit();
            $info = $this->PickTaskService->getPickTaskLists( $where );
            $result = [];
            if (!empty( $info['data'] )) {
                foreach ($info['data'] as $key => $item) {
                    if ($item['already_num'] == 0) {
                        $item['status'] = 1;
                    } elseif ($item['already_num'] > 0 && $item['already_num'] != $item['number']) {
                        $item['status'] = 2;
                    } elseif ($item['already_num'] == $item['number'] && $item['already_num'] > 0) {
                        $item['status'] = 3;
                    }
                    $result['data'][$key] = $item;
                    $result['data'][$key]['status_name'] = $pick_task_status[$item['status']] ?? '-';
                    $result['data'][$key]['type_name'] = $pick_task_type[$item['type']] ?? '';
                    $result['data'][$key]['w_name'] = $warehouse_info[$item['w_id']] ?? '';
                }
            } else {
                $result['data'] = [];
            }

            if ($where['export'] == 1) {
                if (empty($result['data'])) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
                }
                $url = exportToExcel($this->exportListHeader(), $result['data'], '分拣任务单');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }

            $result['total'] = $info['total'] ?? count( $result['data'] );
            return $this->returnApi( ResponseCode::SUCCESS, '操作成功', $result['data'], ['count' => $result['total'], 'limit' => $where['page_size'], 'export' => $where['export']] );
        }
        return $this->show( 'pickTask/index', [
            'warehouse_info' => $warehouse_info,
            'pick_order_status' => $pick_task_status,
            'pick_order_type' => $pick_task_type,
        ] );
    }

    private function exportListHeader()
    {
        return [
            'id' => '分拣任务ID',
            'w_name' => '仓库名称',
            'type_name' => '类型',
            'number' => '货品数量',
            'already_num' => '已分拣数量',
            'status_name' => '分拣状态',
            'created_at' => '任务单时间',
        ];
    }

    /**
     * @RequestMapping(path="/pickTask/detail/{id}",methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function detail ()
    {
        $warehouse_info = collect( $this->WarehouseService->getWarehouses() )->pluck( 'name', 'id' )->all();
        $pick_task_status = PublicCode::pick_task_status;
        $pick_task_type = PublicCode::pick_task_type;
        $pickId = (int)$this->request->route( 'id' );
        $detail = $this->PickTaskService->getPickTaskDetail( $pickId );
        if ($detail['already_num'] == 0) {
            $detail['status'] = 1;
        } elseif ($detail['already_num'] > 0 && $detail['already_num'] != $detail['number']) {
            $detail['status'] = 2;
        } elseif ($detail['already_num'] == $detail['number'] && $detail['already_num'] > 0) {
            $detail['status'] = 3;
        }
        $detail['type_name'] = $pick_task_type[$detail['type']] ?? '';
        $detail['status_name'] = $pick_task_status[$detail['status']] ?? '';
        $detail['w_name'] = $warehouse_info[$detail['w_id']] ?? '';
        return $this->show( 'pickTask/detail', [
            'detail' => $detail,
        ] );
    }

    /**
     * @RequestMapping(path="/pickTask/detailLists/{id}",methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function detailLists ()
    {
        if ($this->isAjax()) {
            $pick_task_detail_status = PublicCode::pick_task_detail_status;
            $params = $this->request->all();
            $where['pick_task_id'] = (int)$this->request->route( 'id' );
            $where['page'] = $params['page'];
            $where['code'] = $params['code'];
            $where['export'] = $params['export'] ?? 0;
            $where['page_size'] = $params['limit'] ?? $this->pageLimit();
            $info = $this->PickTaskService->getPickTaskDetailLists( $where );
            $result = [];
            if (!empty( $info['data'] )) {
                $unique_codes = array_unique( array_filter( array_column( $info['data'], 'unique_code' ) ) );
                $epc_list = [];
                $epc_info = $this->EpcService->getEpcCodeS( ['unique_codes' => $unique_codes, 'status_list' => [0]] );
                if (!empty( $epc_info )) {
                    $epc_list = collect( $epc_info )->pluck( 'epc_code', 'unique_code' )->toArray();
                }
                foreach ($info['data'] as $key => $item) {
                    $result['data'][$key] = $item;
                    $result['data'][$key]['status_name'] = $pick_task_detail_status[$item['status']] ?? '';
                    $result['data'][$key]['epc_code'] = $epc_list[$item['unique_code']] ?? '';
                }
            } else {
                $result['data'] = [];
            }
            $result['total'] = $info['total'] ?? count( $result['data'] );
            return $this->returnApi( ResponseCode::SUCCESS, '操作成功', $result['data'], ['count' => $result['total'], 'limit' => $where['page_size'], 'export' => $where['export']] );
        }
    }


    /**
     * @RequestMapping(path="/pickTask/pick",methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function pick ()
    {
        if ($this->isAjax()) {
            $data = $this->request->all();
            if (empty( $data['pick_task_id'] )) {
                return $this->returnApi( ErrorCode::SERVER_ERROR, '缺少分拣任务ID' );
            }

            if (empty( $data['unique_code'] )) {
                return $this->returnApi( ErrorCode::SERVER_ERROR, '缺少店内码数据' );
            }

            try {
                //检测是否存在
                $where['unique_code'] = $data['unique_code'];
                $where['pick_task_id'] = $data['pick_task_id'];
                $detail = $this->PickTaskService->getPickTaskDetailListOne( $where );
                if (empty( $detail )) {
                    throw new ValidateException( '未检测改店内码' );
                } else {
                    if ($detail['status'] == PublicCode::pick_task_detail_status_stay) {
                        $update['status'] = PublicCode::pick_task_detail_status_finish;
                        $this->PickTaskService->updatePickTaskDetail( (int)$detail['id'], $update );
                    }
                }
            } catch (\Exception $exception) {
                return $this->returnApi( ErrorCode::SERVER_ERROR, $exception->getMessage(), [
                    'unique_code' => $where['unique_code'],
                    'open_music' => $data['open_music'] ?? 'off',
                ] );
            }
            return $this->returnApi( ResponseCode::SUCCESS, '操作成功', [
                'unique_code' => $where['unique_code'],
                'group_id' => $detail['group_id'],
                'check' => $detail['check'],
                'open_music' => $data['open_music'] ?? 'off',
            ] );
        }
        return $this->returnApi( ErrorCode::SERVER_ERROR, '非法操作' );
    }

    /**
     * 创建分拣任务
     * @RequestMapping(path="/pickTask/createPickTask",methods="get,post")
     */
    public function createPickTask ()
    {
        $group_type = $this->request->post( 'group_type', [] );//默认为空
        $out_ids = $this->request->post( 'out_ids', [] );//出库任务id
        $pick_type = $this->request->post( 'pick_type', 1 );//默认RFID分拣
        if (empty( $group_type )) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, '缺少分拣规则' );
        }

        if (empty( $pick_type )) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, '缺少分拣方式' );
        }

        if (empty( $out_ids )) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, '缺少出库任务单ID' );
        }
        $userInfos = $this->getUserInfo();
        try {
            $userInfo = [
                'admin_id' => $userInfos['uid'] ?? 0,
                'admin_name' => $userInfos['nickname'] ?? '',
            ];
            $result = $this->PickTaskService->createPickTask( (array)$out_ids, (array)$group_type, (int)$pick_type, $userInfo );
        } catch (\Exception $exception) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, $exception->getMessage() );
        }
        return $this->returnApi( ResponseCode::SUCCESS, '创建成功', ['pick_task_id' => $result] );
    }

    /**
     * pickTask API 接口
     * @RequestMapping(path="/pickTask/info",methods="get,post")
     */
    public function info()
    {
        $result = [];
        $sign = $this->request->header('sign');
        if (!empty($sign) && trim($sign) == "63750c5ce2b0ad08da2e8cb1063d478c") {
            $params = $this->request->post();
            if (empty($params['pickTaskId'])) {
                return $this->returnApi(10003, "缺少任务ID！");
            }

            $detail = $this->PickTaskService->getPickTaskDetail((int)$params['pickTaskId']);
            if (empty($detail)) {
                return $this->returnApi(10002, "任务单不存在");
            }

            //查询业务数据
            $where['pick_task_id'] = $params['pickTaskId'];
            $detailInfo = $this->PickTaskService->getPickTaskDetailLists($where);
            $detailInfo = collect($detailInfo['data']);
            if ($detailInfo->isEmpty()) {
                return $this->returnApi(10004, "无任务明细数据");
            }
            $groupLists = $detailInfo->groupBy('group_id');
            $groupMaps = $detailInfo->pluck('group', 'group_id')->all();
            $groups = [];
            foreach ($groupLists as $groupId => $groupVal) {
                /**
                 * @var Collection $groupVal
                 */
                $item['groupId'] = $groupId;
                $item['group'] = $groupMaps[$groupId];
                $item['num'] = $groupVal->count();
                $item['w_name'] = $groupVal[0]['w_name']??'';
                $item['items'] = [];
                foreach ($groupVal as $val) {
                    $item['items'][] = [
                        'id' => $val['id'],
                        'uniqueCode' => $val['unique_code'],
                        'w_name' => $val['w_name'],
                        'barcode' => $val['barcode'],
                        'epcCode' => $val['epc_code'],
                    ];
                }
                $groups[] = $item;
            }

            $result['pickTaskId'] = $detail['id'];
            $result['wId'] = $detail['w_id'];
            $result['type'] = $detail['type'];
            $result['number'] = $detail['number'];
            $result['groups'] = $groups;
            return $this->returnApi(1, "请求成功", $result);
        } else {
            return $this->returnApi(10001, "sign错误！", $result, []);
        }
    }

    /**
     * pickTask API 接口
     * @RequestMapping(path="/pickTask/out",methods="get,post")
     */
    public function out()
    {
        $result = false;
        $sign = $this->request->header('sign');
        if (!empty($sign) && trim($sign) == "63750c5ce2b0ad08da2e8cb1063d478c") {
            $params = $this->request->post();
            if (empty($params['ids'])) {
                return $this->returnApi(10002, "缺少ids参数");
            }
            //默认分拣
            $params['status'] = $params['status']??1;
            //
            if (!in_array($params['status'], [0, 1])) {
                return $this->returnApi(10003, "STATUS状态值错误！");
            }

            $result = $this->PickTaskService->out($params);
            return $this->returnApi(1, "请求成功", $result);
        } else {
            return $this->returnApi(10001, "sign错误！", $result, []);
        }
    }

    /**
     * pickTask API 接口
     * @RequestMapping(path="/pickTask/info1",methods="get,post")
     */
    public function info1 ()
    {
        $sign = $this->request->header( 'sign' );
        if (!empty($sign) && trim( $sign ) == "63750c5ce2b0ad08da2e8cb1063d478c") {
            $result = [
                'resultCode' => -1,
                'resultValue' => [],
                'resultMessage' => ''
            ];
            $params = $this->request->post();
            if (empty( $params['platformNo'] )) {
                $result['resultMessage'] = '缺少platformNo参数';
                return $this->response->json( $result );
            }

            if (empty( $params['codemode'] )) {
                $result['resultMessage'] = '缺少分拣类型信息';
                return $this->response->json( $result );
            }

            if (!empty( $params['codemode'] ) && in_array( $params['codemode'], ['RFID', 'CODE'] )) {
                //查询分拣任务明细
                $where['pick_task_id'] = trim( $params['platformNo'] );
                $detail = $this->PickTaskService->getPickTaskDetailLists( $where );
                if (empty( $detail['data'] )) {
                    $result['resultMessage'] = '无分拣数据';
                    return $this->response->json( $result );
                }

                $info = collect( $detail['data'] )->groupBy( 'group' )->all();
                if (!empty( $info )) {
                    $warehouse_info = collect( $this->WarehouseService->getWarehouses() )->pluck( 'name', 'id' )->all();
                    foreach ($info as $group => $value) {
                        $val['group'] = $group;
                        $val['mendianNUMBER'] = '';
                        $val['Stylesnumb'] = '';
                        if (!empty( $group )) {
                            $groups = explode( "|", $group );
                            if (!empty( $groups )) {
                                foreach ($groups as $gro) {
                                    //仓库
                                    if (in_array( $gro, array_values( $warehouse_info ) )) {
                                        $val['mendianNUMBER'] = $gro;
                                    }
                                    //性别
                                    if (in_array( $gro, array_values( $this->gender ) )) {
                                        $val['Stylesnumb'] = $gro;
                                    }
                                }
                            }
                        }

                        if ($params['codemode'] == 'RFID') {
                            $val['Subnumb'] = 0;
                            $unique_codes = collect( $value )->unique( 'unique_code' )->pluck( 'unique_code' )->toArray();
                            $epc_list = [];
                            $epc_info = $this->EpcService->getEpcCodeS( ['unique_codes' => $unique_codes, 'status_list' => [0]] );
                            if (!empty( $epc_info )) {
                                $epc_list = collect( $epc_info )->pluck( 'epc_code', 'unique_code' )->toArray();
                            }
                            foreach ($value as $v) {
                                if (!empty( $v['unique_code'] ) && isset( $epc_list[$v['unique_code']] )) {
                                    $val['code'][] = $epc_list[$v['unique_code']] . '-' . $v['unique_code'];
                                    $val['Subnumb']++;
                                } else {
                                    $val['code'][] = '' . '-' . $v['unique_code'];
                                }
                            }
                        } else if ($params['codemode'] == 'CODE') {
                            $val['Subnumb'] = count( array_column( $value, 'unique_code' ) );
                            $ar = [];
                            foreach (array_count_values( array_column( $value, 'unique_code' ) ) as $code => $num) {
                                $ar['odcode'] = $code;
                                $ar['num'] = $num;
                                $val['code'][] = $ar;
                                unset( $ar, $code, $num );
                            }
                        }
                        if (isset( $val['Subnumb'] )) {
                            $codenumb[] = $val;
                        }
                        unset( $val, $value );
                    }
                }

                $result['resultCode'] = 1;
                $result['resultMessage'] = '请求成功';
                $result['resultValue'] = [
                    'Value' => [
                        "platformNo" => $params['platformNo'] ?? '',
                        "sequenceNum" => $params['sequenceNum'] ?? '',
                        "codemode" => $params['codemode'],
                        "codenumb" => $codenumb,
                    ],
                ];
                return $this->response->json( $result );
            } else {
                $result['resultCode'] = 1;
                $result['resultMessage'] = '分拣类型错误';
            }
        } else {
            $result = [
                'code' => -1,
                'msg' => '签名错误',
                'data' => [$sign],
            ];
        }
        return $this->response->json( $result );
    }
}