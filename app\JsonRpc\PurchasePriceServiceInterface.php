<?php

namespace App\JsonRpc;


/**
 * Class PurchasePriceService
 * @package App\JsonRpc
 */
interface PurchasePriceServiceInterface
{
    /**
     * 根据商品查询成本价
     * @param array $params
     * [
     *   ['sup_id' => (int)供应商id, 'co_model' => (int)合作模式, 'settle_way' => (int)结算方式, 'sku_ids' => array()],
     *   ['sup_id' => (int)供应商id, 'co_model' => (int)合作模式, 'settle_way' => (int)结算方式, 'sku_ids' => array()],
     *   ['sup_id' => (int)供应商id, 'co_model' => (int)合作模式, 'settle_way' => (int)结算方式, 'sku_ids' => array()]
     * ]
     * @return array
     * [
     *   'sku_id' => [
     *    '供应商id' => [
     *     '合作模式' => [
     *       '结算方式' => [
     *          'id' => 调价详情id,
     *          'sku_id' => sku_id,
     *          'sup_id' => 供应商id,
     *          'brand_id' => 品牌id,
     *          'co_model' => 合作模式,
     *          'settle_way' => 结算方式,
     *          'tag_price' => 吊牌价,
     *          'new_purchase_rate' => 定价,
     *          'new_purchase_price' => 扣率,
     *          'start_time' => 开始时间
     *       ]
     *     ]
     *   ]
     *  ]
     * ]
     */
    public function getSkuPurchasePrice(array $params);

    /**
     * 获取sku成本价列表
     * @param array $where
     * [
     *   'time' => '2021-02-01 10:00:00', //可选，默认为当前时间
     *   'sku_list' => [
     *       ['sup_id' => (int)供应商id, 'co_model' => (int)合作模式, 'settle_way' => (int)结算方式, 'sku_id' => sku_id],
     *       ['sup_id' => (int)供应商id, 'co_model' => (int)合作模式, 'settle_way' => (int)结算方式, 'sku_id' => sku_id],
     *       ['sup_id' => (int)供应商id, 'co_model' => (int)合作模式, 'settle_way' => (int)结算方式, 'sku_id' => sku_id]
     *   ]
     * ]
     * @param array $spuSalePriceMap spu销售价关系表
     */
    public function getSkuPrice(array $where,$spuSalePriceMap = []);
    
}