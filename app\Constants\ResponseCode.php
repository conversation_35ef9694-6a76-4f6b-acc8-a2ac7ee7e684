<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Constants;

use Hyperf\Constants\AbstractConstants;
use Hyperf\Constants\Annotation\Constants;

/**
 * @Constants
 */
class ResponseCode extends AbstractConstants
{
    /**
     * @Message("Server Error！")
     */
    const SERVER_ERROR = 500;

    /**
     * @Message("Request Error！")
     */
    const REQUEST_ERROR = 400;

    /**
     * @Message("Success！")
     */
    const SUCCESS = 200;

    /**
     * @Message("Validate Error!")
     */
    const VALIDATE_ERROR = 600;

    /**
     * @Message("Service Error!")
     */
    const SERVICE_ERROR = 700;
}
