<?php
declare(strict_types=1);

namespace App\Controller;

use App\Constants\ResponseCode;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\EpcServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use App\JsonRpc\SkuEpcUnbindLogServiceInterface;
use App\JsonRpc\UniqueCodeServiceInterface;
use App\JsonRpc\ProduceAreaServiceInterface;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use App\Exception\BusinessException;

/**
 * @Controller()
 */
class EpcController extends AbstractController
{
    /**
     * @Inject()
     * @var ValidatorFactoryInterface
     */
    private $validator;

    /**
     * @Inject()
     * @var AdminServiceInterface
     */
    private $AdminService;

    /**
     * @Inject()
     * @var WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject()
     * @var EpcServiceInterface
     */
    private $EpcService;

    /**
     * @Inject()
     * @var SkuEpcUnbindLogServiceInterface
     */
    private $SkuEpcUnbindLogService;

    /**
     * @Inject()
     * @var UniqueCodeServiceInterface
     */
    private $UniqueCodeService;

    /**
     * @Inject()
     * @var ProduceAreaServiceInterface
     */
    private $ProduceAreaService;

    private $type = [1 => '店内码', 2 => 'RFID'];

    /**
     * 列表
     * @RequestMapping(path="/epc/logList", methods="get,post")
     */
    public function logList()
    {
        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses([], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        // 解绑人
        $adminUser = $this->AdminService->idsToNameList([]);
        // 解绑方式
        $type = $this->type;
        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            //检测需要获取的条数是否大于10000，不允许获取10000条以上的数据
            if ( ($page*$pageLimit) > 10000){
                throw new BusinessException('不允许获取10000条以上的数据，请重新设置筛选条件！');
            }
            $search = $params['search'] ?? [];
            $search['log_type'] = 1;
            //时间修改
            if (isset($search['start_time']) && !empty($search['start_time'])){
                $search['start_time'] = $search['start_time'].' 00:00:00';
            }
            if (isset($search['end_time']) && !empty($search['end_time'])){
                $search['end_time'] = $search['end_time'].' 23:59:59';
            }
            $export = $params['export'] ?? 0;// 0列表 1导出
            if ($export == 0){
                $list = $this->SkuEpcUnbindLogService->getOperationLogList($search, (int)$page, (int)$pageLimit);
                if ($list['data']) {
                    foreach ($list['data'] as $key => &$item) {
                        $item['id'] = ($page-1)*$pageLimit+ $key+1;
                        $item['w_name'] = $warehouse[$item['w_id']] ?? '';
                    }
                }
            }else if($export == 1){
                $data = [];
                $page_id = 1;
                $page_limit = 100;
                while (true){
                    $list = $this->SkuEpcUnbindLogService->getOperationLogList($search, $page_id, $page_limit);
                    $page_id++;
                    if (empty($list['data'])) {
                        break;
                    }

                    foreach ($list['data'] as $key => $item) {
                        $data[] = [
                            'w_name' => isset($warehouse[$item['w_id']]) ? $warehouse[$item['w_id']] : '',
                            'unique_code' => isset($item['unique_code']) ? $item['unique_code'] : '',
                            'epc_code' => isset($item['epc_code']) ? $item['epc_code'] :'',
                            'admin_name' => isset($item['admin_name']) ? $item['admin_name'] : '',
                            'created_at' => isset($item['operation_time']) ? $item['operation_time'] : ''
                        ];
                        $item['w_name'] = $warehouse[$item['w_id']] ?? '';
                    }

                }
                logger()->info('data', [$data]);
                if (empty($data)){
                    throw new BusinessException('无数据，导出失败！');
                }else{
                    try {
                        $url = exportToExcel($this->exportListHeader(), $data, 'rfid解绑记录');
                    }catch (\Exception $e){
                        throw new BusinessException('导出失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
                    }
                }
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
        }

        return $this->show('epc/logList', [
            'warehouse_list' => $warehouse,
            'admin_user' => $adminUser,
            'type' => $type
        ]);
    }

    private function exportListHeader()
    {
        return [
//            'id' => '序号',
            'w_name' => '仓库',
            'unique_code' => '店内码',
            'epc_code' => 'RFID码',
            'admin_name' => '操作人',
            'created_at' => '操作时间'
        ];
    }

    /**
     * 列表
     * @RequestMapping(path="/epc/unbindList", methods="get,post")
     */
    public function unbindList()
    {
        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses([], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        $type = $this->type;
        if ($this->isAjax()) {
            $result = [
                'list' => [],
                'scanned_num' => 0,//已扫描
                'bound_num' => 0,//已绑定
                'not_scanned_num' => 0,//未绑定
            ];
            $params = $this->request->all();
            $search = $params['search'] ?? [];
            if (!$search['type']) {
                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $result);
            }
            $rule = [
                'type' => ['required', 'integer', 'between:1,2'],
                'content' => ['required', 'string']
            ];
            $errors = $this->validator->make($search, $rule);
            if ($errors->fails()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first(), $result);
            }
            $search['content'] = array_filter(explode(',',str_replace(["\r\n", "\r", "\n","\t","\s"], ",", $search['content'])));
            if (empty($search['content'])){
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '店内码或RFID不能为空', $result);
            }
            $search['codes'] = array_values($search['content']);
//            if (count($search['codes']) > 500){
//                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
//            }
            $result['scanned_num'] = count($search['codes']);
            $bound_num = $not_scanned_num = 0;
            $data = [];
            if (isset($search['type']) && $search['type'] == 1) {
                //检测店内码是否存在
                $unique_code_list = $this->UniqueCodeService->getUniqueCodes($search['codes'], ['w_id', 'unique_code']);
                if (empty($unique_code_list)){
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '要解绑的店内码不存在,请确认！', $result);
                }
                $error_message = [];
                foreach ($unique_code_list as $u_item){
                    if (!empty($search['w_id']) && $u_item['w_id'] != $search['w_id']){
                        $error_message[] = $u_item['unique_code'].'店内码，不是属于选择仓库不予解锁';
                    }
                }
                if (!empty($error_message)){
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, implode("<br>", $error_message), $result );
                }

                $unique_code_map = [];
                $unique_code_map = array_column($unique_code_list, 'w_id', 'unique_code');

                // 店内码
                $epc_where = [
                    'unique_codes' => $search['codes'],
                    'get_epc_list' => true
                ];
                if (!empty($search['w_id'])){
                    $epc_where['w_id'] = $search['w_id'];
                }
               $list = $this->EpcService->getCodes($epc_where);
//               if (empty($list)){
//                   return $this->returnApi(ResponseCode::VALIDATE_ERROR, '所有店内码未绑定RFID,请确认！', $result);
//               }

               $hasCodes = collect($list)->groupBy(['unique_code'])->toArray();

               foreach ($search['codes'] as $key => $code) {
                   if (isset($hasCodes[$code])) {
                       $bound_num++;
                       $data[] = [
                           'id' => $key+1,
                           'epc_code' => $hasCodes[$code][0]['epc_code'],
                           'unique_code' => $code,
                           'w_name' => $warehouse[$hasCodes[$code][0]['w_id']] ?? '',
                           'status_txt' => $hasCodes[$code][0]['status'] == 2 ? '解绑' : '绑定',
                           'remark' => '',
                           'status' => $hasCodes[$code][0]['status']
                       ];
                   } else {
                       $not_scanned_num++;
                       $data[] = [
                           'id' => $key+1,
                           'epc_code' => '',
                           'unique_code' => $code,
                           'w_name' => $warehouse[$unique_code_map[$code]] ?? '',
                           'status_txt' => '未绑定RFID',
                           'remark' => '',
                           'status' => $hasCodes[$code][0]['status']
                       ];
                   }
               }
            } else if (isset($search['type']) && $search['type'] == 2) {
                // epc
                $epc_where = [
                    'epc_codes' => $search['codes'],
                    'get_epc_list' => true
                ];
                if (!empty($search['w_id'])){
                    $epc_where['w_id'] = $search['w_id'];
                }
                $list = $this->EpcService->getCodes($epc_where);
                if (empty($list)){
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '要解绑的RFID不存在,请确认！', $result);
                }
                $hasCodes = collect($list)->groupBy(['epc_code'])->toArray();
                foreach ($search['codes'] as $key => $code) {
                    if (isset($hasCodes[$code])) {
                        $bound_num++;
                        $data[] = [
                            'id' => $key+1,
                            'epc_code' => $code,
                            'unique_code' => !empty($hasCodes[$code][0]['unique_code']) ? $hasCodes[$code][0]['unique_code'] : $hasCodes[$code][0]['bar_code'],
                            'w_name' => $warehouse[$hasCodes[$code][0]['w_id']] ?? '',
                            'status_txt' => $hasCodes[$code][0]['status'] == 2 ? '解绑' : '绑定',
                            'remark' => '',
                            'status' => $hasCodes[$code][0]['status']
                        ];
                    } else {
                        $not_scanned_num++;
                        $data[] = [
                            'id' => $key+1,
                            'epc_code' => $code,
                            'unique_code' => '',
                            'w_name' => '',
                            'status_txt' => '未绑定店内码',
                            'remark' => '',
                            'status' => 2
                        ];
                    }
                }
            }
            $result['bound_num'] = $bound_num;
            $result['not_scanned_num'] = $not_scanned_num;
            $result['list'] = $data;
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $result);
        }

        return $this->show('epc/unbindList', [
            'warehouse_list' => $warehouse,
            'type' => $type
        ]);
    }

    /**
     * 获取epc绑定关系
     * @RequestMapping(path="/epc/bindInfo", methods="get,post")
     */
    public function epcBindInfo(){
        //检测店内码和epc码是否存在绑定关系
        $params = $this->request->all();
        if (!isset($params['epc_code']) || empty($params['epc_code'])){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, 'RFID码不能为空');
        }

        //检测店内码和epc是否存在绑定关系
        $epc_info = $this->EpcService->getEpcCode(['epc_code' => $params['epc_code']]);
        if (empty($epc_info)){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, 'RFID码不存在绑定关系');
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $epc_info);
    }

    /**
     * 解绑
     * @RequestMapping(path="/epc/unbindEpc", methods="get,post")
     */
    public function unbindEpc(){
        //检测店内码和epc码是否存在绑定关系
        $params = $this->request->all();
        //检测店内码和epc是否存在绑定关系
        $epc_info = $this->EpcService->getEpcCode(['epc_code' => $params['epc_code'], 'unique_code' => $params['unique_code']]);
        $bar_code_info = $this->EpcService->getEpcCode(['epc_code' => $params['epc_code'], 'bar_code' => $params['unique_code']]);
        if (empty($epc_info) && empty($bar_code_info)){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '店内码和RFID码不存在绑定关系');
        }

        $userInfo = $this->session->get('userInfo');
        $this->EpcService->unboundEpc(['epc' => [['incode' => $params['unique_code'], 'rfid' => $params['epc_code']]], 'login_realname' => $userInfo['nickname'], 'login_admin_id' => $userInfo['uid']]);

        $result = [
            'unbound_num' => 1,
            'unbinding_failed_num' => 0
        ];
        return $this->returnApi(ResponseCode::SUCCESS, '解绑成功', $result);
    }

    /**
     * 校验店内码是否可解绑
     * @RequestMapping(path="/epc/checkUniquesInfo", methods="get,post")
     */
    public function checkUniquesInfo(){
        $params = $this->request->all();
        if (empty($params['data'])){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数不能为空');
        }

        $errorCode = $this->checkUniques($params['data']);

        return $this->returnApi(ResponseCode::SUCCESS, '校验成功', $errorCode);
    }

    /**
     * 校验店内码是否可解绑
     * @RequestMapping(path="/epc/checkUniqueInfo", methods="get,post")
     */
    public function checkUniqueInfo(){
        $params = $this->request->all();
        if (empty($params['unique_code'])){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数不能为空');
        }

        $data[] = ['unique_code' => $params['unique_code']] ;
        $errorCode = $this->checkUniques($data);

        return $this->returnApi(ResponseCode::SUCCESS, '校验成功', $errorCode);
    }

    private function checkUniques(array $data){
        $wIds = $this->AdminService->organizeWareHouseData($this->getUserId());

        //获取店内码的在库信息
        $uniqueCodes = array_filter(array_column($data, 'unique_code'));
        $uniqueStockList = $this->UniqueCodeService->getUniqueCodes($uniqueCodes, ['*']);
        $uniqueStockMap = array_combine(array_column($uniqueStockList, 'unique_code'), array_values($uniqueStockList));

        //获取生产区信息
        $produceAreaList = $this->ProduceAreaService->getDetailS(['status' => 0, 'sign_type' => 1, 'unique_codes' => $uniqueCodes], ["*"]);
        $produceAreaMap = array_combine(array_column($produceAreaList, 'unique_code'), array_values($produceAreaList));

        $errorCode = [];

        foreach ($data as $item){
            if (isset($uniqueStockMap[$item['unique_code']]) ){
                if (!in_array($uniqueStockMap[$item['unique_code']]['w_id'], $wIds) && (in_array($uniqueStockMap[$item['unique_code']]['status'], [1,3]) ||  ($uniqueStockMap[$item['unique_code']]['status'] == 2 && $uniqueStockMap[$item['unique_code']]['in_w_id'] !== 0))){
                    $errorCode[] = $item['unique_code'];
                    continue;
                }

                if ($uniqueStockMap[$item['unique_code']]['status'] == 2 && isset($produceAreaMap[$item['unique_code']]) && !in_array($uniqueStockMap[$item['unique_code']]['w_id'], $wIds)){
                    $errorCode[] = $item['unique_code'];
                    continue;
                }
            }
        }
        return $errorCode;
    }

    /**
     * 解绑
     * @RequestMapping(path="/epc/unbindEpcList", methods="get,post")
     */
    public function unbindEpcList(){
        $params = $this->request->all();
        if (empty($params['data'])){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数不能为空');
        }
        $epc_codes = array_filter(array_column($params['data'], 'epc_code'));
        if (empty($epc_codes)){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '需要解绑的RFID为空');
        }
        $epc_list = $this->EpcService->getEpcCodeS(['epc_codes' => $epc_codes]);
        if (empty($epc_list)){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '需要解绑的RFID未查询到，请确认！');
        }

        $notUniqueCode = $this->checkUniques($params['data']);

        //检测传递的epc在数据库中是否存在
        $rfid_codes = array_column($epc_list, 'epc_code');
        $error_message = [];
        foreach ($epc_codes as $code){
            if (!in_array($code, $rfid_codes)){
                $error_message[] = 'RFID '.$code.' 不存在';
            }
        }
        if (!empty($error_message)){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, implode("\n\r", $error_message));
        }

        //获取店内码的在库信息
        $uniqueCodes = array_filter(array_column($params['data'], 'unique_code'));
        $uniqueStockList = $this->UniqueCodeService->getUniqueCodes($uniqueCodes, ['*']);
        $uniqueStockMap = array_combine(array_column($uniqueStockList, 'unique_code'), array_values($uniqueStockList));

        //获取生产区信息
        $produceAreaList = $this->ProduceAreaService->getDetailS(['status' => 0, 'sign_type' => 1, 'unique_codes' => $uniqueCodes], ["*"]);
        $produceAreaMap = array_combine(array_column($produceAreaList, 'unique_code'), array_values($produceAreaList));


        $unbind_info = [];
        foreach ($params['data'] as $item){
            if (!in_array($item['unique_code'], $notUniqueCode)){
                $unbind_info[] = ['incode' => $item['unique_code'], 'rfid' => $item['epc_code']];
            }
        }
        $userInfo = $this->session->get('userInfo');
        if (!empty($unbind_info)){
            try {
                $this->EpcService->unboundEpc(['epc' => $unbind_info, 'login_realname' => $userInfo['nickname'], 'login_admin_id' => $userInfo['uid']]);
                $result = [
                    'unbound_num' => count($epc_codes),
                    'unbinding_failed_num' => count($params['data']) - count($epc_codes)
                ];
                return $this->returnApi(ResponseCode::SUCCESS, '解绑成功', $result);
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '解绑失败');
            }
        }else{
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无符合解绑的商品，请确认后再解绑');
        }
    }
}