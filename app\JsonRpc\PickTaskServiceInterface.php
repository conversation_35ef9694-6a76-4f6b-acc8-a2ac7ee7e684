<?php
declare(strict_types=1);

namespace App\JsonRpc;

interface PickTaskServiceInterface
{
    /**
     * 获取分拣任务列表
     * @param array $where
     * @return mixed
     */
    public function getPickTaskLists (array $where);

    /**
     * 获取分拣任务详情
     * @param int $id
     * @param array|string[] $field
     * @return mixed
     */
    public function getPickTaskDetail (int $id, array $field = ['*']);

    /**
     * 获取分拣任务明细列表
     * @param array $where
     * @return mixed
     */
    public function getPickTaskDetailLists (array $where);

    /**
     * 获取单条分拣任务明细数据
     * @param array $where
     * @return mixed
     */
    public function getPickTaskDetailListOne (array $where);

    /**
     * 修改单条分拣任务明细数据
     * @param int $id
     * @param array $data
     * @return mixed
     */
    public function updatePickTaskDetail (int $id, array $data);


    /**
     * 创建分拣任务
     * @param array $outIds
     * @param array $groupType
     * @param int $pickType
     * @param array $pickInfo
     * @return mixed
     */
    public function createPickTask (array $outIds, array $groupType, int $pickType, array $pickInfo);

    /**
     * 批量分拣出库接口
     * @return mixed
     */
    public function out(array $params);
}