<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use App\Constants\ErrorCode;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Constants\SerialType;
use App\Exception\BusinessException;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\SerialNoServiceInterface;
use App\JsonRpc\ShelfGoodsCodeMapServiceInterface;
use App\JsonRpc\ShelfMapServiceInterface;
use App\JsonRpc\ShelfServiceInterface;
use App\JsonRpc\SkuBarcodeServiceInterface;
use App\JsonRpc\TallyServiceInterface;
use App\JsonRpc\TaskServiceInterface;
use App\JsonRpc\UniqueCodeLogServiceInterface;
use App\Library\RedisLock;
use App\Library\Tally\Handle\CacheData;
use App\Library\Tally\Handle\Context as TallyHandleContext;
use App\Library\Tally\Log\WriteUniqueCodeLog;
use App\Library\Tally\PlanTally\PlanTally;
use App\Library\Tally\TallyConstants;
use App\Service\HashService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Utils\Parallel;
use phpDocumentor\Reflection\PseudoTypes\False_;

/**
 * @Controller()
 */
class TallyController extends AbstractController
{

    /**
     * @Inject ()
     * @var SkuBarcodeServiceInterface
     */
    private $SkuBarcodeService;

    /**
     * @Inject ()
     * @var UniqueCodeLogServiceInterface
     */
    private $UniqueCodeLogService;

    /**
     * @Inject ()
     * @var TallyServiceInterface
     */
    private $TallyService;

    /**
     * @Inject ()
     * @var ShelfServiceInterface
     */
    private $ShelfService;


    /**
     * @Inject ()
     * @var ShelfGoodsCodeMapServiceInterface
     */
    private $ShelfGoodsCodeMapService;

    /**
     * @Inject ()
     * @var AdminServiceInterface
     */
    private $AdminService;

    /**
     * @Inject ()
     * @var HashService
     */
    private $hashService;

    /**
     * @Inject ()
     * @var SerialNoServiceInterface
     */
    private $SerialNoService;

    /**
     * @Inject ()
     * @var TaskServiceInterface
     */
    private $TaskService;

    /**
     * 上传理货数据页面
     * @RequestMapping(path="/tally/create", methods="get,post")
     */
    public function create(RequestInterface $request)
    {
        return $this -> show("/tally/create");
    }

    /**
     * 上传理货数据页面
     * @RequestMapping(path="/tally/detail", methods="get,post")
     */
    public function detail(RequestInterface $request)
    {
        $params = $request -> all();
        $id = $params['id'];

        try {
            $detailData = $this -> TallyService -> getTallyOne($id);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
        return $this -> show("/tally/detail",['detail' => $detailData]);

    }

    /**
     * 任务详情 - 清单列表
     * @RequestMapping(path="/tally/billList", methods="get,post")
     */
    public function billList(RequestInterface $request)
    {
        $params = $request -> all();
        $tId = $params['id'];
        $export = $params['export'];
        $page = $params['page'] ?? [];
        $limit = $params['limit'] ?? [];
        $where = $params['where'] ?? [];

        try {
            $ret = $this -> TallyService -> getBillList((int)$tId,(int)$page,(int)$limit,$where,['*']);
            if ($export == 1) {
                $url = $this -> exportTallyBillData($tId,$where);
                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),['url' => $url]);
            }
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    /**
     * 上传理货数据接口
     * @RequestMapping(path="/tally/uploadTallyData", methods="get,post")
     */
    public function uploadTallyData(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'w_id' => 'required|integer',
            't_type' => 'required|integer',
        ],[
            'w_id.required'=> '请选择仓库',
            'w_id.integer'=> '仓库id必须是正整数',
            't_type.required'=> '理货类型必填',
            't_type.integer'=> '理货类型必须是正整数',
        ]);

        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        $userInfo = $this -> session -> get('userInfo');
        $adminInfo = [
            'admin_id' => $userInfo['uid'],
            'admin_name' => $userInfo['nickname'],
        ];

        if (!$request->hasFile('file')) {
            return $this->returnApi(ErrorCode::REQUEST_FILE_ERROR, ErrorCode::getMessage(ErrorCode::REQUEST_FILE_ERROR));
        }
        $file = $request->file('file');
        $file -> getPathInfo();
        $tplKey = 1 == $params['t_type'] ? 'tally_unique_code_txt' : 'tally_barcode_txt';
        $metaData = getTemplateInfo($tplKey);
        $metaData = explode(',',$metaData['key']);
        // 根据店内码找出原货架信息组装数据
        try {
            $t_1 = microtime(true);
            $t1 = microtime(true);
            $txtData = readTxt($file,$metaData);
            logger() -> debug('读取txt耗时-代码耗时：'.number_format(microtime(true)-$t1, 3, '.', '').' s');
            if (count($txtData) > 30000) {
                return $this -> returnApi(ErrorCode::REQUEST_ERROR,'单次上传最大数据量不得超过3w','');
            }

            $t1 = microtime(true);
            // 将数组内指定字段转换成大写
            if (1 == $params['t_type']) { // 店内码
                arrayValStrConvert($txtData,['unique_code' => 'to_upper','shelf_code' => 'to_upper']);
            }
            if (2 == $params['t_type']) { // 条码
                arrayValStrConvert($txtData,['old_shelf_code' => 'to_upper','new_shelf_code' => 'to_upper']);
            }
            logger() -> debug('将数组内指定字段转换成大写-代码耗时：'.number_format(microtime(true)-$t1, 3, '.', '').' s');

            // 检查理货人是否都存在，不存在的话，终止执行
            $t1 = microtime(true);
            $adminIdsFromUpload = array_column($txtData,'admin_id');
            $adminInfoList = $this -> AdminService -> idsToNameList(array_unique($adminIdsFromUpload));
            $adminIdsSys = array_keys($adminInfoList);
            $diffs = array_diff($adminIdsFromUpload,$adminIdsSys);
            logger() -> debug('检查理货人是否都存在-代码耗时：'.number_format(microtime(true)-$t1, 3, '.', '').' s');

            if (!empty($diffs)) {
                return $this -> returnApi(ErrorCode::REQUEST_ERROR,'id为：'.join('，',array_unique($diffs)).'的理货人不存在','');
            }

            // 处理数据
            //$handledData = $this -> handleData($params['w_id'],$params['t_type'],$txtData,$adminInfo,$adminInfoList);
            $strategy = TallyHandleContext::getStrategy((int)$params['t_type']);
            $handledData = (new $strategy) -> exec([
                'w_id' => $params['w_id'],
                't_type' => $params['t_type'],
                'data' => $txtData,
                'admin_info' => $adminInfo,
                'admin_info_list' => $adminInfoList,
            ]);

            // 将处理过的数据缓存起来
            $t1 = microtime(true);
            CacheData::save([
                't_type' => $params['t_type'],
                'handled_data' => $handledData,
                'admin_info' => $adminInfo,
            ]);
            logger() -> debug('写入redis-代码耗时：'.number_format(microtime(true)-$t1, 3, '.', '').' s');
            logger() -> debug('总耗时-代码耗时：'.number_format(microtime(true)-$t_1, 3, '.', '').' s');

            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",'');
        } catch (\Exception | BusinessException $exception ) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,$exception -> getMessage(),'');
        }

    }

    /**
     * 根据店内码找出原货架信息组装数据
     * @param $wId
     * @param $tType
     * @param $data
     * @param $adminInfo
     * @param $adminInfoList
     * @return array
     */
//    private function handleData($wId,$tType,$data,$adminInfo,$adminInfoList)
//    {
//        if (1 == $tType) { // 店内码
//            return $this -> _uniqueCodeHandleNew($wId,$tType,$data,$adminInfo,$adminInfoList);
//        }
//        if (2 == $tType) { // 条码
//            return $this -> _barcodeHandle($wId,$tType,$data,$adminInfo,$adminInfoList);
//        }
//        return [];
//    }

    private function _perfectBarcodeData($data,$tallyAdminInfo) {
        foreach ($data as $k => &$v) {
            $v['t_admin_name'] = $tallyAdminInfo[$v['admin_id']];
            $v['t_admin_id'] = $v['admin_id'];
        }
        return $data;
    }

    private function _searchBarcodeStock ($stockList,$wId,$shelfCode,$barcode) {
        foreach ($stockList as $k => $v) {
            if ($v['w_id'] == $wId && $v['barcode'] == $barcode && $v['shelf_code'] == $shelfCode) {
                return $k;
            }
        }
        return -1;
    }

    private function _getLastPreKey ($dataC,$uniqueCode) {
        $keys = $dataC -> where('unique_code',$uniqueCode)
            -> where('exception','=',NULL)
            -> where('is_repeat','=',true)
            -> keys();
        return $keys -> last() !== null && $keys -> last() >= 0 ? $keys -> last() : -1;
    }

    /**
     * 任务详情 - 从redis获取上传的理货数据列表
     * @RequestMapping(path="/tally/getUploadTallyData", methods="get,post")
     */
    public function getUploadTallyData(RequestInterface $request)
    {
        $params = $request -> all();

        // 将上传的盘点数据存入redis
        $userInfo = $this -> session -> get('userInfo');
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 10;
        try {
            $dataKey = '';
            if ($params['show_type'] == 'all') {
                $dataKey = 1 == $params['t_type'] ? $userInfo['uid'].'_tally_data_unique_code' : $userInfo['uid'].'_tally_data_barcode';
            }
            if ($params['show_type'] == 'normal') {
                $dataKey = 1 == $params['t_type'] ? $userInfo['uid'].'_tally_normal_data_unique_code' : $userInfo['uid'].'_tally_normal_data_barcode' ;
            }
            if ($params['show_type'] == 'exception') {
                $dataKey = 1 == $params['t_type'] ? $userInfo['uid'].'_tally_e_data_unique_code' : $userInfo['uid'].'_tally_e_data_barcode' ;
            }
            if (empty($dataKey)) {
                return $this -> returnApi(ErrorCode::SERVER_ERROR,"缓存key不能为空",[]);
            }
            $infoKey = 1 == $params['t_type'] ? $userInfo['uid'].'_tally_info_unique_code' : $userInfo['uid'].'_tally_info_barcode';
            $dataResult = $this -> hashService -> getDataListFromHash($dataKey,(int)$page,(int)$limit);
            $infoResult = redis() -> get($infoKey);
            if (!$infoResult || !$dataResult) {
                return $this -> returnApi(ErrorCode::SERVER_ERROR,"数据获取失败",[]);
            }
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",['token' => getIdempotenceToken($userInfo['uid']),'info' => json_decode($infoResult),'data' => $dataResult]);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 确认转架 - 保存上传的数据
     * @RequestMapping(path="/tally/saveData", methods="get,post")
     */
    public function saveData(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            't_type' => 'required|integer',
            'w_id' => 'required|integer',
            'token' => 'required|string',
        ],[
            't_type.required'=> '理货类型必填',
            't_type.integer'=> '理货类型必须为数字',
            'w_id.required'=> '仓库必填',
            'w_id.integer'=> '仓库id必须为数字',
            'token.required'=> '请求令牌不存在',
        ]);

        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        // 登录用户信息
        $userInfo = $this->session->get('userInfo');

        // 请求幂等性校验
//        $checkRes = $this -> idempotenceCheck($userInfo['uid'],$params['token']);
//        if (!$checkRes) {
//            return $this->returnApi( ErrorCode::REQUEST_ERROR, '请勿重复请求' );
//        }
        $token = RedisLock::lock(CacheData::getKey(CacheData::TALLY_ADD_LOCK,$userInfo['uid']),CacheData::TALLY_ADD_LOCK_TIME);
        if($token === false) {
            throw new BusinessException("请求过于频繁，请稍后再试!");
        }

        //$redisKey = 1 == $params['t_type'] ? $adminInfo['uid'].'_tally_data_unique_code' : $adminInfo['uid'].'_tally_data_barcode';
        $redisKey = 1 == $params['t_type'] ? $userInfo['uid'].'_tally_info_unique_code' : $userInfo['uid'].'_tally_info_barcode';
        $tallyData = redis() -> get($redisKey);
        if (!$tallyData) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,'页面数据已过期，请重新上传');
        }
        //$tallyData = json_decode($tallyData,true);
        $adminInfo = [
            'admin_id' => $userInfo['uid'],
            'admin_name' => $userInfo['nickname'],
            'tally_from' => TallyConstants::TALLY_FROM_WMS
        ];
        try {
            $ret = $this -> TallyService -> addTally($params['t_type'],$params['w_id'],$redisKey,$adminInfo);
            logger() -> debug("理货转架保存数据：",[$ret]);
            // 批量写入店内码操作日志
//            $this -> _writeUniqueCodeLogBatch($params['t_type'],$ret['data']);
//            WriteUniqueCodeLog::batch($params['t_type'],$ret['bill_data']);
            // 请求完成，删除幂等性校验令牌
            delIdempotenceToken($userInfo['uid']);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Throwable $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        } finally {
            RedisLock::unlock(CacheData::getKey(CacheData::TALLY_ADD_LOCK,$userInfo['uid']),$token);
        }

    }

    /**
     * 列表
     * @RequestMapping(path="/tally/list", methods="get,post")
     */
    public function list(RequestInterface $request)
    {
        if ($request -> isMethod('POST')) {
            $params = $request -> all();
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 10;
            $where = $params['where'] ?? [];
            if (isset($where['goods_codes']) && !empty($where['goods_codes'])) {
                $where['goods_codes'] = explode(PHP_EOL,$where['goods_codes']);
            }

            try {
                $result = $this -> TallyService -> getTallyList((int)$page,(int)$limit,$where);
                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$result);
            } catch (\Exception $exception) {
                $ret = $exception -> getMessage();
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
            }
        }

        return $this -> show("/tally/list");
    }

    /**
     * 根据店内码找w_id，sku_id，shelf_code
     * @param $uniqueSkuMap
     * @param $uniqueCode
     */
    private function searchForSkuByUniqueCode($uniqueSkuMap,$uniqueCode)
    {
        $return = [];
        foreach ($uniqueSkuMap as $k => $v) {
            if ($v['unique_code'] == $uniqueCode) {
                $return = $v;
                break;
            }
        }
        return $return;
    }

    /**
     * 查找sku货架库存
     * @param $skuStock
     * @param $wId
     * @param $skuId
     * @param $shelfCode
     * @return int|string
     */
    private function searchShelfStock($skuStock,$wId,$skuId,$shelfCode)
    {
        $result = -1;
        foreach ($skuStock as $k => $v) {
            if ($v['w_id'] == $wId && $v['sku_id'] == $skuId && $v['shelf_code'] == $shelfCode) {
                $result = $k;
                break;
            }
        }
        return $result;
    }

    private function exportTallyBillData($tId,$where){

        // 取出异常数据 理货结果类型：1=正常，2=异常
        $eData = $this -> TallyService -> getBillAll($tId,$where);
        if (empty($eData)) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,'异常数据为空');
        }

        $head = [
            'goods_code' => '商品码',
            'goods_code_type_name' => '类型',
            'old_shelf_code' => '原货架',
            'off_shelf_num' => '下架数量',
            'recommend_shelf_code' => '推荐货架',
            'new_shelf_code' => '目标货架',
            'on_shelf_num' => '上架数量',
            'result' => '结果',
            't_admin_name' => '理货人',
        ];
        $fileName = '理货结果'.date('Y-m-d-H-i-s');
        $exportData = [];
        logger() ->debug('理货结果导出数据：',[$eData]);
        if (!empty($eData)) {
            foreach ($eData as $k => $v) {
                $goodsCode = $v['goods_code'] ? $v['goods_code'] : '';
                $goodsCodeTypeName = '';
                if (1 == $v['goods_code_type']) {
                    $goodsCodeTypeName = '店内码';
                }
                if (2 == $v['goods_code_type']) {
                    $goodsCodeTypeName = '条码';
                }

                array_push($exportData,[
                    'goods_code' => '`'.$goodsCode,
                    'goods_code_type_name' => $goodsCodeTypeName,
                    'old_shelf_code' => $v['old_shelf_code'] ?: '',
                    'off_shelf_num' => $v['off_shelf_num'] ? ''.$v['off_shelf_num'].'' : "0", // 避免excel自动将0值改为空
                    'recommend_shelf_code' => $v['recommend_shelf_code'] ?: '',
                    'new_shelf_code' => $v['new_shelf_code'] ?: '',
                    'on_shelf_num' => $v['on_shelf_num'] ? ''.$v['on_shelf_num'].'' : "0", // 避免excel自动将0值改为空
                    'result' => $v['result'] ?: '',
                    't_admin_name' => $v['t_admin_name'] ?: "",
                ]);
            }
        }
        logger() ->debug('理货结果导出数据：',[$exportData]);
        return exportToExcel($head,$exportData,$fileName);

    }

    /**
     * 确认转架 - 保存上传的数据
     * @RequestMapping(path="/tally/delImpotenceToken", methods="get,post")
     */
    public function delImpotenceToken(RequestInterface $request)
    {
        // 登录用户信息
        $adminInfo = $this->session->get('userInfo');
        delIdempotenceToken($adminInfo['uid']);
        return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),'');
    }

    /**
     * 幂等性校验
     * @param $token
     */
    public function idempotenceCheck($adminId,$token)
    {
        $redis = redis();
        $key = $adminId.'_wms_'.PublicCode::IDEMPOTENCE_TOKEN_KEY;
        $res = $redis -> get($key);

        var_dump('幂等校验res-from redis：',$res);
        if ($res) {
            if ($token == $res) {
                // 校验通过，即可删除
                $redis -> unlink($key);
                return true;
            }
            return false;
        }
        return false;
    }

    /**
     * 列表
     * @RequestMapping(path="/tally/getTallSummary", methods="get,post")
     */
    public function getTallSummary(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            't_id' => 'required|integer',
        ],[
            't_id.required'=> '请传入盘点id',
        ]);

        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        try {
            $result = $this -> TallyService -> getTallSummary($params['t_id']);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$result);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    /**
     * 前端轮询获取上传校验结果
     * @RequestMapping(path="/tally/getUploadCheckResult", methods="get,post")
     */
    public function getUploadCheckResult(RequestInterface $request)
    {
        $userInfo = $this -> session -> get('userInfo');
        if (!$userInfo['uid']) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, "请登录" );
        }
        // 由前端轮询获取结果
        $ResKey = 'upload_check_result_'.$userInfo['uid'];
        $resultData = redis() -> rPop($ResKey);
        if ($resultData) {
            $resultData = json_decode($resultData,true);
            if ($resultData["result"]) {
                $returnData = [
                    'result' => "1",
                    'msg' => $resultData["msg"],
                    'data' => $resultData["data"] ? $resultData["data"] : [],
                    'eData' => $resultData["eData"] ? $resultData["eData"] : [],
                ];
                $stId = $resultData["data"]['st_id'];
                /********************************************记录日志****************************************************/
                // 当前登录账户数据
                $userInfo = $this->session->get('userInfo');
                // 记录日志数据
                $logData = [
                    'snow_id' => $this->request->getAttribute('snow_id'),
                    'op_id' => $stId,
                    'op_type' => '创建新盘点任务', // 操作类型
                    'op_content' => '盘点ID：'.$stId, // 操作内容
                    'op_time' => date('Y-m-d H:i:s'), // 操作时间
                    'model_name' => 'st', // 操作模块
                    'st_status' => 1,  // 盘点状态: 0=草稿,1=即将开始，2=正在预盘，3=正在复盘，4=复盘完成，5=已完成，6=已作废
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                    'remark' => "创建盘点任务，盘点ID：".$stId
                ];
                wlog((string)$logData['snow_id'], $logData);
                /********************************************记录日志****************************************************/

                return $this -> returnApi(ResponseCode::SUCCESS,$resultData["msg"],$returnData);
            } else {
                $returnData = [
                    'result' => "0",
                    'msg' => $resultData["msg"],
                    'eData' => $resultData["eData"] ? $resultData["eData"] : [],
                ];
                return $this -> returnApi(ResponseCode::SERVER_ERROR,$resultData["msg"],$returnData);
            }
        }

        return $this -> returnApi(0,"无结果",'');
    }

    /**
     * 理货导出
     * @RequestMapping(path="/tally/export", methods="get,post")
     */
    public function export()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $hasWhere = false;
            foreach ($params['search'] as $key => $value) {
                if ($key == 'goods_codes') {
                    if (implode('', $value)) {
                        $hasWhere = true;
                        break;
                    }
                } else {
                    if ($value) {
                        $hasWhere = true;
                        break;
                    }
                }
            }
            if (!$hasWhere) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '请输入筛选条件');
            }
            if ($params['search']['date_range']) {
                $params['search']['start_time'] = explode(' ~ ', $params['search']['date_range'])[0];
                $params['search']['end_time'] = explode(' ~ ', $params['search']['date_range'])[1];
            }

            $exportHeader = [
                't_id' => '批次号',
                'w_name' => '仓库名称',
                'goods_code' => '商品码',
                'goods_code_type_text' => '类型',
                'old_shelf_code' => '原货架',
                'off_shelf_num' => '下架数量',
                'recommend_shelf_code' => '推荐货架',
                'new_shelf_code' => '目标货架',
                'on_shelf_num' => '上架数量',
                'result_type_text' => '结果',
                't_admin_name' => '理货人',
                'created_at' => '理货时间'
            ];
            $header = [
                'fields' => array_keys($exportHeader),
                'names' => array_values($exportHeader)
            ];
            $userInfo = $this->session->get('userInfo');

            $data = [];
            $data['serial_no'] = $this->SerialNoService->generate(SerialType::WO_TALLY);
            $data['name'] = '理货导出-' . date('YmdHis');
            $data['where'] = json_encode($params['search'], JSON_UNESCAPED_UNICODE);
            $data['status'] = 1;
            $data['admin_id'] = $userInfo['uid'];
            $data['admin_name'] = $userInfo['nickname'];
            $data['header'] = json_encode($header, JSON_UNESCAPED_UNICODE);
            $data['type'] = 2;
            $data['service'] = 'tally/exportData';
            $data['is_limit'] = 1;
            $data['system'] = 'wms';
            try {
                $this->TaskService->addTask($data);
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }
            return $this->returnApi(ResponseCode::SUCCESS, '任务已创建，请到下载任务列表查看');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 上传理货数据页面
     * @RequestMapping(path="/tally/planCreate", methods="get,post")
     */
    public function planCreate(RequestInterface $request)
    {
        return $this -> show("/tally/planCreate");
    }

    /**
     * 保存计划理货任务列表
     * @RequestMapping(path="/tally/saveTaskList", methods="get,post")
     */
    public function saveTaskList(RequestInterface $request)
    {

        $params = $request -> all();
        logger() ->debug('保存计划理货任务列表-入参',[$params]);
        $validator = validate()->make($params, [
            'w_id' => 'required|integer',
            'sku_id_num' => 'required|gt:0',
            'unique_limit_max' =>'required|gt:0',
            'shelf_line_num' =>'gt:0',
            'stock_age' =>'gt:0',
        ],[
            'w_id.required' => '请选择仓库',
            'w_id.gt' => '仓库ID必须大于0',
            'sku_id_num.required' => '条码库存数量必填',
            'sku_id_num.gt' => '条码库存数量必须大于0',
            'unique_limit_max.required' => '拆任务单店内码上限数量必填',
            'unique_limit_max.gt' => '拆任务单店内码上限数量必须大于0',
            'shelf_line_num.gt' => '货架排数量必须大于等于0',
            'stock_age.gt' => '库龄必须大于等于0',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $params['cate_ids'] = isset($params['cate_ids']) && !empty($params['cate_ids']) ? explode(',',$params['cate_ids']) : [];
        $params['brand_ids'] = isset($params['brand_ids']) && !empty($params['brand_ids']) ? explode(',',$params['brand_ids']) : [];
        $params['sku_ids'] = isset($params['sku_ids']) && !empty($params['sku_ids']) ? explode(PHP_EOL,trim($params['sku_ids'])) : [];

        $userInfo = $this -> session -> get('userInfo');
        $params['admin_id'] = $userInfo['uid'];
        $params['admin_name'] = $userInfo['nickname'];

        try {
            $list = $this -> TallyService -> getPlanTallyUniqueList($params);
            logger() -> debug("服务端取出任务数量：",[count($list)]);
            // 将返回数据进行处理：按照拆任务单店内码上限数量分割，并存入redis
            $result = PlanTally::splitUniqueList($list,$params);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$result);
        } catch (\Throwable $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    /**
     * 获取计划理货任务列表
     * @RequestMapping(path="/tally/getTaskList", methods="get,post")
     */
    public function getTaskList(RequestInterface $request)
    {
        $params = $request -> all();
        logger() ->debug('计划理货任务列表预览-入参',[$params]);
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 10;

        $userInfo = $this -> session -> get('userInfo');
        $params['admin_id'] = $userInfo['uid'];
        $params['admin_name'] = $userInfo['nickname'];
        try {
            $taskList = PlanTally::getTaskListPage($params, $page, $limit);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$taskList);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    /**
     * 删除计划理货任务
     * @RequestMapping(path="/tally/delTask", methods="get,post")
     */
    public function delTask(RequestInterface $request)
    {
        $params = $request -> all();
        logger() ->debug('删除计划理货任务-入参',[$params]);
        validate()->make($params, [
            'id' => 'required|integer',
            'unique_ids' => 'required|string',
        ],[
            'id.gt' => '任务ID必须大于0',
            'unique_ids.string' => '删除任务对应的店内码必传',
        ])->validate();
        $userInfo = $this -> session -> get('userInfo');
        $params['admin_id'] = $userInfo['uid'];
        $params['admin_name'] = $userInfo['nickname'];
        try {
            $ret = PlanTally::delTask($params);
            logger() -> debug('删除结果',[$ret]);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 保存计划理货数据
     * @RequestMapping(path="/tally/exportPlanTallyBill", methods="get,post")
     */
    public function exportPlanTallyBill(RequestInterface $request)
    {
        $params = $request -> all();
        logger() ->debug('保存计划理货数据-入参',[$params]);
        $userInfo = $this -> session -> get('userInfo');
        $params['admin_id'] = $userInfo['uid'];
        $params['admin_name'] = $userInfo['nickname'];

        try {
            $uniqueList = PlanTally::getUniqueListAll($params);
            if (!$uniqueList) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据');
            }

            // 给$uniqueList中的spu_id，spu_no，barcode，sku_id 添加反引号`
            foreach ($uniqueList as $key => $value) {
                $uniqueList[$key]['spu_id'] = '`'. $value['spu_id'];
                $uniqueList[$key]['spu_no'] = '`'. $value['spu_no'];
                $uniqueList[$key]['barcode'] = '`'. $value['barcode'];
                $uniqueList[$key]['sku_id'] = '`'. $value['sku_id'];
            }
            $header = [
                'task_id' => '任务序号',
                'category_name' => '类目',
                'brand_name' => '品牌',
                'spu_id' => 'SPU',
                'spu_no' => '货号',
                'barcode' => '条码',
                'sku_id' => 'SKU',
                'shelf_code' => '货架号',
                'unique_code' => '店内码',
                'status_text' => '状态',
                'instore_at' => '入库时间'
            ];

            logger() ->debug('任务列表2',[$uniqueList]);
            $url = exportToExcel($header, $uniqueList, '计划理货任务明细');
            return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 保存计划理货数据
     * @RequestMapping(path="/tally/savePlanTally", methods="get,post")
     */
    public function savePlanTally(RequestInterface $request)
    {
        $params = $request -> all();
        validate()->make($params, [
            'w_id' => 'required|integer|gt:0',
        ],[
            'w_id.gt' => '仓库ID必须大于0',
        ])->validate();
        logger() ->debug('保存计划理货数据-入参',[$params]);
        $userInfo = $this -> session -> get('userInfo');
        $params['admin_id'] = $userInfo['uid'];
        $params['admin_name'] = $userInfo['nickname'];
        // 防重复点击
//        $check = PlanTally::passCheck(['expire'=>3,'redis_key'=>PlanTally::passCheckKey(['admin_id'=>$params['admin_id']])]);
//        logger() ->debug('check-->',[$check]);
//        if (!$check) {
//            return $this -> returnApi(ErrorCode::SERVER_ERROR,'操作过于频繁，请稍后再试');
//        }
        $token = RedisLock::lock(CacheData::getKey(CacheData::TALLY_PLAN_ADD_LOCK,$userInfo['uid']),CacheData::TALLY_ADD_LOCK_TIME);
        if($token === false) {
            throw new BusinessException("请求过于频繁，请稍后再试!");
        }
        try {
            $uniqueList = PlanTally::getUniqueListAll($params);
            logger() -> debug('计划理货任务店内码数据',[$uniqueList]);
            if (!$uniqueList) {
                return $this -> returnApi(ErrorCode::SERVER_ERROR,'数据为空或页面已过期，请重试');
            }
            $uniqueListGroup = collect($uniqueList) -> groupBy('task_id');
            // 一次最多提交200个任务，多余的丢弃
            $uniqueListGroup = $uniqueListGroup->take(200);
            $result = [];
            foreach ($uniqueListGroup as $group) {
                $res = $this -> TallyService -> createPlanTally($group->toArray(),[
                    'w_id' => $params['w_id'],
                    'admin_id' => $params['admin_id'],
                    'admin_name' => $params['admin_name'],
                    'tally_from' => TallyConstants::TALLY_FROM_PDA // 因为计划理货实际上传均是在pda，故创建时，来源直接即为pda
                    ]);
                array_push($result, $res);
            }
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),[$uniqueList,$result]);
        } catch (\Throwable $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }  finally {
            RedisLock::unlock(CacheData::getKey(CacheData::TALLY_PLAN_ADD_LOCK,$userInfo['uid']),$token);
        }
    }

    /**
     * 清空计划任务缓存
     * @RequestMapping(path="/tally/clearPlanTaskCache", methods="get,post")
     */
    public function clearPlanTaskCache(RequestInterface $request)
    {
        logger() ->debug('清空计划任务缓存');
        $userInfo = $this -> session -> get('userInfo');
        $params['admin_id'] = $userInfo['uid'];
        try {
            $ret = PlanTally::clearPlanTaskCache($params);
            logger() -> debug('清空计划任务缓存结果',$ret);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }
    /**
     * 清空重复请求缓存
     * @RequestMapping(path="/tally/clearPassCheck", methods="get,post")
     */
    public function clearPassCheck(RequestInterface $request)
    {
        logger() ->debug('clearPassCheck缓存');
        $userInfo = $this -> session -> get('userInfo');
        $params['admin_id'] = $userInfo['uid'];
        try {
            $ret = PlanTally::clearPassCheck(['redis_key'=>PlanTally::passCheckKey(['admin_id'=>$params['admin_id']])]);
            logger() -> debug('clearPassCheck缓存结果',[$ret]);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }


}
