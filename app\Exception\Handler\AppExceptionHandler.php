<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://doc.hyperf.io
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Exception\Handler;

use App\Constants\ErrorCode;
use App\Exception\AuthenticationException;
use Hyperf\Contract\StdoutLoggerInterface;
use Hyperf\ExceptionHandler\ExceptionHandler;
use Hyperf\HttpMessage\Exception\NotFoundHttpException;
use Hyperf\HttpMessage\Stream\SwooleStream;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Validation\ValidationException;
use Psr\Http\Message\ResponseInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\ResponseInterface as Respon2seInterface;
use Throwable;

class AppExceptionHandler extends ExceptionHandler
{
    /**
     * @var StdoutLoggerInterface
     */
    protected $logger;

    /**
     * @Inject
     * @var RequestInterface
     */
    protected $request;

    /**
     * @Inject
     * @var Respon2seInterface
     */
    protected $response;

    public function __construct(StdoutLoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public function handle(Throwable $throwable, ResponseInterface $response)
    {
        $request_type = $this->request->getHeaderLine('X-Requested-With');
        $msg = $throwable->getMessage();
        $code = 500;
        switch (true) {
            case ($throwable instanceof NotFoundHttpException):
                $code = 404;
                break;
            case ($throwable instanceof AuthenticationException):
                $code = 403;
                break;
            case ($throwable instanceof ValidationException):
                $code = $throwable->status;
                $msg = $throwable->validator->errors()->first();
                break;
            case ($throwable instanceof \Exception):
                $code = 500;
                break;
        }
        logger()->error(sprintf('%s[%s][%s] in %s', $throwable->getMessage(), $throwable->getLine(),$throwable->getCode(), $throwable->getFile()));

        if (strtolower('XMLHttpRequest') == strtolower($request_type)) {
            $result = $response->withHeader("Server", "BigOffs")
                ->withStatus($code)
                ->withHeader('content-type','application/json')
                ->withBody(new SwooleStream(json_encode(['code' => $code, 'msg' => $msg], JSON_UNESCAPED_UNICODE)));
        } else {
            $result = $response->withHeader("Server", "BigOffs")->withStatus($code)
                ->withHeader('Content-Type', 'text/html')
                ->withBody(new SwooleStream('<script>alert("' . $msg . '");</script>'));
        }
        logger()->debug('exception',['result'=>$result,'msg'=>$msg,'code'=>$code]);

        return  $result;
    }

    public function isValid(Throwable $throwable): bool
    {
        return true;
    }
}
