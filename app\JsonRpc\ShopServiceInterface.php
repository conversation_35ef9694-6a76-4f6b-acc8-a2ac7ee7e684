<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * Interface ShopServiceInterface
 * @package App\JsonRpc
 */
interface ShopServiceInterface
{
    // 获取所有门店id
    public function getAllShopIds();

    // 门店树-按区域分
    public function shopTree();

    // 列表
    public function shopList(int $page, int $pageLimit, array $search = []);

    // 查单个
    public function getShop(array $where, array $filed = ['*']);

    // 查多个
    public function getShops(array $where = [], array $field = ['id', 'name']);

    // 新增
    public function create(array $data);

    // 编辑
    public function update(int $id, array $data);

    // 门店仓库
    public function getShopWarehouse(array $where, array $field = ['*']);
    /**
     * 根据id获取店铺信息
     * @param array $ids
     * @param string $columns
     * @return \Hyperf\Database\Model\Builder[]|\Hyperf\Database\Model\Collection
     */
    public function getDataByIds(array $ids, $columns = '*');

    /**
     * 获取店铺id为键的map
     * @param array|string[] $fields
     * @return \Hyperf\Database\Model\Builder[]|\Hyperf\Database\Model\Collection
     */
    public function getKvMapById(array $ids,array $fields = ['id', 'name']);

    /**
     * 串码销售订单库存调整
     * @param array $shopIds 店铺列表 [
     *      [
     *          shop_id => 店铺
     *          sku_id => sku
     *          num => 数量
     *      ]
     * ]
     */
    public function stockAdjustException(array $dataList);

    /**
     * 获取门店和仓库对应关系
     * @param array $where
     * @param array|string[] $field
     * @return mixed
     */
    public function getShopWarehouseMap(array $where, array $field = ['*']);
}
