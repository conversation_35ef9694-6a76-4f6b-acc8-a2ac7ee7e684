<?php
declare(strict_types=1);

namespace App\Controller;

use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\ProduceAreaServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;

/**
 * @Controller()
 */
class ProduceAreaController extends AbstractController
{
    /**
     * @Inject()
     * @var ValidatorFactoryInterface
     */
    private $validator;
    /**
     * @Inject()
     * @var WarehouseServiceInterface
     */
    private $WarehouseService;
    /**
     * @Inject()
     * @var ProduceAreaServiceInterface
     */
    private $ProduceAreaService;
    /**
     * @Inject()
     * @var AdminServiceInterface
     */
    private $AdminService;

    /**
     * 列表
     * @RequestMapping(path="/produceArea/list", methods="get,post")
     */
    public function list()
    {
        // 用户
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->AdminService->organizeWareHouseData($userInfo['uid']);
        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        // 来源类型
        $source_type = PublicCode::produce_area_source_type;
        // 查询方式
        $sign_type = PublicCode::produce_area_sign_type;
        // 状态
        $status = PublicCode::produce_area_status;

        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            $search['w_ids'] = $userWIds;
            $export = $params['export'] ?? 0;// 0列表 1导出

            if ($export == 1) {
                $listData = [];
                $sPage = 1;
                $sLimit = 300;
                while (true) {
                    $data = $this->ProduceAreaService->list(0, $sPage, $sLimit, $search);
                    if (!$data['data']) {
                        break;
                    }
                    $listData = array_merge($listData, $data['data']);
                    unset($data);
                    $sPage++;
                }
                $list['data'] = $listData;
            } else {
                $list = $this->ProduceAreaService->list($export, (int)$page, (int)$pageLimit, $search);
            }
            if ($list['data']) {
                foreach ($list['data'] as &$item) {
                    $item['w_name'] = $warehouse[$item['w_id']];
                    $item['source_type'] = $source_type[$item['source_type']] ?? '';
                    $item['content'] = $item['sign_type'] == 1 ? $item['unique_code'] : $item['barcode'];
                    $item['status'] = $status[$item['status']];
                }
            }

            if ($export) {
                if (!$list['data']) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
                }
                $url = exportToExcel($this->exportListHeader(), $list['data'], '生产区货品');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
        }

        return $this->show('produce_area/list', [
            'warehouse_list' => $warehouse,
            'source_type' => $source_type,
            'sign_type' => $sign_type,
            'status' => $status
        ]);
    }

    /**
     * 汇总列表
     * @RequestMapping(path="/produceArea/summaryList", methods="get,post")
     */
    public function summaryList(){
        $params = $this->request->all();
        $page = $params['page'] ?? 1;
        $pageLimit = $params['limit'] ?? $this->pageLimit();
        $search = $params['search'] ?? [];
        if (!isset($search['status']) || empty($search['status']) ){
            $search['status'] = 0;
        }
        $export = $params['export'] ?? 0;// 0列表 1导出
        if (!isset($search['status']) || $search['status'] === "" || intval($search['status']) !== 0){
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', [], ['count' => 0, 'limit' => $pageLimit]);
        }
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->AdminService->organizeWareHouseData($userInfo['uid']);
        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        // 来源类型
        $source_type = PublicCode::produce_area_source_type;
        // 状态
        $status = PublicCode::produce_area_status;
        $search['w_ids'] = $userWIds;


        if ($export == 1) {
            $listData = [];
            $sPage = 1;
            $sLimit = 300;
            while (true) {
                $data = $this->ProduceAreaService->summaryList(0, $sPage, $sLimit, $search);
                if (!$data['data']) {
                    break;
                }
                $listData = array_merge($listData, $data['data']);
                unset($data);
                $sPage++;
            }
            $list['data'] = $listData;
        } else {
            $list = $this->ProduceAreaService->summaryList($export, (int)$page, (int)$pageLimit, $search);
        }
        if ($list['data']) {
            foreach ($list['data'] as &$item) {
                $item['w_name'] = $warehouse[$item['w_id']];
                $item['source_type'] = $source_type[$item['source_type']] ?? '';
            }
        }

        if ($export) {
            if (!$list['data']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
            }
            $url = exportToExcel($this->exportHeader(), $list['data'], '生产区货品汇总');
            return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
        }

        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
    }

    private function exportListHeader()
    {
        return [
            'id' => 'ID',
            'w_name' => '仓库',
            'source_type' => '来源类型',
            'source_order_no' => '来源单号',
            'content' => '店内码/条形码',
            'produce_num' => '已生产数量',
            'reback_num' => '已回库数量',
            'surplus_num' => '剩余数量',
            'status' => '状态',
            'created_at' => '创建时间'
        ];
    }

    private function exportHeader()
    {
        return [
            'w_name' => '仓库',
            'source_type' => '生产类型',
            'source_order_no' => '来源单号',
            'sum_num' => '数量',
            'sum_produce_num' => '已生产数量',
            'sum_pack_num' => '已装箱数量',
            'sum_reback_num' => '已回库数量',
            'sum_surplus_num' => '剩余数量',
            'finish_num' => '已完成数量'
        ];
    }

}