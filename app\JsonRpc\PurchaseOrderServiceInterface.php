<?php
declare(strict_types=1);

namespace App\JsonRpc;

interface PurchaseOrderServiceInterface
{
    /**
     * 查单个
     * @param array $where
     * @param array|string[] $filed
     * @return mixed
     */
    public function getOrder(array $where, array $filed = ['*']);

    /**
     * 查多个
     * @param array $where
     * @param array|string[] $filed
     * @return array
     */
    public function getOrders(array $where = [], array $filed = ['*']);

    /**
     * 获取采购单采购明细
     * @param array $where
     * @return array
     */
    public function getOrderDetail(array $where);

    public function getOrderArrivalShopAt(array $where);
}