<?php
declare(strict_types=1);

namespace App\JsonRpc;

interface ProduceAreaServiceInterface
{
    /**
     * 列表
     * @param int $export
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return mixed
     */
    public function list(int $export, int $page, int $pageLimit, array $search);

    /**
     * 查多条
     * @param array $where
     * @param array|string[] $filed
     * @return array
     */
    public function getDetailS(array $where = [], array $filed = ['*']);

    /**
     * 操作生产区条码 - 匹配数据
     * @param int $handleType 1、回库 2、生产
     * @param array $handleData
     * @param array $produceData
     * @return array
     */
    public function getMatchBarcodeData(int $handleType, array $handleData, array $produceData);

    /**
     * 进入生产区
     * @param array $data
     * @return bool
     */
    public function create(array $data);

    /**
     * 将生厂区的商品发货
     * @param array $uniqueCodeInfo
     * @param array $barcodeInfo
     */
    public function produceAreaOut(array $uniqueCodeInfo, array $barcodeInfo);

    /**
     * 汇总列表
     * @param int $export
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     */
    public function summaryList(int $export, int $page, int $pageLimit, array $search);
}