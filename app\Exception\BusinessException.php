<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Exception;

use App\Constants\ErrorCode;
use Hyperf\Server\Exception\ServerException;
use Throwable;

class BusinessException extends ServerException
{
    public function __construct( string $message = null,int $code = ErrorCode::REQUEST_ERROR, Throwable $previous = null)
    {
        if (is_null($message)) {
            $message = ErrorCode::getMessage($code);
        }

        parent::__construct($message, $code, $previous);
    }


    public static function createByException(Throwable $throwable,$msg = '操作失败',$code = ErrorCode::SERVER_ERROR){
        if($throwable instanceof BusinessException){
            return $throwable;
        }
        return new static($msg,$code,$throwable);
    }
}
