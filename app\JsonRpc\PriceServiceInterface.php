<?php

namespace App\JsonRpc;

interface PriceServiceInterface
{
    public function addPrice(array $data);

    public function editPrice(array $where, array $data, array $detail_where, array $detail_data);

    public function getPriceList(array $where, int $perPage, int $currentPage);

    public function getPriceDetail(array $where);

    /**
     * 连表查询商品的调价信息
     * @param array $where['goods_ids' => array(goods_id)]
     * @param int $perPage 每页条数
     * @param int $currentPage 当前页码
     */
    public function getGoodsPrice(array $where, int $perPage, int $currentPage);

    public function getPriceById(int $id);

    public function getGoodsPriceRecord(array $where, int $perPage, int $currentPage);

    public function searchSpuCostPrice(array $skuids);
    
    /**
     * 根据sku获取成本价
     * @param array $skuIds
     */
    public function searchCostPrice(array $skuIds);

    /**
     * 连表查询店内码的调价信息
     * @param array $codeList
     */
    public function uniqueCodePrice(array $codeList);

    /**
     * 连表分页查询店内码的调价信息
     * @param array $codeList
     * @param int $perPage
     * @param int $currentPage
     */
    public function getUniqueCodeList(array $codeList, int $perPage = 10, int $currentPage = 1);

    /**
     * 连表查询商品的调价记录
     */
    public function getUniqueCodePriceRecord(string $unique_code = '', int $perPage = 10, int $currentPage = 1);

    /**
     * 获取店内码调价单详情
     * @param array $where
     * @return array
     */
    public function getPriceUniqueCodeDetail(array $where);


    /**
     * 检测是否存在负毛利的商品记录
     * @param array $where
     */
    public function existsGoodsNegativeGrossProfit(int $price_id);

    /**
     * 检测是否存在负毛利的商品记录
     * @param array $where
     */
    public function existsUniqueNegativeGrossProfit(int $price_id);
    /**
     * 获取门店相关的调价单据
     * @param array $params
     */
    public function getShopDaylyPriceList(array $params);

    /**
     * 获取门店相关的调价单据 残次调价
     * @param array $params
     */
    public function getShopImperfectPriceList(array $params);

    /**
     * 获取门店相关的活动变价 限时特价
     * @param array $params
     */
    public function getShopPromotionPriceList(array $params);

}