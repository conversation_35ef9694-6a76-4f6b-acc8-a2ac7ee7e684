<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * 装箱
 */
Interface  GoodsPackingServiceInterface
{
    /**
     * 新建装箱
     * @param array $data
     * @return mixed
     */
    public function addGoodsPacking(array $data);

    /**
     * 装箱列表
     * @param int $page
     * @param int $limit
     * @param array $where
     * @param array $field
     * @return array
     */
    public function getGoodsPackingList($authWIds,int $page = 1, int $limit = 10, array $where = [], array $field = []);

    /**
     * 获取装箱详情
     * @param $id
     */
    public function getGoodsPackingOne($id);

    /**
     * 获取装箱清单列表
     * @param $pk_id
     * @param array $where
     */
    public function getGoodsPackingBillList($pk_id,$export,$page = 1,$limit=10,$where=[]);

    /**
     * 箱号是否已存在
     * @param $boxNos
     */
    public function checkBoxNos(array $boxNos);

    /**
     * 校验店内码是否已打包
     * @param array $uniqueCodes
     */
    public function checkIfUniquePacked(array $uniqueCodes);

    /**
     * 发货获取箱号相关信息
     * @param $boxNo
     */
    public function checkTaskBox($boxNo);


    /**
     * 装箱 取消
     * @param int $id
     * @param array $data
     */
    public function cancelGoodsPacking(int $id, array $data);

    /**
     * 校验店内码在指定任务单是否已打包
     * @param array $uniqueCodes
     */
    public function checkIfUniquePackedByTask(int $taskType, string $sourceNo, array $uniqueCodes);


    /**
     * 根据物流id获取发货的店内码
     * @param int $logistics_id
     * @return array
     */
    public function getSendUqByLogisticsId(int $logistics_id);

    /**
     * 称重
     * @param $id
     * @param $weight
     * @param $adminInfo
     */
    public function weighing($id,$weight,$adminInfo);

    /**
     * 称重
     * @param $id
     */
    public function weightLog($id);

    /**
     * 最近一次称重
     * @param $id
     */
    public function getLatestWeightLog($id);

    /**
     * 批量获取装箱详情
     * @param array $ids
     */
    public function getPackingBillsByPkIds(array $ids);

    /**
     * 获取装箱列表
     * @param int $export
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return array
     */
    public function packList(int $export, int $page, int $pageLimit, array $search);

    /**
     * 箱号质检
     * @param int $sourceNo
     * @param array $boxNos
     * @return void
     */
    public function qualityBox($sourceNo,$boxNos);

    /**
     * 签收货品列表
     * @param int $export
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return array
     */
    public function signList(int $export, int $page, int $pageLimit, array $search);

    /**
     * 箱号发货记录作废
     * @param $boxId
     * @return bool
     */
    public function cancelBox($boxIds = '');

    /**
     * 根据货品编码校验货品数据
     * @param array $checkParams
     * @return mixed
     */
    public function checkCode(array $checkParams);

    /**
     * 根据货品编码校验货品数据
     * @param array $checkParams
     * @return mixed
     */
    public function signCode(array $checkParams);

    /**
     * 获取签收记录列表
     * @param int $export
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return array
     */
    public function signLogList(int $export, int $page, int $pageLimit, array $search);

    /**
     * 根据店内码获取店内码和条码的映射关系
     * @param $uniqueCodes
     */
    public function getUniqueBarCodeMap($uniqueCodes);

    /**
     * 获取单号下已装箱货品数量
     * @param $taskType
     * @param $serialNo
     */
    public function getPackedGoodsTotalBySerialNo($taskType,$serialNo);

    /**
     * 为店内码装箱的数据追加epc
     * @param $taskType
     * @param $sourceNo
     */
    public function syncEpcCodeForUniqueCodeBox($taskType,$sourceNo);

    /**
     * 校验扫描的箱号或铅封号
     * @param array $nos
     */
    public function checkBoxNoOrLeadSealNo(array $nos);

    /**
     * 校验扫描的箱号是否在指定来源单号下
     * @param array $boxNos 箱号
     * @param string $sourceNo 来源单号
     * @return array[]
     */
    public function checkBoxNoInSourceNo(array $boxNos,string $sourceNo);

    /**
     * 获取铅封号的绑定信息
     * @param array $leadSealNos 铅封号
     */
    public function getLeadSealNosBindInfo(array $leadSealNos);

    /**
     * 保存称重与绑铅封信息
     * @param string $boxNo
     * @param string $leadSealNo
     * @param string $sourceNo
     * @param string $weight
     * @param array $adminInfo
     */
    public function saveWeightAndLeadSealNo(string $boxNo,string $leadSealNo,string $sourceNo,string $weight,array $adminInfo);

    /**
     * 装箱打印次数+1
     * @param $boxNos
     */
    public function incrPrintTimes($boxNos);

    /**
     * 根据验证店内码是否可以签收
     * @param $wId int 仓库id
     * @param $uniqueCode string 店内码
     * @return \Hyperf\Database\Model\Builder|\Hyperf\Database\Query\Builder|\Hyperf\Utils\Collection|mixed
     */
    public function checkCodeByWarehouse($wId,$uniqueCode);

    /**
     * 店内码批量签收
     * @param array $params[
     *   'admin_id'=>$checkParams['admin_id'],
     *   'admin_name'=>$checkParams['admin_name'],
     *   'code_list' => [
     *     serial_no => 调拨单号,
     *     unique_code => 店内码
     *   ]
     * ]
     * @return mixed
     */
    public function batchSign(array $params);

    /**
     * 根据验证店内码是否可以签收
     * @param $wId int 仓库id
     * @param $uniqueCode string 店内码
     * @return \Hyperf\Database\Model\Builder|\Hyperf\Database\Query\Builder|\Hyperf\Utils\Collection|mixed
     */
    public function batchCheckCodeByWarehouse($wId,$uniqueCode);

    /**
     * 根据来源单号获取指定调拨单下的装箱人列表
     * @param string $sourceNo
     */
    public function getPkAdminList(string $sourceNo);

    /**
     * 获取箱码打印相关信息
     * @param string $sourceNo
     * @param array $pkIds
     */
    public function getBoxNoPrintInfo(string $sourceNo, array $pkIds);

    /**
     * pda获取箱码打印相关信息
     * @param string $boxNo
     */
    public function getBackBoxNoPrintInfo(array $boxNo);


    /**
     * 调拨错下架信息保存,追溯拣货人 - 240201-yj
     * @param array $params [
    'source_code' => $sourceNo,
    'not_out_store_goods' => $notOutStoreGoods,
    'wrong_off_goods' => $wrongOffGoods,
    'admin_id'=> $extend['admin_id'],
    'admin_name' => $extend['admin_name'],
    ]
     * @return void
     */
    public function exceptionGoodsAdd(array $params);

    /**
     * 漏装箱列表
     * @param array $params
     * @param int $page
     * @param int $size
     * @param int $export
     */
    public function notBoxList(array $params, int $page, int $size, $export=0);

    /**
     * 未见实物标记
     * @param array $params
     */
    public function notSeenMark(array $params);

    /**
     * 店内码批量签收
     * @param array $params[
     *   'admin_id'=>$checkParams['admin_id'],
     *   'admin_name'=>$checkParams['admin_name'],
     *   'cache_key' => 缓存key
     * ]
     * @return mixed
     */
    public function batchSignByCacheKey(array $params);

    /**
     * 获取箱号列表
     * @param $wId
     * @param $boxNo
     * @return \Hyperf\Database\Model\Builder[]|\Hyperf\Database\Model\Collection|\Hyperf\Database\Query\Builder[]|\Hyperf\Utils\Collection
     */
    public function getBoxList($wId, $boxNo);

    /**
     * 获取登记箱号信息
     * @param $boxNo
     * @return array
     */
    public function checkRegBoxNo($wId, $boxNo);

    /**
     * 调拨箱号登记
     * @param array $params[
     *   'cache_key'=> 缓存key,
     *   'remark'=> 备注,
     * ]
     * @return mixed
     */
    public function batchRegBoxNo(array $params);

    /**
     * 调拨差异单列表
     * @param int $page
     * @param int $limit
     * @param array $where [
     * ]
     * @param array|string[] $field
     * @return array
     */
    public function regList(int $page = 1, int $limit = 10, array $where = [], array $field = ['*']);

    /**
     * 获取登记人列表
     */
    public function getRegAdminList();
}