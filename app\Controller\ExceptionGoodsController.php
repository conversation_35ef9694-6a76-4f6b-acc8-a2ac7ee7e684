<?php
declare(strict_types=1);

namespace App\Controller;

use App\Constants\ErrorCode;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\ExceptionGoodsServiceInterface;
use App\JsonRpc\ShelfGoodsCodeMapServiceInterface;
use App\JsonRpc\ShelfServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use Hyperf\Utils\Collection;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;

/**
 * @Controller()
 */
class ExceptionGoodsController extends AbstractController
{

    /**
     * @Inject()
     * @var ExceptionGoodsServiceInterface
     */
    private $exceptionGoodsService;

    /**
     * @Inject()
     * @var ShelfGoodsCodeMapServiceInterface
     */
    private $shelfGoodsCodeMapService;


    /**
     * 添加
     * @RequestMapping(path="/exceptionGoods/add", methods="get,post")
     */
    public function add()
    {
        $userInfo = $this->session->get('userInfo');

        if ($this->isAjax()) {
            logger()->info('添加异常商品', [$this->request->all()]);
            $data = $this->request->all();
            $validator = validate()->make($data, [
                'w_id' => 'required|numeric|gt:0',
                'exception_type' => 'required|numeric|in:1,2,3,4',
            ],[
                'w_id.required' => '仓库ID必填',
                'w_id.numeric' => '仓库ID必须是数字',
                'w_id.gt' => '仓库ID必须为大于0的正整数',
                'exception_type.required' => '异常类型必填',
                'exception_type.numeric' => '异常类型必须是数字',
                'exception_type.gt' => '异常类型错误',
            ]);
            if ($validator->fails()) {
                return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
            }
            $data['admin_id'] = $userInfo['uid'];
            $data['admin_name'] = $userInfo['nickname'];
            $goodsList = $data['goods_list'];
            if (!$goodsList) {
                return $this->returnApi( ErrorCode::REQUEST_ERROR, '商品列表不能为空' );
            }
            try {
                logger()->debug('添加异常商品-数据', $data);
                $result = $this -> exceptionGoodsService -> add($data);

                // 记录日志
                // 当前登录账户数据
                $userInfo = $this->session->get('userInfo');
                foreach ($result['ids'] as $id) {
                    // 记录日志数据
                    $logData = [
                        'snow_id' => $this->request->getAttribute('snow_id'),
                        'op_id' => $id,
                        'op_type' => '创建异常货品', // 操作类型
                        'op_content' => 'ID：'.$id, // 操作内容
                        'op_time' => date('Y-m-d H:i:s'), // 操作时间
                        'model_name' => 'exception_goods', // 操作模块
                        'admin_id' => $userInfo['uid'],
                        'admin_name' => $userInfo['nickname'],
                        'remark' => "创建异常货品，ID：".$id
                    ];
                    wlog((string)$logData['snow_id'], $logData);
                }
                return $this->returnApi(ResponseCode::SUCCESS, '创建成功');
            } catch (\Throwable $exception) {
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
            }
        }
        logger()->info('添加异常商品-渲染页面', $this->request->all());
        return $this->show('exceptionGoods/add');
    }
    /**
     * 校验店内码
     * @RequestMapping(path="/exceptionGoods/checkUniqueCode", methods="get,post")
     */
    public function checkUniqueCode()
    {
        $userInfo = $this->session->get('userInfo');

        if ($this->isAjax()) {
            logger()->info('校验店内码', [$this->request->all()]);
            $data = $this->request->all();
            $validator = validate()->make($data, [
                'unique_code' => 'required',
            ],[
                'unique_code.required' => '店内码必填',
            ]);
            if ($validator->fails()) {
                return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
            }
            logger()->debug('校验店内码-数据', $data);
            $result = $this -> shelfGoodsCodeMapService -> getUniqueCode(['unique_code'=>$data['unique_code']]);
            if (!$result) {
                return $this->returnApi( ErrorCode::REQUEST_ERROR, '店内码不存在' );
            }
            return $this->returnApi(ResponseCode::SUCCESS, '校验通过');
        }
        logger()->info('添加异常商品-渲染页面', $this->request->all());
        return $this->show('exceptionGoods/add');
    }

    /**
     * 列表
     * @RequestMapping(path="/exceptionGoods/list", methods="get,post")
     */
    public function list()
    {

        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 10;
            if (isset($params['w_id']) && !empty($params['w_id'])) {
                $params['w_id'] = explode(',',$params['w_id']);
            }
            if (isset($params['brand_id']) && !empty($params['brand_id'])) {
                $params['brand_id'] = explode(',',$params['brand_id']);
            }
            if (isset($params['goods_code']) && !empty($params['goods_code'])) {
                $params['goods_code'] = explode(PHP_EOL,$params['goods_code']);
            }
            if (isset($params['status']) && !empty($params['status'])) {
                $params['status'] = explode(',',$params['status']);
            }
            if (isset($params['admin_id']) && !empty($params['admin_id'])) {
                $params['admin_id'] = explode(',',$params['admin_id']);
            }
            if (isset($params['buyer_admin_id']) && !empty($params['buyer_admin_id'])) {
                $params['buyer_admin_id'] = explode(',',$params['buyer_admin_id']);
            }
            if (isset($params['exception_type']) && !empty($params['exception_type'])) {
                $params['exception_type'] = explode(',',$params['exception_type']);
            }
            if (isset($params['destination']) && !empty($params['destination'])) {
                $params['destination'] = explode(',',$params['destination']);
            }
            if (isset($params['id']) && !empty($params['id'])) {
                $params['id'] = explode(PHP_EOL,$params['id']);
            }
            if (isset($params['sup_id']) && !empty($params['sup_id'])) {
                $params['sup_id'] = explode(',',$params['sup_id']);
            }
            logger()->debug('列表-数据', $params);
            $result = $this -> exceptionGoodsService -> list($params,$page,$limit);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功',$result);
        }

        return $this->show('exceptionGoods/list', [

        ]);
    }

    /**
     * 列表导出
     * @RequestMapping(path="/exceptionGoods/export", methods="get,post")
     */
    public function export()
    {
        $params = $this->request->all();
        $url = $this -> exportData($params);
        return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),['url' => $url]);
    }

    /**
     * 详情
     * @RequestMapping(path="/exceptionGoods/detail", methods="get,post")
     */
    public function detail()
    {
        $data = $this->request->all();
        logger()->info('详情-数据', $data);
        $validator = validate()->make($data, [
            'id' => 'required',
        ],[
            'id.required' => 'ID必填',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $result = $this -> exceptionGoodsService -> detail($data['id']);
        logger()->debug('详情-数据-result', $result);
        return $this->show('exceptionGoods/detail', [
            'detail'=>$result
        ]);
    }
    /**
     * 买手处理
     * @RequestMapping(path="/exceptionGoods/buyer", methods="get,post")
     */
    public function buyer()
    {
        $data = $this->request->all();
        logger()->info('买手处理-数据', $data);
        $validator = validate()->make($data, [
            'id' => 'required',
        ],[
            'id.required' => 'ID必填',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $result = $this -> exceptionGoodsService -> detail($data['id'],['flag'=>'buyer']);
        logger()->debug('买手处理-数据-result', $result);
        return $this->show('exceptionGoods/buyer', [
            'detail'=>$result
        ]);
    }

    /**
     * 异常货品更新
     * @RequestMapping(path="/exceptionGoods/updateByBuyer", methods="get,post")
     */
    public function updateByBuyer()
    {
        logger()->info('添加异常商品', [$this->request->all()]);
        $data = $this->request->all();
        $validator = validate()->make($data, [
            'id' => 'required',
        ],[
            'id.required' => 'ID必填',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $userInfo = $this->session->get('userInfo');
        $data['admin_id'] = $userInfo['uid'];
        $data['admin_name'] = $userInfo['nickname'];
        try {
            logger()->debug('异常货品更新-数据', $data);
            $this -> exceptionGoodsService -> updateByBuyer($data['id'],$data);

            // 记录日志数据
            $logData = [
                'snow_id' => $this->request->getAttribute('snow_id'),
                'op_id' => $data['id'],
                'op_type' => '买手处理', // 操作类型
                'op_content' => 'ID：'.$data['id'], // 操作内容
                'op_time' => date('Y-m-d H:i:s'), // 操作时间
                'model_name' => 'exception_goods', // 操作模块
                'admin_id' => $userInfo['uid'],
                'admin_name' => $userInfo['nickname'],
                'remark' => "买手处理，ID：".$data['id'],
            ];
            wlog((string)$logData['snow_id'], $logData);
            return $this->returnApi(ResponseCode::SUCCESS, '创建成功');
        } catch (\Throwable $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }
    /**
     * 微残驳回
     * @RequestMapping(path="/exceptionGoods/updateByImperfect", methods="get,post")
     */
    public function updateByImperfect() {
        logger()->info('微残驳回', [$this->request->all()]);
        $data = $this->request->all();
        $validator = validate()->make($data, [
            'id' => 'required',
        ],[
            'id.required' => 'ID必填',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $userInfo = $this->session->get('userInfo');
        try {
            logger()->debug('微残驳回-数据', $data);
            $this->exceptionGoodsService->updateByImperfect($data['id']);

            // 记录日志数据
            $logData = [
                'snow_id' => $this->request->getAttribute('snow_id'),
                'op_id' => $data['id'],
                'op_type' => '微残驳回', 
                'op_content' => 'ID：'.$data['id'],
                'op_time' => date('Y-m-d H:i:s'),
                'model_name' => 'exception_goods',
                'admin_id' => $userInfo['uid'],
                'admin_name' => $userInfo['nickname'],
                'remark' => "微残驳回，ID：".$data['id'],
            ];
            wlog((string)$logData['snow_id'], $logData);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功！');
        } catch (\Throwable $exception) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
        }
    }

    /**
     * 校验店内码
     * @RequestMapping(path="/exceptionGoods/checkUniqueInfo", methods="get,post")
     */
    public function checkUniqueInfo() {
        logger()->info('微残驳回', [$this->request->all()]);
        $data = $this->request->all();
        $validator = validate()->make($data, [
            'goods_code' => 'required',
        ],[
            'goods_code.required' => 'ID必填',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        try {
            logger()->debug('微残驳回-数据', $data);
            $result = $this->exceptionGoodsService->checkUniqueInfo(['goods'=>[
                'goods_code' => $data['goods_code'],
                'cate1' => $data['cate1'],
                'brand_id' => $data['brand_id'],
                'w_id' => $data['w_id'],
                'exception_type' => $data['exception_type'],
            ]]);

            return $this->returnApi(ResponseCode::SUCCESS, $result);
        } catch (\Throwable $exception) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
        }
    }
    /**
     * 仓店处理
     * @RequestMapping(path="/exceptionGoods/shop", methods="get,post")
     */
    public function shop()
    {
        $data = $this->request->all();
        logger()->info('仓店处理-数据', $data);
        $validator = validate()->make($data, [
            'id' => 'required',
        ],[
            'id.required' => 'ID必填',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $result = $this -> exceptionGoodsService -> detail($data['id']);
        logger()->debug('仓店处理-数据-result', $result);
        return $this->show('exceptionGoods/shop', [
            'detail'=>$result
        ]);
    }

    /**
     * 仓店货品更新
     * @RequestMapping(path="/exceptionGoods/updateByShop", methods="get,post")
     */
    public function updateByShop()
    {
        logger()->info('仓店货品更新', [$this->request->all()]);
        $data = $this->request->all();
        $validator = validate()->make($data, [
            'id' => 'required',
        ],[
            'id.required' => 'ID必填',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $userInfo = $this->session->get('userInfo');
        $data['admin_id'] = $userInfo['uid'];
        $data['admin_name'] = $userInfo['nickname'];
        try {
            logger()->debug('仓店货品更新-数据', $data);
            $this -> exceptionGoodsService -> updateByShop($data['id'],$data);

            // 记录日志数据
            $logData = [
                'snow_id' => $this->request->getAttribute('snow_id'),
                'op_id' => $data['id'],
                'op_type' => '仓店处理', // 操作类型
                'op_content' => 'ID：'.$data['id'], // 操作内容
                'op_time' => date('Y-m-d H:i:s'), // 操作时间
                'model_name' => 'exception_goods', // 操作模块
                'admin_id' => $userInfo['uid'],
                'admin_name' => $userInfo['nickname'],
                'remark' => "仓店处理，ID：".$data['id'],
            ];
            wlog((string)$logData['snow_id'], $logData);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功!');
        } catch (\Throwable $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 作废
     * @RequestMapping(path="/exceptionGoods/invalid", methods="get,post")
     */
    public function invalid()
    {
        logger()->info('作废异常商品', [$this->request->all()]);
        $data = $this->request->all();
        $validator = validate()->make($data, [
            'id' => 'required|numeric|gt:0',
        ],[
            'id.required' => 'ID必填',
            'id.numeric' => 'ID必须是数字',
            'id.gt' => 'ID必须为大于0的正整数',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $userInfo = $this->session->get('userInfo');
        try {
            logger()->debug('添加异常商品-数据', $data);
            $result = $this -> exceptionGoodsService -> updateStatus($data['id'],['status' => 4]);
            // 记录日志数据
            $logData = [
                'snow_id' => $this->request->getAttribute('snow_id'),
                'op_id' => $data['id'],
                'op_type' => '作废', // 操作类型
                'op_content' => 'ID：'.$data['id'], // 操作内容
                'op_time' => date('Y-m-d H:i:s'), // 操作时间
                'model_name' => 'exception_goods', // 操作模块
                'admin_id' => $userInfo['uid'],
                'admin_name' => $userInfo['nickname'],
                'remark' => "作废，ID：".$data['id']
            ];
            logger() -> debug('作废异常商品-日志', $logData);
            wlog((string)$logData['snow_id'], $logData);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        } catch (\Throwable $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 获取日志列表
     * @param RequestInterface $request
     * @RequestMapping(path="/exceptionGoods/getLog", methods="get,post")
     */
    public function getLog(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'id' => 'required|gt:0',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $params = [
            'op_id' =>  $params['id'],
            'system' => 'wms',
            'model_name' =>'exception_goods',
        ];
        try{
            $ret = getLog($params);
            $retData = [];
            foreach ($ret['data'] as $k => $v) {
                array_push($retData,$v['res_params']);
            }

            $result = ['data' => $retData, 'total' => $ret['total'],];
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$result);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    private function exportData($params){

        // 取出异常数据 理货结果类型：1=正常，2=异常
        $page = 1;
        $size = 5000;
        $exportData = [];
        $fileName = '异常货品导出'.date('Y-m-d-H-i-s');

        do {
            $result = $this->exceptionGoodsService->getAllFieldList($params, $page, $size,false);
            if (empty($result) || empty($result['data'])) {
                break;
            }

            foreach ($result['data'] as $v) {
                $exportData[] = [
                    'id' => $v['id'],
                    'warehouse_name' => $v['warehouse_name'],
                    'exception_type_text' => $v['exception_type_text'],
                    'status_text' => $v['status_text'],
                    'category_name' => $v['category_name'],
                    'brand_name' => $v['brand_name'],
                    'supplier_name' => $v['supplier_name'],
                    'spu_no' => $v['spu_no'],
                    'goods_code' => '`'.$v['goods_code'],
                    'code_type_text' => $v['code_type_text'],
                    'remark1' => $v['remark'] ? explode('&',$v['remark'])[0] : '',
                    'remark2' => $v['remark'] ? explode('&',$v['remark'])[1] : '',
                    'remark3' => $v['remark'] ? explode('&',$v['remark'])[2] : '',
                    'admin_name' => $v['admin_name'],
                    'created_at' => $v['created_at'],
                    'recommend_price' => $v['recommend_price'],
                    'buyer_remark' => $v['buyer_remark'],
                    'buyer_admin_name' => $v['buyer_admin_name'],
                    'buyer_handle_at' => $v['buyer_handle_at'],
                    'destination_text' => $v['destination_text'],
                    'real_price' => $v['real_price'],
                    'shop_remark' => $v['shop_remark'],
                    'shop_admin_name' => $v['shop_admin_name'],
                    'shop_handle_at' => $v['shop_handle_at']
                ];
            }

            $page++;
        } while (count($result['data']) == $size);

        if (empty($exportData)) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, '导出数据为空');
        }

        $head = [
            'id' => 'ID',
            'warehouse_name' => '仓店',
            'exception_type_text' => '异常类型',
            'status_text' => '状态',
            'category_name' => '一级类目',
            'brand_name' => '品牌',
            'supplier_name' => '供应商',
            'spu_no' => '货号',
            'goods_code' => '条码/店内码',
            'code_type_text' => '码类型',
            'remark1' => '备注',
            'remark2' => '备注1',
            'remark3' => '备注2',
            'admin_name' => '创建人',
            'created_at' => '创建时间',
            'recommend_price' => '买手建议售价',
            'buyer_remark' => '买手备注',
            'buyer_admin_name' => '买手处理人',
            'buyer_handle_at' => '买手处理时间',
            'destination_text' => '货品去向',
            'real_price' => '实际售价',
            'shop_remark' => '仓店备注',
            'shop_admin_name' => '仓店处理人',
            'shop_handle_at' => '仓店处理时间'
        ];

        return exportToExcel($head,$exportData,$fileName);

    }

    /**
     * 获取供应商列表
     * @param RequestInterface $request
     * @RequestMapping(path="/exceptionGoods/suppliers", methods="get,post")
     */
    public function suppliers(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'id' => 'required|gt:0',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        try{
            $result = $this -> exceptionGoodsService -> getSuppliers($params['id']);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$result);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }
    /**
     * 获取合同效期内剩余货损金额
     * @param RequestInterface $request
     * @RequestMapping(path="/exceptionGoods/getContractLossAmount", methods="get,post")
     */
    public function getContractLossAmount(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'sup_id' => 'required|gt:0',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        try{
            $result = $this -> exceptionGoodsService -> getContractLossAmount($params['sup_id']);
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$result);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    /**
     * 获取供应商下拉列表
     * @param RequestInterface $request
     * @RequestMapping(path="/exceptionGoods/getSuppliers", methods="get,post")
     */
    public function getSuppliers(RequestInterface $request)
    {
        try{
            $result = $this -> exceptionGoodsService -> getExceptionSuppliers();
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$result);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

}