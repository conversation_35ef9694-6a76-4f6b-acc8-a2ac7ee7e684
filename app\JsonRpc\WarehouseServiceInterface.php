<?php
declare(strict_types=1);

namespace App\JsonRpc;

Interface  WarehouseServiceInterface
{
    /**
     * 查单个
     * @param array $where
     * @param array|string[] $filed
     */
    public function getWarehouse(array $where, array $filed = ['*']);

    /**
     * 查多个
     * @param array $where
     * @param array|string[] $filed
     */
    public function getWarehouses(array $where = [], array $filed = ['*']);

    public function getWarehouseOne(int $id);
}