<?php
declare(strict_types=1);

namespace App\JsonRpc;

interface RebackServiceInterface
{
    /**
     * 创建
     * @param array $reback-回库表
     * @param array $detail-回库明细
     * @param int $sign_type-回库类型
     * @param array $produce_data-更新生产区的数据
     * @return bool
     */
    public function create(array $reback, array $detail, int $sign_type, array $produce_data);

    /**
     * 列表
     * @param int $export
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return mixed
     */
    public function list(int $export, int $page, int $pageLimit, array $search);

    /**
     * 明细列表
     * @param int $export
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return array
     */
    public function detailList(int $export, int $page, int $pageLimit, array $search);

    /**
     * 查单个
     * @param array $where
     * @param array|string[] $filed
     */
    public function getReback(array $where, array $filed = ['*']);

    /**
     * 导出明细
     * @param array $search
     * @return array
     */
    public function detailExport(array $search);
}