<?php

namespace App\Library\Facades;


use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\InStoreServiceInterface;
use App\JsonRpc\OutStoreServiceInterface;
use Hyperf\Guzzle\ClientFactory;

class OutStoreService extends Facade
{
    /**
     * 单据类型
     */
    //采购退返
    const TYPE_PUR_OUT = 21;
    //差异调整单-出
    const TYPE_DIFF_OUT = 22;
    //调拨-出
    const TYPE_ALLOT_OUT = 23;
    //盘点-亏
    const TYPE_CHECK_OUT = 24;
    //退货(订单)
    const TYPE_ORDER_ODI = 25;
    //配送丢失
    const TYPE_DIST_LOSE = 26;
    //配送报损
    const TYPE_DIST_LOSS = 27;

    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor ()
    {
        return OutStoreServiceInterface::class;
    }
}
