<?php

namespace App\JsonRpc;

interface OrderExpressApiLogServiceInterface
{
    /**
     * 获取单个快递对接信息
     * @param array $where
     */
    public function getExpressApiInfo(array $where);

    /**
     * 更新快递面单信息
     * @param array $where
     * @param array $data
     */
    public function updateExpressApiInfo(array $where, array $data);

    /**
     * 获取单个快递对接信息
     * @param array $where
     */
    public function getExpressApiList(array $where);
}