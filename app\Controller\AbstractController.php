<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use App\JsonRpc\RpcService;
use Hyperf\Contract\ConfigInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Contract\ResponseInterface;
use Psr\Container\ContainerInterface;
use Hyperf\View\RenderInterface;
use Hyperf\Logger\LoggerFactory;

abstract class AbstractController
{
    /**
     * @Inject
     * @var ContainerInterface
     */
    public $container;

    /**
     * @Inject
     * @var RequestInterface
     */
    protected $request;

    /**
     * @Inject
     * @var ResponseInterface
     */
    protected $response;

    /**
     * @Inject()
     * @var \Hyperf\Contract\SessionInterface
     */
    protected $session;

    /**
     * @Inject
     * @var RenderInterface
     */
    protected $render;

    /**
     * @Inject()
     * @var ConfigInterface
     */
    public $config;

    public function show(string $template, array $data = []){
        // 当前登录用户名称
        $userInfo = $this->getUserInfo();
        $data = array_merge($data,['title'=>'WMS管理系统','app_name'=>$userInfo['nickname']]);
        return $this->render->render($template, $data);
    }

    public function getContents(string $template, array $data = []){
        return $this->render->getContents($template, $data);
    }

    /**
     * 获取当前登录用户id
     */
    public function getUserId(){
        $userInfo = $this->session->get('userInfo');
        logger()->debug('getUserId',['userInfo'=>$userInfo]);
        return intval($userInfo['uid']);
    }

    /**
     * 获取当前登录用户id
     */
    public function getUserInfo(){
        return $this->session->get('userInfo');
    }

    /**
     * 返回API
     * @access protected
     * @param int $code 状态码
     * @param string $msg 提示信息
     * @param array $data 对应数据
     * @param array $extend 扩展字段
     * @return \Psr\Http\Message\ResponseInterface
     */
    function returnApi($code = 200, $msg = '操作成功',$data = [], $extend = [])
    {
        $return = [
            'msg'  => $msg,
            'code' => $code,
            'data' => [],
        ];
        $return['request_id'] = getRequestId();
        if (!empty($data)) {
            $return['data'] = $data;
        }
        if (!empty($extend)) {
            foreach ($extend as $k => $v) {
                $return[$k] = $v;
            }
        }
        return $this->response->json($return);
    }

    /**
     * 当前是否Ajax请求
     * @access public
     * @param  bool $ajax true 获取原始ajax请求
     * @return bool
     */
    public function isAjax(): bool
    {
        $value  = $this->request->getHeaderLine('X-Requested-With');
        $result = $value && 'xmlhttprequest' == strtolower($value) ? true : false;
        return $result;
    }

    /**
     * 获取分页记录数
     * @param string $limitKey
     * @return int
     */
    protected function pageLimit($limitKey = 'limit'){
        return intval($this->request->input($limitKey, 10));
    }
}
