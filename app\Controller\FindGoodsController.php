<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use App\Constants\ErrorCode;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Constants\SerialType;
use App\Library\Facades\AdminService;
use App\JsonRpc\FindGoodsServiceInterface;
use App\JsonRpc\MessageLogServiceInterface;
use App\JsonRpc\SkuServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Request;
use function _PHPStan_76800bfb5\React\Promise\Stream\first;

/**
 * @Controller()
 */
class FindGoodsController extends AbstractController
{
    /**
     * @Inject()
     * @var FindGoodsServiceInterface
     */
    private $findGoodsService;

    /**
     * @Inject()
     * @var SkuServiceInterface
     */
    private $skuService;

    /**
     * @Inject()
     * @var WarehouseServiceInterface
     */
    private $wService;

    /**
     * @Inject()
     * @var \App\JsonRpc\WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject()
     * @var MessageLogServiceInterface
     */
    private $mess;

    //日志状态
    const ZT = ['-1'=>'-','0'=>'上传数据','1'=>'任务已创建','2'=>'寻到','3'=>'未寻到','4'=>'丢弃','5'=>'任务已作废','6'=>'任务已完成'];

    /**
     * 巡货任务列表
     * @RequestMapping(path="/fg/taskList", methods="get,post")
     */
    public function getTaskList(RequestInterface $request)
    {
        if ($this->isAjax()) {
            $params = $this->request -> all();
            try {
                $page = $params['page'] ?? 1;
                $limit = $params['limit'] ?? 10;
                $list = $this->findGoodsService->getFindGoodsList($params,intval($page), intval($limit));
                return $this -> returnApi(ResponseCode::SUCCESS,'操作成功',$list['data']['list'],['count' =>$list['data']['total'], 'limit' => $limit]);
            } catch (\Exception $exception) {
                $ret = $exception -> getMessage();
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
            }
        }

        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $authWIds = $this->WarehouseService->getWarehouses(['ids' => $wIds, 'type' => 1], ['id', 'name']);
        return $this -> show("/findGoods/taskList",['w_list'=>$authWIds]);

    }

    /**
     * 新建巡货任务
     * @RequestMapping(path="/fg/addTask", methods="get,post")
     *
     */
    public function addTask()
    {
        if ($this->isAjax() && $this->request->isMethod("post")) {
            $params = $this->request -> all();
            $validator = validate()->make($params, [
                'w_id' => 'required|numeric',
                'title' => 'required|string',
                'type' => 'required|numeric|in:1,2',
                'serial_no' => 'required|string',
            ],[
                'title.required'        => '巡货名称必填',
                'serial_no.required'        => '请上传文件',
            ]);

            if ($validator->fails()) {
                return $this->returnApi( ResponseCode::SERVER_ERROR, $validator->errors()->first() );
            }

            // 登录用户
            $userInfo = $this->session->get('userInfo');
            if(!$userInfo) {
                return $this->returnApi( ResponseCode::SERVER_ERROR, '请登录' );
            }

            try {
                $serial_no = $params['serial_no'];
                $xh_info = redis()->get('XH:'.$serial_no.'_success_data') ? json_decode(redis()->get('XH:'.$serial_no.'_success_data'),true) : [];
                if(empty($xh_info)){
                    return $this->returnApi( ResponseCode::SERVER_ERROR, '上传文件不存在' );
                }

                if(0==$xh_info['count']){
                    return $this->returnApi( ResponseCode::SERVER_ERROR, '上传文件中没有正确数据' );
                }

                if($params['type'] != $xh_info['type']){
                    return $this->returnApi( ResponseCode::SERVER_ERROR, '货品类型与上传时不一致，请重新上传或修改货品类型' );
                }

                if($params['w_id'] && $params['w_id'] != $xh_info['w_id']){
                    return $this->returnApi( ResponseCode::SERVER_ERROR, '货品仓库与上传时不一致，请重新上传或修改货品仓库' );
                }

                $params['admin_id'] = $userInfo['uid'] ?? 0;
                $params['admin_name'] = $userInfo['nickname']  ?? '';
                $ret = $this->findGoodsService->createTask($params);

                //记录上传操作日志
                $dr['serial_no'] = $serial_no;
                $dr['admin_id'] = $userInfo['uid'];
                $dr['curr_status'] = -1;
                $dr['op_name'] = $userInfo['nickname'];
                $dr['op_time'] = date('Y-m-d H:i:s');
                $dr['upload_data'] = '';
                $dr['op_data'] = [];
                $dr['op_after_status'] = 0;
                $dr['op_params'] = json_encode($params);
                $this->findGoodsService->recordLog($dr);

                return $this -> returnApi(ResponseCode::SUCCESS,'创建成功',$ret);
            } catch (\Exception $exception) {
                $ret = $exception -> getMessage();
                return $this -> returnApi(ResponseCode::SERVER_ERROR,$ret);
            }
        }
        //$authWIds = $this->wService->getWarehouses();
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $authWIds = $this->WarehouseService->getWarehouses(['ids' => $wIds, 'type' => 1], ['id', 'name']);
        return $this -> show("/findGoods/addTask",['w_list'=>$authWIds]);
    }

    /**
     * 上传寻货文件
     * @RequestMapping(path="/fg/upload", methods="post")
     *
     */
    public function upload(){
        $rule = [
            'type' => ['required', 'integer', 'between:1,2'],
        ];
        $params = $this->request -> all();
        $errors = validate()->make($params, $rule);
        if ($errors->fails()) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, $errors->errors()->first());
        }

        if (!$this->request->hasFile('file')) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '请导入文件');
        }

        $userInfo = $this->getUserInfo();
        //$userWIds = $this->AdminService->organizeWareHouseData($userInfo['uid']);
        $tp = $params['type'];
        $codeName = ($tp==1) ? 'barcode' : 'unique_code';
        $file = $this->request->file('file');
        $filePathName = $file->getPathname();
        $open = fopen($filePathName, "r") or  $this->returnApi(ResponseCode::SERVER_ERROR, '打开文件出错');
        $importData =  fread($open,filesize($filePathName));
        $importData = explode(',',str_replace(["\r\n", "\r", "\n","\t","\s"], ",", $importData));
        $importData = array_filter($importData,function ($v){
            $val = trim($v);
            if($val){
                return strtoupper($val);
            }
        });

        //验证导入数据
        $codes = $error_data = [];
        foreach ($importData as $k=>$d){
            $code[] = trim($d);
            $codes = array_unique($code);
        }

        if(empty($codes)){
            return $this->returnApi(ResponseCode::SERVER_ERROR, ($tp==1) ? '没有找到条码为' : '没有找到店内码');
        }

        //查询相应店内码
        $code_list = $this->findGoodsService->getCodeByInfo($codes,$tp,$params['w_id']);
        $exist_code_map = array_column($code_list,$codeName); //系统中存在的商品码
        $diff_list = array_merge(array_diff($codes,$exist_code_map),array_diff($exist_code_map,$codes)); //系统中不存在的数据

        //检查是否有epc_code
        $exist_epc_unique_code = $diff_epc_unique_code_list = $diff_epc_list = [];
        if($exist_code_map){
            $exist_unique_code= array_column($code_list,'unique_code');
            $epc_list = $this->findGoodsService->getCodeByEpc($exist_unique_code,$params['w_id']);
            $exist_epc_unique_code= array_column($epc_list,'unique_code');//存在epc_code的店内码
            $diff_epc_unique_code_list = array_merge(array_diff($exist_unique_code,$exist_epc_unique_code),array_diff($exist_epc_unique_code,$exist_unique_code)); //系统中不存在的数据
            var_dump('----------------------------',$exist_unique_code,$exist_epc_unique_code,$diff_epc_unique_code_list);
            if(count($diff_epc_unique_code_list)>0){
                $diff_epc_list = $this->findGoodsService->getCodeByInfo($diff_epc_unique_code_list,2);//为方便直接再次查询出相应数据。
            }
        }

        $serial_no = $params['serial_no'] ?? '';
        $findGoods = $this->findGoodsService->getFindGoodsInfo($serial_no,2);
        if(empty($params['serial_no']) || $findGoods){
            $serial_no = serialNoService()->generate(SerialType::FG,intval($params['type']));
        }

        //缓存正确数据
        $success_code_list = [];
        if(count($exist_epc_unique_code)>0){
            $success_code_list = $this->findGoodsService->getCodeByInfo($exist_epc_unique_code,2,$params['w_id']);
        }

        $d = ['type'=>$tp,'w_id'=>$params['w_id'] ?? '','data'=>$success_code_list,'count'=>count($success_code_list)];
        redis()->set('XH:'.$serial_no.'_success_data', json_encode($d), 60*60*3);

        //记录上传操作日志
        $dr['serial_no'] = $serial_no;
        $dr['admin_id'] = $userInfo['uid'];
        $dr['curr_status'] = -1;
        $dr['op_name'] = $userInfo['nickname'];
        $dr['op_time'] = date('Y-m-d H:i:s');
        $dr['upload_data'] = $importData;
        $dr['op_data'] = json_encode($d);
        $dr['op_after_status'] = 0;
        $dr['op_params'] = json_encode($params);
        $this->findGoodsService->recordLog($dr);

        //导常处理
        if(!empty($diff_list) || !empty($diff_epc_unique_code_list)){
            return $this->returnApi(ResponseCode::SUCCESS, '数据有错',['all_sign'=>count($success_code_list),'error_data'=>['no'=>$diff_list,'bno'=>$diff_epc_list],'serial_no'=>$serial_no]);
        }

        return $this->returnApi(ResponseCode::SUCCESS, '上传成功,数据无误',['all_sign'=>count($success_code_list),'error_data'=>['no'=>[],'bno'=>[]],'serial_no'=>$serial_no,'data'=>$d]);
    }


    /**
     * 任务详情
     * @RequestMapping(path="/fg/detail/{fg_id:\d+}", methods="get,post")
     */
    public function detail($fg_id=0)
    {
        if ($this->isAjax() && $this->request->isMethod("post")) {
            $params = $this->request->all();
            $t = $params['t'];
            switch ($t){
                case 1:
                    $brand_group_list = $this->findGoodsService->getFindGoodsBrandCountList(['fg_id' => $fg_id], intval($params['page']) ?? 1, intval($params['limit']) ?? 15);//汇总
                    $data = $brand_group_list['data']['list'] ?? [];
                    $count = $brand_group_list['data']['total'] ?? 0;
                    break;
                case 2:
                    $detai_list = $this->findGoodsService->getFindGoodsDetailList(['fg_id'=>$fg_id], intval($params['page']) ?? 1, intval($params['limit']) ?? 15);//明细
                    $data = $detai_list['data']['list'] ?? [];
                    $count = $detai_list['data']['total'] ?? 0;
                    break;
                default:
                    $find_goods_info = $this->findGoodsService->getFindGoodsInfo($fg_id);
                    $logList = $this->getLogList($find_goods_info['serial_no'],intval($params['page']) ?? 1, intval($params['limit']) ?? 15);//操作日志
                    $data = $logList['data']?? [];
                    $count = $logList['total'] ?? 0;
                    break;
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功',$data,['count'=>$count]);
        }else{
            $find_goods_info = $this->findGoodsService->getFindGoodsInfo($fg_id);
            if (empty($find_goods_info)) {
                return $this->returnApi(ResponseCode::REQUEST_ERROR, "数据不存在");
            }
            return $this->show('findGoods/detail',['fg_info'=>$find_goods_info,'zt'=>json_encode(self::ZT)]);
        }
    }

    /**
     * 作废任务
     * @RequestMapping(path="/fg/toVoid/{fg_id:\d+}", methods="get,post")
     */
    public function toVoid($fg_id=0)
    {
        $detail = $this->findGoodsService->getFindGoodsInfo($fg_id);
        if(empty($detail)){
            return $this->returnApi(ResponseCode::REQUEST_ERROR, "数据不存在");
        }
        if($detail['status']!=0){
            return $this->returnApi(ResponseCode::SERVER_ERROR, "当前数据不允许该操作");
        }
        $ret = $this->findGoodsService->zf($fg_id);
        $userInfo = $this->getUserInfo();
        $dr['serial_no'] = $detail['serial_no'];
        $dr['admin_id'] = $userInfo['uid'];
        $dr['curr_status'] = 0;
        $dr['op_name'] = $userInfo['nickname'];
        $dr['op_time'] = date('Y-m-d H:i:s');
        $dr['upload_data'] = '';
        $dr['op_data'] = json_encode($detail);
        $dr['op_after_status'] = 5;
        $dr['op_params'] = json_encode($this->request->all());
        $this->findGoodsService->recordLog($dr);
        return $this->returnApi();
    }

    public function getLogList($serial_no='',$page=1,$size=15){
        $where = [
            'serial_no' => $serial_no,
        ];
        $start = $params['page'] ?? 1;
        $size = $params['size'] ?? 100;
        return  $this->mess->getLogList( $where, $start, $size,'find_goods_log');
    }
}
