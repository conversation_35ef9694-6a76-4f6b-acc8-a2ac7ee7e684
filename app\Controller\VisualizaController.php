<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Controller;

use App\Constants\ErrorCode;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Exception\ValidateException;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\AttributeServiceInterface;
use App\JsonRpc\BlockServiceInterface;
use App\JsonRpc\BrandServiceInterface;
use App\JsonRpc\CategoryServiceInterface;
use App\JsonRpc\OrderServiceInterface;
use App\JsonRpc\OutStoreService;
use App\JsonRpc\PickOrderServiceInterface;
use App\JsonRpc\SerialNoServiceInterface;
use App\JsonRpc\SkuServiceInterface;
use App\JsonRpc\SpuServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use App\Library\Facades\VisualizaService;
use Hyperf\Contract\SessionInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use App\Library\Facades\AdminService;
use Hyperf\HttpServer\Contract\RequestInterface;

/**
 * Class VisualizaController
 * @Controller
 * @package App\Controller
 */
class VisualizaController extends AbstractController
{
    /**
     * @Inject()
     * @var \App\JsonRpc\AdminServiceInterface
     */
    private $adminService;

    /**
     * @RequestMapping(path="/visualiza/index",methods="get")
     * @return \Psr\Http\Message\ResponseInterface|void
     */
    public function index()
    {
        try {
            $cacheKey = "VisualizaDays";
            $cache = redis()->get($cacheKey);
            if (empty($cache)) {
                redis()->set($cacheKey, 1, 2);
                $days = VisualizaService::days();
            } else {
                $days = VisualizaService::days(['noSave' => 1]);
            }
        } catch (\Exception $e) {
            $days = VisualizaService::days();
        }

        $days = collect($days);

        $citys = $days->pluck('city')->unique()->toArray();

        $visualizas = VisualizaService::visualizas();

        $dayItems = $days->where('city', $citys[0])->all();

        // 当前用户是否有配置权限
        $userInfo = $this->session->get('userInfo');
        $authLimit = $this->adminService->can($userInfo['uid'], '/visualiza/limit', getAppType());

        return $this->show('visualiza/index', [
            'dayItems' => $dayItems,
            'citys' => $citys,
            'authLimit' => $authLimit,
            'visualizas' => $visualizas,
        ]);
    }

    /**
     * @RequestMapping(path="/visualiza/days",methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface|void
     */
    public function days(RequestInterface $request)
    {
        $city = $request->input('city', '');
        $days = VisualizaService::days(['noSave' => 1]);
        $days = collect($days);
        if (!empty($city)) {
            $citys = $days->where('city', $city)->all();
        } else {
            $citys = $days->all();
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', [
            'citys' => $citys,
        ]);
    }

    /**
     * @RequestMapping(path="/visualiza/setDays",methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface|void
     */
    public function setDays(RequestInterface $request)
    {
        $cityData = $request->post();
        logger()->info("入参数据", [$cityData]);
        $result = VisualizaService::setDays($cityData);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $result);
    }

    /**
     * @RequestMapping(path="/visualiza/visualizas",methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface|void
     */
    public function visualizas(RequestInterface $request)
    {
        $city = $request->input('city', '');
        $data = VisualizaService::visualizas(['city' => $city]);
        //处理数据
        $days = [];
        $use = [];
        $free = [];
        $overbooking = [];
        $plan = [];
        $first = [];
        $inStore = [];
        foreach ($data as $item) {
            $extraInfo = ['w_id' => $item['w_id'], 'start_time' => $item['day'] . " 00:00:00", 'end_time' => $item['day'] . " 23:59:59"];
            $days[] = $item['day'] . "/" . ($item['use_num'] + $item['plan_num'] + $item['stock_up_num']);
            $useNum = ($item['use_num'] >= $item['excess_num']) ? ($item['use_num'] - $item['excess_num']) : $item['use_num'];
            $use[] = ['value' => $useNum, 'name' => $item['day'], 'extraInfo' => array_merge($extraInfo, ['type' => 1])];
            $free[] = $item['excess_num'] == 0 ? $item['take_num'] - ($item['use_num']) : 0;
            $overbooking[] = ['value' => $item['excess_num'], 'name' => $item['day'], 'extraInfo' => array_merge($extraInfo, ['type' => 1])];
            $plan[] = ['value' => $item['plan_num'], 'name' => $item['day'], 'extraInfo' => array_merge($extraInfo, ['type' => 2])];
            $first[] = ['value' => $item['stock_up_num'], 'name' => $item['day'], 'extraInfo' => array_merge($extraInfo, ['type' => 3])];
            $inStore[] = ['value' => $item['in_store_num'], 'name' => $item['day'], 'extraInfo' => array_merge($extraInfo, ['type' => 1])];
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['days' => $days, 'use' => $use, 'free' => $free, 'over' => $overbooking, 'plan' => $plan, 'first' => $first,'inStore'=>$inStore]);
    }
}