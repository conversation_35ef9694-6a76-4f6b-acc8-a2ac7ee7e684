<?php
declare(strict_types=1);

namespace App\JsonRpc;

use App\Model\SyncTask;

interface SyncTaskServiceInterface
{
    /**
     * 创建任务
     * @param array $data 任务数据
     * @param int $lockTime 根据服务名及关联单号限制指定时间内不能重复提交任务
     */
    public function add(array $data,$lockTime = 0);

    /**
     * 获取任务列表
     * @param array $where
     */
    public function list(array $where, int $page = 1, int $limit = 10, array $field = ['*']);

    /**
     * 获取详情
     * @param int $id
     * @return mixed
     */
    public function info($id);

    /**
     * 重试任务
     * @param $id
     * @return int
     */
    public function reTry($id);
}