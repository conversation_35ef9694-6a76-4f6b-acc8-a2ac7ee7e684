<?php
declare(strict_types=1);

namespace App\JsonRpc;

Interface BlockServiceInterface
{
    /**
     * 查单个
     * @param array $where
     * @param array|string[] $filed
     */
    public function getBlock(array $where, array $filed = ['*']);

    /**
     * 创建
     * @param array $data
     * @param array $shelf_ids
     * @return bool
     */
    public function create(array $data, array $shelf_ids);

    /**
     * 列表
     * @param int $export
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return array
     */
    public function list(int $export, int $page, int $pageLimit, array $search);

    /**
     * 作废
     * @param int $id
     * @return int
     */
    public function cancel(int $id);

    /**
     * 详情
     * @param int $id
     */
    public function getDetailById(int $id);

    /**
     * 更新
     * @param int $id
     * @param array $data
     * @param array $shelf_ids
     * @return bool
     */
    public function update(int $id, array $data, array $shelf_ids);

    /**
     * 获取暂存区列表
     * @param array $where
     * @param array|string[] $field
     * @return mixed
     */
    public function getBlockLists (array $where = [], array $field = ["*"]);
}