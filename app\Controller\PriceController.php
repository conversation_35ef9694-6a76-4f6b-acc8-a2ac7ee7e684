<?php
declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use App\Constants\CachePre;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Exception\BusinessException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Validation\Rule;
use App\Library\Facades\AdminService;
use App\Library\Facades\SyncTaskService;

/**
 * @Controller()
 */
class PriceController extends AbstractController
{
    /**
     * @Inject()
     * @var \App\JsonRpc\WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject()
     * @var \App\JsonRpc\PriceServiceInterface
     */
    private $PriceService;

    private $priceTypeMap = [
        '1' => '商品编码',
        '2' => '店内码',
        '3' => 'SPU'
    ];

    private $tabType = [
        '1' => '常规调价',
        '2' => '残次调价',
        '3' => '活动变价'
    ];

    /**
     * 列表
     * @RequestMapping(path="/price/list", methods="get,post")
     */
    public function list()
    {
        $data['title'] = "鞋服变价";

        //获取仓库信息
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $warehouseList = $this->WarehouseService->getWarehouses(['ids' => $wIds, 'type' => 1], ['id', 'name']);
        $data['warehouse_list'] = $warehouseList;
        $warehouseInfo = [];
        if (!empty($warehouseList)) {
            $warehouseInfo = array_column($warehouseList, 'name', 'id');
        }
        //生效时间
        $data['date'] = date('Y-m-d 00:00:00') .' - '. date('Y-m-d 23:59:59', strtotime('2 days'));

        if ($this->isAjax()) {
            $where = $this->request->all();
            if (isset($where['export']) && $where['export'] == 1){
                $userInfo = $this->getUserInfo();
                $params = [];
                $params['service_name'] = 'price:exportShopPrice';
                $params['admin_id'] = $userInfo['uid'];
                $params['admin_name'] = $userInfo['nickname'];
                $params['task_name'] = '鞋服变价-' . $this->tabType[$where['price_type']];
                $params['sys_type'] = 'wms';
                $params['params'] = json_encode([[
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                    'where' => $where
                ]]);
                $taskInfo = SyncTaskService::add($params, 10);
                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $taskInfo);
            }else{
                $where['page_size'] = $where['limit'] ?? $this->pageLimit();
                //生效时间
                if (isset($where['effectuate_time']) && !empty($where['effectuate_time'])){
                    $effectuateTime = explode(' - ', $where['effectuate_time']);
                    $where['effectuate_start_time'] = $effectuateTime[0];
                    $where['effectuate_end_time'] = $effectuateTime[1];
                }
                //失效时间
                if (isset($where['finish_time']) && !empty($where['finish_time'])){
                    $finishTime = explode(' - ', $where['finish_time']);
                    $where['finish_start_time'] = $finishTime[0];
                    $where['finish_end_time'] = $finishTime[1];
                }
                //获取数量
                $where['get_price_info'] = true;
                $num_where = $where;
                $num_where['get_count'] = true;
                $dailyCount = $this->PriceService->getShopDaylyPriceList($num_where);
                $imperfectCount = $this->PriceService->getShopImperfectPriceList($num_where);
                $promotionCount = $this->PriceService->getShopPromotionPriceList($num_where);
                $count = 0;
                //获取记录
                $result = [];
                $where['group_by_price'] = true;
                if ($where['price_type'] == 1){#常规调价
                    $dailyList = $this->PriceService->getShopDaylyPriceList($where);
                    foreach ($dailyList as $item){
                        $result[] = [
                            'code' => $item['code'],
                            'w_name' => $warehouseInfo[$where['w_id']],
                            'type_name' => $this->priceTypeMap[$item['type']],
                            'start_time' => $item['start_time'],
                            'lapse_time' => '2099-12-31 23:59:59',
                            'admin_name' => $item['admin_name'],
                            'created_at' => $item['created_at']
                        ];
                    }
                    $count = $dailyCount;
                }else if($where['price_type'] == 2){#残次调价
                    $imperfectList = $this->PriceService->getShopImperfectPriceList($where);
                    foreach ($imperfectList as $item){
                        $result[] = [
                            'code' => $item['code'],
                            'w_name' => $warehouseInfo[$where['w_id']],
                            'type_name' => $this->priceTypeMap[$item['type']],
                            'start_time' => $item['start_time'],
                            'lapse_time' => '2099-12-31 23:59:59',
                            'admin_name' => $item['admin_name'],
                            'created_at' => $item['created_at']
                        ];
                    }
                    $count = $imperfectCount;
                }else if($where['price_type'] == 3){
                    $promotionList = $this->PriceService->getShopPromotionPriceList($where);
                    foreach ($promotionList as $item){
                        $result[] = [
                            'code' => $item['id'],
                            'w_name' => $warehouseInfo[$where['w_id']],
                            'start_time' => $item['start_time'],
                            'lapse_time' => $item['end_time'],
                            'admin_name' => $item['admin_name'],
                            'created_at' => $item['created_at']
                        ];
                    }
                    $count = $promotionCount;
                }
                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $result, ['count' => $count, 'limit' => $where['page_size'], 'dailyCount' => $dailyCount, 'imperfectCount' => $imperfectCount, 'promotionCount' => $promotionCount]);
            }
        }
        return $this->show('price/price', $data);
    }
}