<?php

declare(strict_types=1);

namespace App\JsonRpc;


Interface OutStoreServiceInterface
{
    /**
     * 连表查询出库任务单明细
     * @param array $wheres
     * @return array
     */
    public function getOutStoreDetail(array $wheres = []);

    /**
     * 根据出库单号获取出库单信息
     * @param string $serialNo
     * @return mixed
     */
    public function getOutStoreOneBySerialNo(string $serialNo);

    /**
     * 根据条件获取出库任务单详情信息
     * @param array $where
     */
    public function getOutStoreDetalList(array $where = []);

    /**
     * 修改出库任务单详情
     * @param array $params
     * @return bool
     */
    public function updateOutStoreDetail(array $params = []);

    /**
     * 入库单列表
     * @param int $page
     * @param int $limit
     * @param array $where [
     *      "w_id":"仓库id",
     *      "in_stock_no":"批次号",
     *      "purchase_code":"采购单号",
     *      "sku_id":"sku",
     *      "spu_id":"spu",
     *      "unique_code":"spu",
     *      "supplier_ids":"供应商",
     *      "spu_no":"货号",
     *      "status":"状态",
     *      "start_time":"开始时间",
     *      "end_time":"结束时间"
     * ]
     * @param array|string[] $field
     * @return array
     */
    public function list(int $page = 1, int $limit = 10, array $where = []);


    /**
     * 详请
     * @param int $id
     */
    public function info(int $id);


    /**
     * 详请
     * @param int $id
     */
    public function cancel(int $id);

    /**
     * 详情页
     * @param array $where
     * @param int $page
     * @param int $limit
     */
    public function detailGroupList(array $where = [],int $page = 1, int $limit = 10);

    /**
     * 根据任务单号获取货品区域分布及数量
     * @param array $taskIds
     * @return array
     */
    public function blockInfo(array $taskIds);

    /**
     * 查单个
     * @param array $where
     * @param array|string[] $filed
     */
    public function getOutStore(array $where, array $filed = ['*']);

    /**
     * 详情列表 分页
     * @param int $export
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return array
     */
    public function detailList(int $export, int $page, int $pageLimit, array $search);

    /**
     * 创建出库任务
     * @param array $data [
     *    "serial_no" => 预约单号，
     *    "type" => 出库类型， 21=采购退返，23调拨出库
     *    "admin_id" => 用户id，
     *    "admin_name" => 用户姓名
     * ]
     * @return mixed
     */
    public function add( array $data);

    /**
     * 详情页列表
     * @param array $where
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function OutDetailList(array $where = [],int $page = 1, int $limit = 10);

    /**
     * 完成入库
     * @param $taskId 任务单id
     */
    public function finish($taskId);

    /**
     * 二次出库
     * @param $params $taskId
     * @return mixed
     */
    public function secondOut($taskId);

    /**
     * 获取未出库的sku数量
     * 一个调拨单（或退返单）某个条码的可以装箱的数量 = 调拨单（或退返单）对应`出库任务单`中该条码的出库总数（out_num） - 装箱记录表中该调拨单（或退返单）中该条码的装箱状态正常且已发货数量总和（num）
     * @param $serialNo
     * @param $goodsCode // 商品码：店内码或条码
     * @param $codeType // 商品码类型：1=店内码，2=条码
     * @param $type '单据类型'
     */
    public function getNotOutStoreNum(string $serialNo,int $type, $goodsCode,$codeType);

    /**
     * 根据关联单号获取有效的出库任务
     * @param string $orderNo
     */
    public function getValidByOrderNo($orderNo);

    public function getNotOutStoreBarcodeNum(string $serialNo,int $type, $goodsCodes,$codeType);

    /**
     * 批量获取未出库的sku数量
     * 一个调拨单（或退返单）某个条码的可以装箱的数量 = 调拨单（或退返单）对应`出库任务单`中该条码的出库总数（out_num） - 装箱记录表中该调拨单（或退返单）中该条码的装箱状态正常且已发货数量总和（num）
     * @param $serialNo
     * @param $goodsCodes // 商品码：店内码或条码
     * @param $codeType // 商品码类型：1=店内码，2=条码
     * @param $type '装箱任务类型' 1=调拨任务，2=退返任务
     */
    public function getNotOutStoreNumByBatch(string $serialNo,int $type, $goodsCodes,$codeType);

    /**
     * 根据关联单号获取有效的出库数量
     * @param $orderNo
     * @return mixed
     */
    public function getOutStoreNumByOrderNo($orderNo);


    /**
     * 发送邮件附件内容
     * @param array $serialNos
     * @param int $outType
     * @return mixed
     */
    public function getSendMailAnnexDetail(array $serialNos, int $outType);

    /**
     * 设置异常原因
     * @param $detailId
     * @param array $data
     * @return mixed
     */
    public function setException($detailId, array $data);

    /**
     * 线上订单快递交接
     * @param array $params
     * [
     *  'c_id' => 1,#快递公司id
     *  'w_id' => '1',#仓库id
     *  'logistics_serial_nos' = '' , #物流单号
     *  ’user_info‘ = [
     *      'uid' => 1,
     *      'nickname' => ''
     *  ]#用户信息
     * ]
     */
    public function onlineOrderDelivery($params = []);
}