<?php

namespace App\JsonRpc;

interface SkuServiceInterface
{
    /**
     * 获取sku数据
     * @param array $where
     */
    public function checkSkuInfo(array $where);
    
    /**
     * 通过条形码获取商品信息
     */
    public function getSkuInfo(array $barcode);

    /**
     * 通过规格key值获取商品名称
     */
    public function getSpecName(array $spec_kvs);

    /**
     * 获取sku与barcode的对应关系
     * @param array $params
     */
    public function getSkuBarcodeMap(array $params);

    /**
     * 查单个
     * @param array $where
     * @param array|string[] $filed
     */
    public function getSku(array $where, array $filed = ['*']);

    /**
     * 批量校验spu
     */
    public function checkSpuBarcode(array $data, $isTempFiltered = true);

    /**
     * 批量通过规格key值获取规格名称
     */
    public function getSpecNameBatch(array $spec_kvs);

    /**
     * 根据sku_id获取商品信息
     * @param array $skuIds
     * @param int $channelId
     * @return mixed
     */
    public function getSkuInfoById(array $skuIds, int $channelId);
}