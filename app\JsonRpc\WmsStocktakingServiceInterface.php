<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * wms盘点服务消费者
 */
Interface  WmsStocktakingServiceInterface
{
    /**
     * 添加盘点任务
     * @param array $data
     * @return mixed
     */
    public function addStocktakingTask(array $data);

    /**
     * 盘点任务单详情
     * @param int $id
     */
    public function getStocktakingTaskOne(int $id);

    /**
     * 盘点任务单列表
     * @param int $page
     * @param int $limit
     * @param array $where
     * @param array $field
     */
    public function getStocktakingTaskList($authWIds,int $page = 1, int $limit = 10, array $where = [], array $field = []);

    /**
     * 任务详情 - 货架分布
     */
    public function getShelfDistributionList(int$page, int $limit,int $st_id);

    /**
     * 任务详情 - 货架分布 查询明细
     * @param int $page
     * @param int $limit
     * @param int $st_id
     * @param $shelfLine
     * @return array|string
     */
    public function getShelfDistributionDetail(int$page, int $limit,int $st_id,$shelfLine);

    /**
     * 确定录入 - 保存上传的盘点数据
     * @param $stId '盘点任务id'
     * @param $stWay '盘点方式'
     * @param $data '盘点数据'
     * @return mixed
     */
    public function saveSbData($stId,$stWay,$data);


    /**
     * 获取某一任务下的实盘结果
     * @param int $st_id
     * @param int $page
     * @param int $limit
     * @param array $where
     * @param array $field
     */
    public function getStResultListByStId(int $st_id,int $page = 1, int $limit = 10, array $where = [], array $field = []);

    /**
     * 获取盘点任务统计数据
     * @param $st_id
     */
    public function getStatistics($st_id);

    /**
     * 更改状态
     * @param $st_id '盘点状态: 1=待盘点，2=盘点中，3=待审核，4=已完成，5=已作废，6=驳回'
     * @param $data '更新数据'
     */
    public function updateStatus($st_id,$data);

    /**
     * 报表列表
     * @param $st_id
     */
    public function getReportList($st_id);

    /**
     * 动盘（商超）
     * @param $stId
     * @param $stWay
     * @param $data
     */
    public function stDynamic($wId,$stId,$stWay,$adminId,$data);

    /**
     * 创建盘点审批单
     * @param $data
     */
    public function addStResultAudit($data);
    /**
     * 审批单列表
     * @param $data
     */
    /**
     * 审批单列表
     * @param $data
     */
    public function getStResultAuditList($authWIds,int $page = 1, int $limit = 10, array $where = [], array $field = []);

    /**
     * 盘点审核单详情
     * @param int $id
     */
    public function getStResultAuditDetail(int $id);

    /**
     * 盘点结果审批单状态更改-审核
     * @param $id
     * @param $data
     */
    public function updateStAuditStatus($id,$data);

    /**
     * 获取某一任务下的盘点卡列表
     * @param int $st_id
     * @param int $page
     * @param int $limit
     * @param array $where
     * @param array $field
     */
    public function getStCardListByStId(int $st_id,int $page = 1, int $limit = 10, array $where = [], array $field = []);

    /**
     * 获取盘点综合结果摘要：盘盈，盘亏
     * @param $stId
     * @return \Hyperf\Database\Model\Builder[]|\Hyperf\Database\Model\Collection
     */
    public function getStResultSummaryById($stId);
    /*******************************************************************************************************************/

    /**
     * 根据盘点任务单ID获取盘盘点清单列表
     * @param int $stId 盘点任务id
     * @param int $page
     * @param int $limit
     * @param array $where
     * @param array $field
     */
    // public function getStocktakingBillList(int $stId, int $page = 1, int $limit = 10, array $where = [], array $field = []);

    /**
     * 根据盘点任务id获取盘点清单统计信息
     * @param int $stId 盘点任务id
     */
    // public function getStocktakingBillStatistics(int $stId = 0);

    /**
     * 更新状态
     * @param int $id 盘点任务id
     * @param array $data
     */
    // public function updateStatus(int $id, array $data);

    /**
     * 批量导入盘点数据
     * @param array $id 盘点任务id
     * @param array $importType 导入类型 1=商品维度（条码），2=唯一马维度，3=epc码维度
     * @param array $data
     */
    // public function importBill(int $id, int $importType, array $data);

    /**
     * 获取所有盈亏数据
     * @param int $stId 盘点任务id
     */
    // public function getPlData(int $stId);

}