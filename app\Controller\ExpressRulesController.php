<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Controller;

use App\Constants\ErrorCode;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Exception\ValidateException;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\AreaServiceInterface;
use App\JsonRpc\ExpressRuleServiceInterface;
use App\JsonRpc\LogisticsServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;

/**
 * Class ExpressRulesController
 * @Controller
 * @package App\Controller
 */
class ExpressRulesController extends AbstractController
{

    /**
     * @Inject()
     * @var AdminServiceInterface
     */
    private $AdminService;

    /**
     * @Inject
     * @var WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject ()
     * @var LogisticsServiceInterface
     */
    private $LogisticsService;

    /**
     * @Inject ()
     * @var AreaServiceInterface
     */
    private $AreaService;

    /**
     * @Inject
     * @var ExpressRuleServiceInterface
     */
    private $ExpressRuleService;

    static private $userInfo;
    static private $wIds;

    public function __construct ()
    {
        self::$userInfo = parent::getUserInfo();
        self::$wIds = $this->AdminService->organizeWareHouseData( self::$userInfo['uid'] );
    }

    /**
     * @RequestMapping(path="/expressRules/rules",methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface|void
     */
    public function rules ()
    {
        $warehouse = [];
        if (!empty( self::$wIds )) {
            $warehouse['ids'] = self::$wIds;
        }
        $warehouse_info = collect( $this->WarehouseService->getWarehouses( $warehouse ) )->pluck( 'name', 'id' )->all();

        //查询所有快递公司
        $logistics_info = $this->LogisticsService->getLogisticsConfigs( ['status' => 1, 'w_ids' => self::$wIds] );

        $logistics_list = array_column( $logistics_info, 'name', 'lg_id' );

        //获取一级区域 省
        $prov_list = $this->AreaService->getAreaInfo( ['level' => 1] );
        $express_rules_status = PublicCode::express_rules_status;
        if ($this->isAjax()) {
            $where = $this->request->all();
            $where['export'] = $where['export'] ?? 0;
            $where['limit'] = $where['limit'] ?? $this->pageLimit();
            $result = $this->ExpressRuleService->getExpressRuleLists( $where );
            if (!empty( $result['data'] )) {
                $area_list = collect( $this->AreaService->getAreaInfo( [] ) )->pluck( 'name', 'code' )->toArray();
                foreach ($result['data'] as $key => $item) {
                    $result['data'][$key]['ruleStr'] = '';
                    if (!empty( $item['rules'] )) {
                        foreach ($item['rules'] as $rule) {
                            $result['data'][$key]['ruleStr'] .= $logistics_list[$rule['logistics_config_id']] . '（比例：' . round( ($rule['rate_end'] - $rule['rate_start'] + 1) / 100 ) . '%）' . '</br>';
                            unset( $rule );
                        }
                        unset( $item['rules'] );
                    }
                    $result['data'][$key]['w_name'] = $warehouse_info[$item['w_id']];
                    $result['data'][$key]['prov_name'] = $area_list[$item['prov']] ?? '全部省份';
                    $result['data'][$key]['city_name'] = $area_list[$item['city']] ?? '全部地市';
                    $result['data'][$key]['area_name'] = $area_list[$item['area']] ?? '全部区县';
                    $result['data'][$key]['status_name'] = $express_rules_status[$item['status']] ?? '-';
                }
            }

            if ($where['export'] == 1) {
                $result['total'] = count( $result['data'] );
            }
            return $this->returnApi( ResponseCode::SUCCESS, '操作成功', $result['data'], ['count' => $result['total'], 'limit' => $where['limit'], 'export' => $where['export']] );
        }
        return $this->show( 'express/rules', [
            'warehouse_info' => $warehouse_info,
            'logistics_info' => $logistics_list,
            'prov_list' => $prov_list,
            'express_rules_status' => $express_rules_status,
        ] );
    }

    /**
     * @RequestMapping(path="/expressRules/rulesArea",methods="post")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function rulesArea ()
    {
        $code = $this->request->post( 'code' );
        $level = $this->request->post( 'level' );
        try {
            $result = $this->AreaService->getAreaInfo( ['likeCode' => $code, 'level' => $level] );
        } catch (\Exception $exception) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, $exception->getMessage() );
        }
        return $this->returnApi( ResponseCode::SUCCESS, '操作成功', $result );
    }

    /**
     * @RequestMapping(path="/expressRules/addRules",methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface|void
     */
    public function addRules ()
    {
        $params = $this->request->all();
        $warehouse = [];
        if (!empty( self::$wIds )) {
            $warehouse['ids'] = self::$wIds;
        }
        $warehouse_info = collect( $this->WarehouseService->getWarehouses( $warehouse ) )->pluck( 'name', 'id' )->all();

        //查询所有快递公司
        $logistics_info = $this->LogisticsService->getLogisticsConfigs( ['status' => 1, 'w_ids' => self::$wIds] );

        //获取一级区域 省
        $prov_list = $this->AreaService->getAreaInfo( ['level' => 1] );
        $express_rules_status = PublicCode::express_rules_status;

        //处理规则编辑
        $detail['rules'] = [];
        $detail['id'] = 0;
        $detail['w_id'] = 0;
        $detail['rule_id'] = 0;
        $detail['prov'] = 0;
        $detail['city'] = 0;
        $detail['area'] = 0;
        if (!empty( $params['id'] )) {
            $detailInfo = $this->ExpressRuleService->getExpressRuleDetailByRuleId( (int)$params['rule_id'] );
            if (!empty( $detailInfo['rules'] )) {
                foreach ($detailInfo['rules'] as $d) {
                    $detail['rules'][$d['logistics_config_id']] = (($d['rate_end'] - $d['rate_start'] + 1) / 100);
                }
            }
            $detail['w_id'] = $detailInfo['w_id'];
            $detail['rule_id'] = $detailInfo['rule_id'];
            $detail['prov'] = $detailInfo['prov'];
            $detail['city'] = $detailInfo['city'];
            $detail['area'] = $detailInfo['area'];
            $detail['id'] = $detailInfo['id'];
        }

        if ($this->isAjax()) {
            $params = $this->request->post();
            try {
                //省
                if (count( $params['prov'] ) == 1) {
                    $insert['prov'] = current( $params['prov'] );
                } else {
                    $provs = $params['prov'];
                }

                //市
                if (!empty( $params['city'] ) && count( $params['city'] ) == 1) {
                    $insert['city'] = current( $params['city'] );
                } else {
                    $citys = $params['city'] ?? [];
                }

                //区
                if (!empty( $params['area'] ) && count( $params['area'] ) == 1) {
                    $insert['area'] = current( $params['area'] );
                } else {
                    $areas = $params['area'] ?? [];
                }

                //快递
                $express = $params['rate'];

                //其他参数
                $insert['w_id'] = $params['w_id'];
                $insert['status'] = $params['status'];

                $rule_id = $this->ExpressRuleService->getExpressMaxRuleId();
                $rule_id++;
                $insertData = [];
                //开始处理数据
                if (!empty( $provs )) {
                    foreach ($provs as $provItem) {
                        $rate_start = 1;
                        foreach ($express as $express_id => $rate) {
                            $data = $insert;
                            $data['rule_id'] = $rule_id;
                            $data['logistics_config_id'] = $express_id;
                            $data['district'] = '001';
                            $data['prov'] = $provItem;
                            $data['rate_start'] = $rate_start;
                            $data['rate_end'] = $rate_start + $rate * 100 - 1;
                            $data['admin_id'] = self::$userInfo['uid'];
                            $data['admin_name'] = self::$userInfo['nickname'];
                            $rate_start = $data['rate_end'] + 1;
                            $insertData[] = $data;
                            unset( $express_id, $rate, $data );
                        }
                        $rule_id++;
                        unset( $provItem );
                    }
                } elseif (!empty( $citys )) {
                    foreach ($citys as $cityItem) {
                        $rate_start = 1;
                        foreach ($express as $express_id => $rate) {
                            $data = $insert;
                            $data['rule_id'] = $rule_id;
                            $data['logistics_config_id'] = $express_id;
                            $data['district'] = '001';
                            $data['city'] = $cityItem;
                            $data['rate_start'] = $rate_start;
                            $data['rate_end'] = $rate_start + $rate * 100 - 1;
                            $data['admin_id'] = self::$userInfo['uid'];
                            $data['admin_name'] = self::$userInfo['nickname'];
                            $rate_start = $data['rate_end'] + 1;
                            $insertData[] = $data;
                            unset( $express_id, $rate, $data );
                        }
                        $rule_id++;
                        unset( $cityItem );
                    }
                } elseif (!empty( $areas )) {
                    foreach ($areas as $areaItem) {
                        $rate_start = 1;
                        foreach ($express as $express_id => $rate) {
                            $data = $insert;
                            $data['rule_id'] = $rule_id;
                            $data['logistics_config_id'] = $express_id;
                            $data['district'] = '001';
                            $data['area'] = $areaItem;
                            $data['rate_start'] = $rate_start;
                            $data['rate_end'] = $rate_start + $rate * 100 - 1;
                            $data['admin_id'] = self::$userInfo['uid'];
                            $data['admin_name'] = self::$userInfo['nickname'];
                            $rate_start = $data['rate_end'] + 1;
                            $insertData[] = $data;
                            unset( $express_id, $rate, $data );
                        }
                        $rule_id++;
                        unset( $areaItem );
                    }
                } else {
                    $rate_start = 1;
                    foreach ($express as $express_id => $rate) {
                        $data = $insert;
                        $data['rule_id'] = $rule_id;
                        $data['logistics_config_id'] = $express_id;
                        $data['district'] = '001';
                        $data['rate_start'] = $rate_start;
                        $data['rate_end'] = $rate_start + $rate * 100 - 1;
                        $data['admin_id'] = self::$userInfo['uid'];
                        $data['admin_name'] = self::$userInfo['nickname'];
                        $rate_start = $data['rate_end'] + 1;
                        $insertData[] = $data;
                        unset( $express_id, $rate, $data );
                    }
                }

                $result = $this->ExpressRuleService->creteExpressRules( $insertData );

            } catch (\Exception $exception) {
                return $this->returnApi( ErrorCode::SERVER_ERROR, $exception->getMessage() );
            }
            return $this->returnApi( ResponseCode::SUCCESS, '操作成功', $result );
        }

        $data = [
            'warehouse_info' => $warehouse_info,
            'logistics_info' => $logistics_info,
            'prov_list' => $prov_list,
            'detail' => $detail,
            'express_rules_status' => $express_rules_status,
        ];
        return $this->show( 'express/add', $data );
    }

    /**
     * @RequestMapping(path="/expressRules/rulesWarehouse",methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function rulesWarehouse ()
    {
        $result = [];
        $w_id = $this->request->post( 'w_id' );
        try {
            if (empty( $w_id )) {
                throw new ValidateException( '请选择仓库' );
            }
            $logistics = $this->LogisticsService->getLogisticsConfigByWId( (int)$w_id );
            if (!empty( $logistics )) {
                foreach ($logistics as $logisticItem) {
                    $result[$logisticItem['id']] = $logisticItem;
                }
            }

            if (empty( $result )) {
                throw new ValidateException( '无快递关联信息' );
            }
        } catch (\Exception $exception) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, $exception->getMessage() );
        }
        return $this->returnApi( ResponseCode::SUCCESS, '操作成功', array_values( $result ) );
    }

    /**
     * @RequestMapping(path="/expressRules/editRules",methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface|void
     */
    public function editRules ()
    {
        $params = $this->request->post();
        try {
            if (empty( $params['id'] )) {
                throw new ValidateException( '缺少参数ID' );
            }

            $detailInfo = $this->ExpressRuleService->getExpressRuleDetailByRuleId( (int)$params['rule_id'] );
            $rulesData = [];
            if (!empty( $detailInfo['rules'] )) {
                foreach ($detailInfo['rules'] as $item) {
                    $rulesData[$item['logistics_config_id']] = $item;
                }
            }

            $updateDate = [];
            $insertDate = [];
            if (!empty( $params['rate'] )) {
                $rate_start = 1;
                foreach ($params['rate'] as $lg_id => $rate) {
                    if (isset( $rulesData[$lg_id] )) {
                        $update['where']['id'] = $rulesData[$lg_id]['id'];
                        $update['data']['status'] = $params['status'];
                        $update['data']['rate_start'] = $rate_start;
                        $update['data']['rate_end'] = $rate_start + $rate * 100 - 1;
                        $update['data']['admin_id'] = self::$userInfo['uid'];
                        $update['data']['admin_name'] = self::$userInfo['nickname'];
                        $rate_start = $update['data']['rate_end'] + 1;
                        $updateDate[] = $update;
                    } else {
                        $insert['rule_id'] = $params['rule_id'];
                        $insert['logistics_config_id'] = $lg_id;
                        $insert['district'] = '001';
                        $insert['status'] = $params['status'];
                        $insert['w_id'] = $detailInfo['w_id'];
                        $insert['prov'] = $detailInfo['prov'];
                        $insert['city'] = $detailInfo['city'];
                        $insert['area'] = $detailInfo['area'];
                        $insert['rate_start'] = $rate_start;
                        $insert['rate_end'] = $rate_start + $rate * 100 - 1;
                        $insert['admin_id'] = self::$userInfo['uid'];
                        $insert['admin_name'] = self::$userInfo['nickname'];
                        $rate_start = $insert['rate_end'] + 1;
                        $insertDate[] = $insert;
                    }
                    unset( $update, $rate, $insert, $lg_id );
                }
            }

            $result = $this->ExpressRuleService->updateExpressRules( $updateDate, $insertDate );
        } catch (\Exception $exception) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, $exception->getMessage() );
        }
        return $this->returnApi( ResponseCode::SUCCESS, '修改成功', $result );
    }
}