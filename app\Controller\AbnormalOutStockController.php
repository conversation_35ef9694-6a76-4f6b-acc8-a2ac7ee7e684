<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Controller;

use App\Constants\ErrorCode;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Exception\BusinessException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use App\Library\Facades\AdminService;
use Hyperf\Validation\Rule;
use function foo\func;
use function Symfony\Component\Finder\in;
use function Symfony\Component\HttpFoundation\isSecure;

/**
 * @Controller()
 */
class AbnormalOutStockController extends AbstractController
{
    /**
     * @Inject()
     * @var \App\JsonRpc\WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject()
     * @var \App\JsonRpc\AbnormalOutStockServiceInterface
     */
    private $AbnormalOutStockService;

    /**
     * @Inject()
     * @var \App\Service\HashService
     */
    private $HashService;

    //状态
    private $status = [
        '1' => '创建',
        '2' => '部分',
        '3' => '完成'
    ];

    private $code_type = [
        '1' => '店内码',
        '2' => '条形码'
    ];

    /**
     * 错下架货品列表
     * @RequestMapping(path="/abnormalOutStock/list", methods="get,post")
     * @return mixed
     */
    public function list(RequestInterface $request){
        $data['title'] = '错下架货品';

        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        //获取仓库信息
        $warehouse_list = $this->WarehouseService->getWarehouses(['ids' => $wIds], ['id', 'name']);

        if ($this->isAjax()) {
            $search = $request->all();
            $page = intval($request->input('page', 1));
            $limit = intval($request->input('limit', 30));

            //时间
            if (!empty($search['date_range'])) {
                $dateRangeArray = explode(' - ', $search['date_range']);
                $where['start_time'] = $dateRangeArray[0];
                $where['end_time'] = $dateRangeArray[1];
            }
            //货品类型
            if (!empty($search['code_type']) && array_key_exists($search['code_type'], $this->code_type)) {
                $detail_where = [
                    'code_type' => $search['code_type'],
                    'codes' => explode("\n", $search['code_value'])
                ];
                $abnormal_out_stock_detail = $this->AbnormalOutStockService->detail($detail_where, ['abnormal_out_stock_id']);
                if (empty($abnormal_out_stock_detail['data'])){
                    return $this->returnApi(ResponseCode::SUCCESS, '操作成功', [], ['count' => 0, 'limit' => $limit]);
                }

                $where['ids'] = array_column($abnormal_out_stock_detail['data'], 'abnormal_out_stock_id');
            }
            //仓库
            if (!empty($search['warehouse_ids'])) {
                $where['w_ids'] = explode(',', $search['warehouse_ids']);
                unset($search['warehouse_ids']);
            }else{
                $where['w_ids'] = $wIds;
            }
            //状态
            if (isset($search['status']) && !empty($search['status'])){
                $where['status'] = $search['status'];
            }
            $where['order_by_id_desc'] = true;

            //获取错下架列表
            $list = $this->AbnormalOutStockService->list($where, $page, $limit);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $limit]);
        }

        $data = [
            'warehouse_list' => $warehouse_list,
            'status_list' => $this->status,
            'code_type' => $this->code_type
        ];

        return $this->show('abnormalOutStock/list', $data);
    }

    /**
     * @RequestMapping(path="/abnormalOutStock/export", methods="get,post")
     * @return mixed
     */
    public function export(RequestInterface $request)
    {
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        //获取仓库信息
        $warehouse_list = $this->WarehouseService->getWarehouses(['ids' => $wIds], ['id', 'name']);

        $search = $request->all();
        $page = 1;
        $limit = 100;

        //时间
        if (!empty($search['date_range'])) {
            $dateRangeArray = explode(' - ', $search['date_range']);
            $where['start_time'] = $dateRangeArray[0];
            $where['end_time'] = $dateRangeArray[1];
        }
        //货品类型
        if (!empty($search['code_type']) && array_key_exists($search['code_type'], $this->code_type)) {
            $where['code_type'] = $search['code_type'];
            $where['codes'] = explode("\r\n", $search['code_value']);
        }
        //仓库
        if (!empty($search['warehouse_ids'])) {
            $where['w_ids'] = explode(',', $search['warehouse_ids']);
            unset($search['warehouse_ids']);
        }else{
            $where['w_ids'] = $wIds;
        }
        //状态
        if (isset($search['abnormal_out_stock_id']) && !empty($search['abnormal_out_stock_id'])){
            $where['abnormal_out_stock_id'] = $search['abnormal_out_stock_id'];
        }
        if (isset($search['status']) && !empty($search['status'])){
            $where['status'] = $search['status'];
        }

        $where['field'] = [ 'abnormal_out_stock.source_serial_no','abnormal_out_stock.w_id','abnormal_out_stock.w_name','aosd.code','aosd.shelf_code','aosd.out_num','aosd.tally_num','aosd.no_tally_num', 'abnormal_out_stock.created_at', 'abnormal_out_stock.admin_name'];

        $header = [
            'source_serial_no' => '来源单号',
            'w_name' => '仓库名称',
            'code' => '店内码/条形码',
            'shelf_code' => '货架号',
            'out_num' => '下架数量',
            'tally_num' => '理货数量',
            'no_tally_num' => '无需理货数量',
            'created_at' => '创建时间',
            'admin_name' => '创建人'
        ];
        $data = [];

        while (true){
            $detail_list = $this->AbnormalOutStockService->AbnormalOutStockDetailList($where, $page, $limit);
            if (empty($detail_list)){
                break;
            }
            $page++;
            foreach ($detail_list as $item){
                $data[] = [
                    'source_serial_no' => $item['source_serial_no'],
                    'w_name' => $item['w_name'],
                    'code' => $item['code'],
                    'shelf_code' => $item['shelf_code'],
                    'out_num' => $item['out_num'],
                    'tally_num' => $item['tally_num'],
                    'no_tally_num' => $item['no_tally_num'],
                    'created_at' => $item['created_at'],
                    'admin_name' => $item['admin_name']
                ];
            }
        }

        $info = exportCsv($header, $data, '错下架详情');
        return $info;
    }

    /**
     * @RequestMapping(path="/abnormalOutStock/detail", methods="get,post")
     * @return mixed
     */
    public function detail(RequestInterface $request){
        $data['title'] = "错下架明细";
        // 存在则返回，不存在则返回默认值 null
        $search = $request->all();
        $id = $search['id'];
        if ($this->isAjax()) {
            $where['abnormal_out_stock_id'] = $id;
            $where['perPage'] = intval($request->input('page', 1));
            $where['currentPage'] = intval($request->input('limit', 30));
            //货品类型
            if (!empty($search['code_type']) && array_key_exists($search['code_type'], $this->code_type)) {
                $where['code_type'] = $search['code_type'];
                $where['codes'] = explode("\n", $search['code_value']);
            }
            $info = $this->AbnormalOutStockService->detail($where, ['*']);

            if (!empty($info['data'])){
                //获取明细有效的无需理货记录
                $abnormal_out_stock_detail_ids = array_column($info['data'], 'id');
                $no_tally_where = [
                    'abnormal_out_stock_detail_ids' => $abnormal_out_stock_detail_ids,
                    'status' => 1
                ];
                $no_tally_detail_list = $this->AbnormalOutStockService->noTallyDetailList($no_tally_where);
                $abnormal_out_stock_detail_ids = [];
                if (!empty($no_tally_detail_list['data'])){
                    $abnormal_out_stock_detail_ids = array_column($no_tally_detail_list['data'], 'abnormal_out_stock_detail_id');
                }
                foreach ($info['data'] as &$item){
                    $item['is_have_no_tally'] = false;
                    if (in_array($item['id'], $abnormal_out_stock_detail_ids)){
                        $item['is_have_no_tally'] = true;
                    }elseif($item['status'] == 3){
                        $item['is_have_no_tally'] = true;
                    }
                }
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $info['data'], ['count' => $info['total'], 'limit' => $where['currentPage']]);
        }
        $data = [
            'code_type' => $this->code_type,
            'id' => $id
        ];
        return $this->show('abnormalOutStock/detail', $data);
    }

    /**
     * 无需理货 单个
     * @RequestMapping(path="/abnormalOutStock/opetion_no_tally_num", methods="get,post")
     * @return mixed
     */
    public function opetion_no_tally_num(RequestInterface $request){
        $search = $request->all();
        //获取详情信息
        $where = [
            'detail_id' => $search['abnormal_out_stock_detail_id']
        ];
        $abnormal_out_stock_detail = $this->AbnormalOutStockService->detailOne($where, ['*']);
        if (empty($abnormal_out_stock_detail)){
            return $this->returnApi( ErrorCode::SERVER_ERROR, '错下架详情不存在' );
        }
        if ($abnormal_out_stock_detail['status'] == 3){
            return $this->returnApi( ErrorCode::SERVER_ERROR, '错下架详情已完成' );
        }

        if ($search['no_tally_num'] > ($abnormal_out_stock_detail['out_num']-$abnormal_out_stock_detail['tally_num'])){
            return $this->returnApi( ErrorCode::SERVER_ERROR, '无需理货数量不能大于'.($abnormal_out_stock_detail['out_num']-$abnormal_out_stock_detail['tally_num']) );
        }

        //获取错下架单据
        $abnormal_out_stock_info = $this->AbnormalOutStockService->info(['id' => $search['operation_abnormal_out_stock_id']]);
        if (empty($abnormal_out_stock_info)){
            return $this->returnApi( ErrorCode::SERVER_ERROR, '错下架单据不存在' );
        }
        if ($abnormal_out_stock_info['status'] == 3){
            return $this->returnApi( ErrorCode::SERVER_ERROR, '错下架单据已处理完毕，无法再处理' );
        }

        //错下架单据
        $modify_info_data = [
            'no_tally_num' => ($abnormal_out_stock_info['no_tally_num'] + $search['no_tally_num']) > $abnormal_out_stock_info['out_num'] ? $abnormal_out_stock_info['out_num'] : ($abnormal_out_stock_info['no_tally_num'] + $search['no_tally_num']),
            'status' => 2
        ];
        if (($abnormal_out_stock_info['no_tally_num'] + $abnormal_out_stock_info['tally_num']) >= $abnormal_out_stock_info['out_num']){// 无需理货数 + 已理货数 = 错下架数据
            $modify_info_data['status'] = 3;
        }
        $modify_info_where = [
            'id' => $search['operation_abnormal_out_stock_id']
        ];


        //无需理货数据
        $modify_detail_data = [
            'no_tally_num' => $search['no_tally_num'],
            'status' => 2
        ];
        if (($search['no_tally_num'] + $abnormal_out_stock_detail['tally_num']) >= $abnormal_out_stock_detail['out_num']){// 无需理货数 + 已理货数 = 错下架数据
            $modify_detail_data['status'] = 3;
        }

        //无需理货条件
        $modify_detail_where = [
            'id' => $abnormal_out_stock_detail['id']
        ];

        $modify_detail_list[] = [
            'modify_detail_where' => $modify_detail_where,
            'modify_detail_data' => $modify_detail_data
        ];

        //无需理货记录
        $user_info = $this->session->get('userInfo');
        $no_tally_detail = [
            "abnormal_out_stock_detail_id" => $abnormal_out_stock_detail['id'],
            "code_type" => $abnormal_out_stock_detail['code_type'],
            "code" => $abnormal_out_stock_detail['code'],
            "shelf_code" => $abnormal_out_stock_detail['shelf_code'],
            "reason" => $search['reason'],
            "no_tally_num" => $search['no_tally_num'],
            'admin_id' => $user_info['uid'],
            'admin_name' => $user_info['nickname'],
            "status" => 1,
        ];


        $data = [
            'modify_detail_data' => $modify_detail_data,
            'modify_detail_where' => $modify_detail_where,
            'no_tally_detail' => $no_tally_detail,
            'modify_info_data' => $modify_info_data,
            'modify_info_where' => $modify_info_where,
            'modify_detail_list' => $modify_detail_list,
        ];

        try {
            $this->AbnormalOutStockService->modifyAbnormalOutStockDetail($data);
        } catch (\Exception $exception) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, $exception->getMessage() );
        }
        return $this->returnApi( ResponseCode::SUCCESS, '成功', [] );
    }

    /**
     * 无需理货记录
     * @RequestMapping(path="/abnormalOutStock/no_tally_detail", methods="get,post")
     * @return mixed
     */
    public function no_tally_detail(RequestInterface $request){
        $data['title'] = "无需理货记录";
        // 存在则返回，不存在则返回默认值 null
        $search = $request->all();
        $id = $search['id'];
        if ($this->isAjax()) {
            $where = [
                'abnormal_out_stock_detail_id' => $id
            ];
            $where['perPage'] = intval($request->input('page', 1));
            $where['currentPage'] = intval($request->input('limit', 30));
            $info = $this->AbnormalOutStockService->noTallyDetailList($where, ['*']);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $info['data'], ['count' => $info['total'], 'limit' => $where['currentPage']]);
        }

        $data = [
            'id' => $id
        ];
        return $this->show('abnormalOutStock/noTallyDetail', $data);
    }

    /**
     * 作废无需理货记录
     * @RequestMapping(path="/abnormalOutStock/cancel_no_tall_detail", methods="get,post")
     * @return mixed
     */
    public function cancel_no_tall_detail(RequestInterface $request){
        $search = $request->all();
        //获取详情信息
        $no_tally_where = [
            'id' => $search['id']
        ];
        //获取无需理货记录详情
        $no_tally_detail = $this->AbnormalOutStockService->noTallyDetailOne($no_tally_where, ['*']);
        if (empty($no_tally_detail)){
            return $this->returnApi( ErrorCode::SERVER_ERROR, '无需理货记录不存在' );
        }
        if ($no_tally_detail['status'] == -1){
            return $this->returnApi( ErrorCode::SERVER_ERROR, '无需理货记录已作废' );
        }

        //错下架详情
        $out_stock_detail_where = [
            'detail_id' => $no_tally_detail['abnormal_out_stock_detail_id']
        ];
        $abnormal_out_stock_detail = $this->AbnormalOutStockService->detailOne($out_stock_detail_where, ['*']);

        //获取错下架单据
        $abnormal_out_stock_info = $this->AbnormalOutStockService->info(['id' => $abnormal_out_stock_detail['abnormal_out_stock_id']]);
        if (empty($abnormal_out_stock_info)){
            return $this->returnApi( ErrorCode::SERVER_ERROR, '错下架单据不存在' );
        }
        $modify_info_data = [
            'no_tally_num' => (($abnormal_out_stock_info['no_tally_num']-$no_tally_detail['no_tally_num']) >= 0) ? ($abnormal_out_stock_info['no_tally_num']-$no_tally_detail['no_tally_num']) : 0
        ];
        if ( (($abnormal_out_stock_info['no_tally_num']-$no_tally_detail['no_tally_num']) + $abnormal_out_stock_info['tally_num']) > 0 ){ //错下架数量+理货数量 > 0
            $modify_info_data['status'] = 2;
        }else{
            $modify_info_data['status'] = 1;
        }
        $modify_info_where = [
            'id' => $abnormal_out_stock_detail['abnormal_out_stock_id']
        ];

        //需要修改的错下架详情
        $modify_detail_data = [
            'no_tally_num' => (($abnormal_out_stock_detail['no_tally_num']-$no_tally_detail['no_tally_num']) >= 0) ? ($abnormal_out_stock_detail['no_tally_num']-$no_tally_detail['no_tally_num']) : 0
        ];
        if ( (($abnormal_out_stock_detail['no_tally_num']-$no_tally_detail['no_tally_num']) + $abnormal_out_stock_detail['tally_num']) > 0 ){ //错下架数量+理货数量 > 0
            $modify_detail_data['status'] = 2;
        }else{
            $modify_detail_data['status'] = 1;
        }
        $modify_detail_where = [
            'id' => $no_tally_detail['abnormal_out_stock_detail_id']
        ];

        $data = [
            'modify_detail_where' => $modify_detail_where,
            'modify_detail_data' => $modify_detail_data,
            'cancel_no_tally_where' => $no_tally_where,
            'modify_info_data' => $modify_info_data,
            'modify_info_where' => $modify_info_where
        ];
        try {
            $this->AbnormalOutStockService->cancelNoTallyDetail($data);
        } catch (\Exception $exception) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, $exception->getMessage() );
        }

        return $this->returnApi( ResponseCode::SUCCESS, '成功', [] );
    }

    /**
     * 作废无需理货记录
     * @RequestMapping(path="/abnormalOutStock/check_abnormal", methods="get,post")
     * @return mixed
     */
    public function check_abnormal(RequestInterface $request){
        $search = $request->all();

        //获取错下架单据
        $abnormal_out_stock_info = $this->AbnormalOutStockService->info(['id' => $search['abnormal_out_stock_id']]);
        if (empty($abnormal_out_stock_info)){
            return $this->returnApi( ErrorCode::SERVER_ERROR, '错下架单据不存在' );
        }
        if ($abnormal_out_stock_info['status'] == 3){
            return $this->returnApi( ErrorCode::SERVER_ERROR, '错下架单据已处理完毕，无法再处理' );
        }

        $redis_key = 'batch_operation_data' . $search['abnormal_out_stock_id'];
        //判断key是否存在
        if ($this->HashService->keyIsExist($redis_key)) {
            $this->HashService->del($redis_key);
        }

        //处理传上来的数据
        $file = $request->file('file');
        $filePathName = $file->getPathname();
        $open = fopen($filePathName, "r") or $this->returnApi(ResponseCode::SERVER_ERROR, '打开文件出错');
        $import_data = fread($open, filesize($filePathName));
        $import_row_data = array_filter(explode("@", str_replace(["\r\n", "\r", "\n", "\t", "\s"], "@", $import_data)));
        $file_info = [];
        $error_info = [];
        foreach ($import_row_data as $row => $row_data) {
            $row_line = explode(',', $row_data);
            $shelf_code = trim($row_line[0]);
            $code = trim($row_line[1]);
            if (isset($file_info[$shelf_code.'_'.$code])){//检测数据是否重复
                $error_info[]['msg'] = '第'. ($row+1) . '行重复，货架号：'. $shelf_code.'，货品编码：'. $code.'，请重新填写';
            }else{
                if (!is_numeric(trim($row_line[2]))){
                    $error_info[]['msg'] = '第'. ($row+1) . '行数量类型不符，请重新填写';
                }else{
                    //将不重复的数据写入待检测数据中
                    $file_info[$shelf_code.'_'.$code] = [
                        'row' => $row+1,
                        'shelf_code' => $shelf_code,
                        'code' => $code,
                        'num' => trim($row_line[2])
                    ];
                }
            }
        }

        //获取错下架详情信息
        $abnormal_out_stock_detail = $this->AbnormalOutStockService->detail(['abnormal_out_stock_id' => $search['abnormal_out_stock_id']]);
        if (empty($abnormal_out_stock_detail['data'])){
            return $this->returnApi( ErrorCode::SERVER_ERROR, '错下架数据不存在' );
        }
        $abnormal_out_stock_detail_data = [];
        foreach ($abnormal_out_stock_detail['data'] as $detail_item){
            $abnormal_out_stock_detail_data[$detail_item['shelf_code'].'_'.$detail_item['code']] = $detail_item;
        }

        //检测货品编码是否存在错下架中
        foreach ($file_info as $key => $item){
            if (!array_key_exists($key, $abnormal_out_stock_detail_data)){
                $error_info[]['msg'] = '第'. $item['row'] . '行数据不在错下架，请重新填写';
            }elseif($item['num'] > ($abnormal_out_stock_detail_data[$key]['out_num'] - $abnormal_out_stock_detail_data[$key]['no_tally_num'] - $abnormal_out_stock_detail_data[$key]['tally_num'])){
                $error_info[]['msg'] = '第'. $item['row'] . '行 申请数量＞剩余数量，请重新填写';
            }elseif($abnormal_out_stock_detail_data[$key]['status'] == 3 ){
                $error_info[]['msg'] = '第'. $item['row'] . '行 已完成，无需再处理';
            }
        }
        if (empty($error_info)){
            $this->HashService->saveData($redis_key, $file_info, 86400);
        }

        return $this->returnApi('200', '操作成功', $error_info);
    }

    /**
     * 无需理货 批量
     * @RequestMapping(path="/abnormalOutStock/batch_opetion_no_tally_num", methods="get,post")
     * @return mixed
     */
    public function batch_opetion_no_tally_num(RequestInterface $request){
        $search = $request->all();
        $redis_key = 'batch_operation_data' . $search['abnormal_out_stock_id'];
        $not_tally_data = $this->HashService->getDataAllFromHash($redis_key, true);

        //获取错下架单据
        $abnormal_out_stock_info = $this->AbnormalOutStockService->info(['id' => $search['abnormal_out_stock_id']]);
        if (empty($abnormal_out_stock_info)){
            return $this->returnApi( ErrorCode::SERVER_ERROR, '错下架单据不存在' );
        }
        if ($abnormal_out_stock_info['status'] == 3){
            return $this->returnApi( ErrorCode::SERVER_ERROR, '错下架单据已处理完毕，无法再处理' );
        }

        //错下架单据
        $modify_info_where['id'] = $search['abnormal_out_stock_id'];
        $modify_info_data['no_tally_num'] = $abnormal_out_stock_info['no_tally_num'];

        //获取错下架详情信息
        $abnormal_out_stock_detail = $this->AbnormalOutStockService->detail(['abnormal_out_stock_id' => $search['abnormal_out_stock_id']]);
        if (empty($abnormal_out_stock_detail['data'])){
            return $this->returnApi( ErrorCode::SERVER_ERROR, '错下架数据不存在' );
        }

        $user_info = $this->session->get('userInfo');
        $no_tally_detail = [];//无需理货log
        $modify_detail_list = [];//需要修改的详情数据

        foreach ($abnormal_out_stock_detail['data'] as $item){
            if (isset($not_tally_data[$item['shelf_code'].'_'.$item['code']])){
                $not_tally_info = $not_tally_data[$item['shelf_code'].'_'.$item['code']];

                //错下架单据无需理货数量
                $modify_info_data['no_tally_num'] += $not_tally_info['num'];

                //无需理货详情数据
                $modify_detail_data = [
                    'no_tally_num' => $item['no_tally_num'] + $not_tally_info['num'],
                    'status' => 2
                ];
                if (($not_tally_info['num'] + $item['no_tally_num'] + $item['tally_num']) >= $item['out_num']){// 无需理货数 + 已理货数 = 错下架数据
                    $modify_detail_data['status'] = 3;
                }

                //无需理货条件
                $modify_detail_where = [
                    'id' => $item['id']
                ];

                $modify_detail_list[] = [
                    'modify_detail_where' => $modify_detail_where,
                    'modify_detail_data' => $modify_detail_data
                ];

                //无需理货记录
                $no_tally_detail[] = [
                    "abnormal_out_stock_detail_id" => $item['id'],
                    "code_type" => $item['code_type'],
                    "code" => $item['code'],
                    "shelf_code" => $item['shelf_code'],
                    "reason" => '',
                    "no_tally_num" => $not_tally_info['num'],
                    'admin_id' => $user_info['uid'],
                    'admin_name' => $user_info['nickname'],
                    "status" => 1,
                ];
            }
        }

        //单据状态
        if ($modify_info_data['no_tally_num'] == ($abnormal_out_stock_info['out_num'] - $abnormal_out_stock_info['tally_num'])){
            $modify_info_data['status'] = 3;
        }else{
            $modify_info_data['status'] = 2;
        }

        $data = [
            'modify_info_data' => $modify_info_data,
            'modify_info_where' => $modify_info_where,
            'modify_detail_list' => $modify_detail_list,
            'no_tally_detail' => $no_tally_detail
        ];

        try {
            $this->AbnormalOutStockService->modifyAbnormalOutStockDetail($data);
        } catch (\Exception $exception) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, $exception->getMessage() );
        }

        return $this->returnApi( ResponseCode::SUCCESS, '成功', [] );
    }
}