<?php

namespace App\JsonRpc;

interface SerialNoServiceInterface
{
    public function generate(int $type,int $businessData=0);

    /**
     * 说明：	EPC初始化位数为16位
     * 1、	EPC第1-2位为好超值标签初始化代码【BB】
     * 2、	EPC第3位为预留校验位。默认为【0】
     * 3、	EPC第4位为标签单位标记位
     * 4、	EPC第5-6为标签生产年份，【24】代表2024年，以此类推
     * 5、	EPC第7位为标签类型标记位，0水洗唛，1贴纸，2吊牌，3-8预留，9其他
     * 6、	EPC第8-16位为数字流水码，预计每年度可生成的流水码数量为9.99亿
     * @param int $type
     * @param int $check  检验位
     * @param int $companySign 单位标记 1思创理德 2保点（上海）贸易有限公司 3艾利（广州）包装系统产品有限公司 9好超值
     * @param int $epcType 标签类型 0水洗唛 1贴纸 2吊牌 9其他
     * @param int $num 生成epc的数量
     */
    public function generateEPC(int $type, int $check, int $companySign, int $epcType, int $num );
}