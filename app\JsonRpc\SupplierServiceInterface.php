<?php
declare(strict_types=1);

namespace App\JsonRpc;


/**
 * 供应商服务消费者
 */
Interface  SupplierServiceInterface
{
    public function addSupplier(array $data);

    public function editSupplier(array $data, int $id);

    public function deleteSupplier(int $id);

    public function getSupplierOne(int $id);

    public function getSupplierList(int $page = 1, int $limit = 10, array $where = [], array $field = []);

    // 查多个
    public function getSuppliers(array $where = [], array $filed = ['*']);
}