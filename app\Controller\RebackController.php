<?php
declare(strict_types=1);

namespace App\Controller;

use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\BrandServiceInterface;
use App\JsonRpc\CategoryServiceInterface;
use App\JsonRpc\OrderServiceInterface;
use App\JsonRpc\ProduceAreaServiceInterface;
use App\JsonRpc\RebackServiceInterface;
use App\JsonRpc\ShelfServiceInterface;
use App\JsonRpc\SkuServiceInterface;
use App\JsonRpc\SpuServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;

/**
 * @Controller()
 */
class RebackController extends AbstractController
{
    /**
     * @Inject()
     * @var ValidatorFactoryInterface
     */
    private $validator;
    /**
     * @Inject()
     * @var WarehouseServiceInterface
     */
    private $WarehouseService;
    /**
     * @Inject()
     * @var ShelfServiceInterface
     */
    private $ShelfService;
    /**
     * @Inject()
     * @var ProduceAreaServiceInterface
     */
    private $ProduceAreaService;
    /**
     * @Inject()
     * @var RebackServiceInterface
     */
    private $RebackService;
    /**
     * @Inject()
     * @var SkuServiceInterface
     */
    private $SkuService;
    /**
     * @Inject()
     * @var SpuServiceInterface
     */
    private $SpuService;
    /**
     * @Inject()
     * @var CategoryServiceInterface
     */
    private $CategoryService;
    /**
     * @Inject()
     * @var BrandServiceInterface
     */
    private $BrandService;
    /**
     * @Inject()
     * @var AdminServiceInterface
     */
    private $AdminService;
    /**
     * @Inject()
     * @var OrderServiceInterface
     */
    private $OrderService;


    /**
     * 上传
     * @RequestMapping(path="/reback/upload", methods="get,post")
     */
    public function upload()
    {
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->AdminService->organizeWareHouseData($userInfo['uid']);

        if ($this->isAjax()) {
            $params = $this->request->all();
            $rule = [
                'w_id' => ['required', 'integer'],
                'reback_type' => ['required', 'integer', 'between:1,2']
            ];
            $message = [
                'w_id.required' => '请选择仓库',
                'w_id.integer' => '请选择仓库'
            ];
            $errors = $this->validator->make($params, $rule, $message);
            if ($errors->fails()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
            }
            if (!$this->request->hasFile('file')) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '请导入文件');
            }

            $file = $this->request->file('file');

            // 校验表头
            $configHeader = getTemplateInfo('reback', $params['reback_type']);
            if (!$configHeader) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '表头不正确');
            }

            // 读取数据
            $importData = readExcelByHeader($file, $configHeader['original_data']);
            if (!$importData) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '导入数据不能为空');
            }

            // 校验导入数据
            $extend = ['admin_id' => $userInfo['uid'], 'admin_name' => $userInfo['nickname']];
            $checkRes = $this->checkImportData($params['reback_type'], $importData, $params['w_id'], $extend);
            if ($checkRes['code'] != 0) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $this->returnError($checkRes['error']));
            }
            // 添加
            try {
                $this->RebackService->create(
                    $checkRes['data']['reback'],
                    $checkRes['data']['detail'],
                    (int)$checkRes['data']['sign_type'],
                    $checkRes['data']['produce_data']
                );
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }

            return $this->returnApi(ResponseCode::SUCCESS, '创建成功');
        }

        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses(['ids' => $userWIds, 'status' => PublicCode::warehouse_status_valid], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        // 回库方式
        $reback_type = PublicCode::reback_type;

        return $this->show('reback/upload', [
            'warehouse_list' => $warehouse,
            'reback_type' => $reback_type
        ]);
    }

    private function checkImportData($type, $data, $w_id, $extend)
    {
        // 第一步：检查数据格式
        $formatError = [];

        $uniqueCodeData = [];// unique_code数组
        $barcodeData = [];// source_order_no+barcode=>总数量
        foreach ($data as $key => $val) {
            $val['row_num'] = $key;
            // 检查 货架号格式
            if (strlen($val['shelf_code']) > 40) {
                $formatError[$val['row_num']][] = "{$val['shelf_code']}货架号长度超过40个字符了";
            }
            $item = explode('-', $val['shelf_code']);
            if (!in_array(count($item), [3, 4])) {
                $formatError[$val['row_num']][] = "{$val['shelf_code']}货架号格式错误";
            }
            // 检查 店内码 格式、是否重复
            if ($type == 1) {
                $res = checkUniqueCodeFormat($val['unique_code']);
                if ($res) {
                    $formatError[$val['row_num']][] = $val['unique_code'] . $res;
                }
                if (in_array($val['unique_code'], $uniqueCodeData)) {
                    $formatError[$val['row_num']][] = "{$val['unique_code']}店内码重复";
                } else {
                    $uniqueCodeData[] = $val['unique_code'];
                }
            }
            // 检查 数量格式
            if ($type == 2) {
                if (!preg_match('/^[1-9][0-9]*$/', $val['num'])) {
                    $formatError[$val['row_num']][] = "{$val['num']}数量值必须为正整数";
                }
                if (!isset($barcodeData[$val['source_order_no']][$val['barcode']]['num'])) {
                    $barcodeData[$val['source_order_no']][$val['barcode']]['num'] = $val['num'];
                } else {
                    $barcodeData[$val['source_order_no']][$val['barcode']]['num'] += $val['num'];
                }
            }
            // 检查 员工号格式
            if (!preg_match('/^[1-9][0-9]*$/', $val['admin_id'])) {
                $formatError[$val['row_num']][] = "{$val['admin_id']}员工号必须为正整数";
            }
        }
        if ($formatError) {
            return ['code' => -1, 'error' => $formatError];
        }

        // 第二步：检查数据库
        $databaseError = [];

        // 获取生效的货架号
        $shelf_codes = array_column($data, 'shelf_code');
        $shelfCodeList = $this->ShelfService->getShelfS(['w_id' => $w_id, 'shelf_codes' => $shelf_codes, 'status' => PublicCode::shelf_status_valid], ['shelf_code']);
        $hasShelfCode = $shelfCodeList ? array_column($shelfCodeList, 'shelf_code') : [];

        // 获取生产区的未完成的 店内码
        $uniqueCodeSource = [];// unique_code=>source_type,source_order_no
        if ($type == 1) {
            $unique_codes = array_unique(array_column($data, 'unique_code'));
            $uniqueCodeList = $this->ProduceAreaService->getDetailS(['w_id' => $w_id, 'sign_type' => 1, 'unique_codes' => $unique_codes, 'status' => PublicCode::produce_area_status_no_finish], ['id', 'unique_code', 'source_type', 'source_order_no', 'barcode', 'sku_id', 'spu_id', 'brand_id', 'category_id']);
            $hasUniqueCode = collect($uniqueCodeList)->groupBy('unique_code')->toArray();
            logger()->info("reback:{$w_id}:{$extend['admin_id']}", ['hasUniqueCode' => $hasUniqueCode]);
        }

        // 获取员工号
        $hasAdminIds = array_column($this->AdminService->allIds(), 'id');

        // 获取生产区的未完成的 来源订单号+条形码
        if ($type == 2) {
            $barcodes = array_column($data, 'barcode');
            $source_order_nos = array_column($data, 'source_order_no');
            $barcodeList = $this->ProduceAreaService->getDetailS(['w_id' => $w_id, 'sign_type' => 2, 'source_order_nos' => $source_order_nos, 'barcodes' => $barcodes, 'status' => PublicCode::produce_area_status_no_finish]);
            $hasBarcode = collect($barcodeList)->groupBy(['source_order_no', 'barcode'])->toArray();
            logger()->info("reback:{$w_id}:{$extend['admin_id']}", ['hasBarcode' => $hasBarcode]);
            logger()->info("reback:{$w_id}:{$extend['admin_id']}", ['barcodeData' => $barcodeData]);
        }

        foreach ($data as $key => $val) {
            $val['row_num'] = $key;
            // 检查货架号是否存在
            if (!in_array($val['shelf_code'], $hasShelfCode)) {
                $databaseError[$val['row_num']][] = "{$val['shelf_code']}货架号不存在";
            }
            // 检查店内码是否在生产区
            if ($type == 1) {
                if (!isset($hasUniqueCode[$val['unique_code']])) {
                    $databaseError[$val['row_num']][] = "{$val['unique_code']}店内码不在此仓库的生产区";
                } else {
                    $uniqueCodeSource[$val['unique_code']] = [
                        'unique_code' => $val['unique_code'],
                        'id' => $hasUniqueCode[$val['unique_code']][0]['id'],
                        'source_type' => $hasUniqueCode[$val['unique_code']][0]['source_type'],
                        'source_order_no' => $hasUniqueCode[$val['unique_code']][0]['source_order_no'],

                        'barcode' => $hasUniqueCode[$val['unique_code']][0]['barcode'],
                        'sku_id' => $hasUniqueCode[$val['unique_code']][0]['sku_id'],
                        'spu_id' => $hasUniqueCode[$val['unique_code']][0]['spu_id'],
                        'brand_id' => $hasUniqueCode[$val['unique_code']][0]['brand_id'],
                        'category_id' => $hasUniqueCode[$val['unique_code']][0]['category_id'],
                    ];
                }
            }
            // 检查 来源单号+条形码 是否在生产区、未完成数量是不是超额
            if ($type == 2) {
                if (!isset($hasBarcode[$val['source_order_no']][$val['barcode']])) {
                    $databaseError[$val['row_num']][] = "来源单号（{$val['source_order_no']}）的条形码（{$val['barcode']}）不在生产区";
                } else {
                    $importNum = $barcodeData[$val['source_order_no']][$val['barcode']]['num'];
                    $produceArea = $hasBarcode[$val['source_order_no']][$val['barcode']];
                    $noFinishNum = array_sum(array_column($produceArea, 'surplus_num'));
                    if ($importNum > $noFinishNum) {
                        $databaseError[$val['row_num']][] = '本次导入的数量总和大于生产区未完成的数量';
                    }
                }
            }
            // 检查员工号是否存在
            if (!in_array($val['admin_id'], $hasAdminIds)) {
                $databaseError[$val['row_num']][] = "{$val['admin_id']}员工号不存在";
            }
        }
        if ($databaseError) {
            return ['code' => -1, 'error' => $databaseError];
        }
        // 条码回库-匹配生产区数据
        if ($type == 2) {
            $barcodeSource = $this->ProduceAreaService->getMatchBarcodeData(1, $barcodeData, $hasBarcode);
            logger()->info("reback:{$w_id}:{$extend['admin_id']}", ['barcodeSource' => $barcodeSource]);
        }

        // 获取员工号所对应的名称
        $adminList = $this->AdminService->idsToNameList(array_column($data, 'admin_id'));

        // 组合存表数据
        $detail = [];// 回库详情
        foreach ($data as $val) {
            if ($type == 1) {
                $item = [
                    'source_type' => $uniqueCodeSource[$val['unique_code']]['source_type'],
                    'source_order_no' => $uniqueCodeSource[$val['unique_code']]['source_order_no'],
                    'unique_code' => $val['unique_code'],
                    'barcode' => $uniqueCodeSource[$val['unique_code']]['barcode'],
                    'num' => 1,
                    'shelf_code' => $val['shelf_code'],
                    'admin_id' => $val['admin_id'],
                    'admin_name' => $adminList[$val['admin_id']],

                    'sku_id' => $uniqueCodeSource[$val['unique_code']]['sku_id'],
                    'spu_id' => $uniqueCodeSource[$val['unique_code']]['spu_id'],
                    'brand_id' => $uniqueCodeSource[$val['unique_code']]['brand_id'],
                    'category_id' => $uniqueCodeSource[$val['unique_code']]['category_id'],
                ];
            } else {
                $item = [
                    'source_type' => $hasBarcode[$val['source_order_no']][$val['barcode']][0]['source_type'],
                    'source_order_no' => $val['source_order_no'],
                    'barcode' => $val['barcode'],
                    'num' => $val['num'],
                    'shelf_code' => $val['shelf_code'],
                    'admin_id' => $val['admin_id'],
                    'admin_name' => $adminList[$val['admin_id']],

                    'sku_id' => $hasBarcode[$val['source_order_no']][$val['barcode']][0]['sku_id'],
                    'spu_id' => $hasBarcode[$val['source_order_no']][$val['barcode']][0]['spu_id'],
                    'brand_id' => $hasBarcode[$val['source_order_no']][$val['barcode']][0]['brand_id'],
                    'category_id' => $hasBarcode[$val['source_order_no']][$val['barcode']][0]['category_id'],
                ];
            }
            $detail[] = $item;
        }
        $reback = [ //回库基本信息
            'w_id' => $w_id,
            'batch_code' => date('Ymd') . '-' . $w_id . '-' . mt_rand(1000, 9999),
            'type' => $type,
            'num' => array_sum(array_column($detail, 'num')),
            'admin_id' => $extend['admin_id'],
            'admin_name' => $extend['admin_name'],
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return [
            'code' => 0,
            'data' => [
                'reback' => $reback,
                'detail' => $detail,
                'sign_type' => $type,
                'produce_data' => $type == 1 ? $uniqueCodeSource : $barcodeSource
            ]
        ];
    }

    private function returnError(array $error)
    {
        foreach ($error as $key => $value) {
            unset($error[$key]);
            $error[] = '第' . $key . '行：' . implode('；', $value);
        }
        return implode("<br/>", $error);
    }

    /**
     * 列表
     * @RequestMapping(path="/reback/list", methods="get,post")
     */
    public function list()
    {
        // 用户
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->AdminService->organizeWareHouseData($userInfo['uid']);
        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        // 货品方式
        $type = PublicCode::reback_type;

        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            $search['w_ids'] = $userWIds;
            $export = $params['export'] ?? 0;// 0列表 1导出

            $list = $this->RebackService->list($export, (int)$page, (int)$pageLimit, $search);
            if ($list['data']) {
                foreach ($list['data'] as &$item) {
                    $item['w_name'] = $warehouse[$item['w_id']];
                    $item['type'] = $type[$item['type']];
                }
            }

            if ($export) {
                if (!$list['data']) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
                }
                $url = exportToExcel($this->exportListHeader(), $list['data'], '回库记录');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
        }

        return $this->show('reback/list', [
            'warehouse_list' => $warehouse,
            'type' => $type
        ]);
    }

    private function exportListHeader()
    {
        return [
            'batch_code' => '批次号',
            'w_name' => '仓库',
            'type' => '回库方式',
            'num' => '数量',
            'admin_name' => '回库人',
            'created_at' => '创建时间'
        ];
    }

    /**
     * 列表 - 导出明细
     * @RequestMapping(path="/reback/detailExport", methods="get,post")
     */
    public function detailExport()
    {
        // 用户
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->AdminService->organizeWareHouseData($userInfo['uid']);

        if ($this->isAjax()) {

            $params = $this->request->all();
            $search = $params['search'] ?? [];
            $search['w_ids'] = $userWIds;

            if ($search['start_time']) {
                if ($search['start_time'] < date("Y-m-d", strtotime("-1 month"))) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '只支持一个月内的数据');
                }
            } else {
                $search['start_time'] = date("Y-m-d", strtotime("-1 month"));
                $search['end_time'] = date("Y-m-d H:i:s");
            }

            $list = $this->RebackService->detailExport($search);
            if ($list['data']) {
                $url = exportToExcel($this->exportDetailHeader(), $list['data'], '回库记录明细');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }

            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    private function exportDetailHeader()
    {
        return [
            'batch_code' => '批次号',
            'w_name' => '仓库',
            'type' => '回库方式',
            'source_order_no' => '来源单号',
            'unique_code' => '店内码',
            'barcode' => '条形码',
            'num' => '数量',
            'brand_name' => '品牌',
            'category_name' => '分类',
            'admin_name' => '回库人',
            'created_at' => '创建时间'
        ];
    }

    /**
     * 明细
     * @RequestMapping(path="/reback/detail", methods="get,post")
     */
    public function detail()
    {
        $reback = $this->RebackService->getReback(['id' => $this->request->input('reback_id')]);
        if (!$reback) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '回库记录不存在');
        }

        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            $search = array_merge($search, ['reback_id' => $reback['id']]);

            $list = $this->RebackService->detailList(0, (int)$page, (int)$pageLimit, $search);

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
        }

        $warehouse = $this->WarehouseService->getWarehouse(['id' => $reback['w_id']], ['name']);
        $reback['w_name'] = $warehouse['name'] ?? '';
        $reback['type'] = PublicCode::reback_type[$reback['type']];

        return $this->show('reback/detail', [
            'reback' => $reback
        ]);
    }
}