<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * EpcService
 * Interface EpcServiceInterface
 * @package App\JsonRpc
 */
interface EpcServiceInterface
{
    /**
     * 查单个 epc
     * @param array $where
     * @param array|string[] $filed
     * @return \Hyperf\Database\Model\Builder|\Hyperf\Database\Model\Model|object|null
     */
    public function getEpcCode(array $where, array $filed = ['*']);

    /**
     * 获取epc码信息
     * @param array $where
     * @param array|string[] $filed
     * @return mixed
     */
    public function getEpcCodeS(array $where = [], array $filed = ['*']);

    /**
     * 日志列表
     * @param int $export
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return array
     */
    public function logList(int $export, int $page, int $pageLimit, array $search);

    /**
     * 解绑epc码
     * @param array $data
     * @return bool
     */
    public function unboundEpc(array $data);

    /**
     * 获取店内码绑定epc的数据
     * @param array $where
     * @param array|string[] $filed
     */
    public function getCodes(array $where = [], array $filed = ['*']);

    /**
     * 检测epc是否在磁扣池中
     */
    public function checkEpc(string $epc_code);
}