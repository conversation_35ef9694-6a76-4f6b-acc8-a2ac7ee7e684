<?php
declare(strict_types=1);

namespace App\JsonRpc;

Interface ShelfServiceInterface
{
    /**
     * 查多个货架号
     * @param array $where
     * @param array|string[] $filed
     * @return array
     */
    public function getShelfS(array $where = [], array $filed = ['*']);

    /**
     * 货架号 - 批量添加
     * @param int $w_id
     * @param int $type
     * @param array $data
     * @param array $extend
     * @return bool
     */
    public function batchCreate(int $w_id, int $type, array $data, array $extend);

    /**
     * 货架号 - 列表
     * @param int $export
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return mixed
     */
    public function list(int $export, int $page, int $pageLimit, array $search);

    /**
     * 货架号 - 批量更新
     * @param array $shelf_ids
     * @param array $data
     * @return int
     */
    public function update(array $shelf_ids, array $data);

    /**
     * 获取货架号的库存数量
     * @param int $w_id
     * @param string $shelf_code
     * @return \Hyperf\Utils\HigherOrderTapProxy|mixed|void|null
     */
    public function getShelfStockNum(int $w_id, string $shelf_code);

    /**
     * 批量校验货架号是否存在
     * @param string $data 货架号
     */
    public function checkShelfCodes(array $data);

    /**
     * 根据仓库ID获取所有货架排
     * @param $wId
     */
    public function getShelfLinesByWId($wId,$where = []);

    /**
     * 获取wms库存列表
     * @param array $where
     * @param array|string[] $filed
     * @return array
     */
    public function getShelfStockList(array $where, array $filed = ['*']);

    /**
     * 货架占用查询
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return mixed
     */
    public function occupy(int $page, int $pageLimit, array $search);
}