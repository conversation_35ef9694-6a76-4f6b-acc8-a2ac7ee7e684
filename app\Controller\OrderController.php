<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Controller;

use App\Exception\BusinessException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use App\Constants\ResponseCode;
use App\Constants\PublicCode;
use App\Library\Facades\AdminService;
use App\JsonRpc\OrderDeliveryServiceInterface;
use function Composer\Autoload\includeFile;

/**
 * @Controller()
 */
class OrderController extends AbstractController
{

    /**
     * @Inject()
     * @var \App\JsonRpc\WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject()
     * @var \App\JsonRpc\OrderServiceInterface
     */
    private $OrderService;

    /**
     * @Inject()
     * @var \App\JsonRpc\OrderLogisticsServiceInterface
     */
    private $OrderLogisticsService;

    /**
     * @Inject()
     * @var \App\JsonRpc\UniqueCodeServiceInterface
     */
    private $UniqueCodeService;

    /**
     * @Inject()
     * @var \App\Service\HashService
     */
    private $HashService;

    /**
     * @Inject()
     * @var \App\JsonRpc\LogisticsServiceInterface
     */
    private $LogisticsService;

    /**
     * @Inject()
     * @var OrderDeliveryServiceInterface
     */
    private $OrderDeliveryService;

    /**
     * @Inject()
     * @var \App\JsonRpc\OrderExpressApiLogServiceInterface
     */
    private $OrderExpressApiLogService;

    //订单类型
    private $order_pick_type = PublicCode::order_pick_type;
    //发货类型
    private $order_delivery_type = PublicCode::order_delivery_type;
    //操作环节
    private $order_current_generation = PublicCode::order_current_generation;
    //订单发货状态
    private $order_delivery_status = PublicCode::order_delivery_status;
    //订单异常
    private $order_exception_status = PublicCode::order_exception_status;
    //订单货品发货状态
    private $order_detail_delivery_status = PublicCode::order_detail_delivery_status;
    //货品异常
    private $order_detail_exception_status = PublicCode::order_detail_exception_status;
    //订单来源
    private $order_source = PublicCode::order_source;

    /**
     * @RequestMapping(path="/order/list", methods="get,post")
     * @return mixed
     */
    public function list()
    {
        $data['title'] = "订单列表";

        //获取仓库信息
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $warehouse_list = $this->WarehouseService->getWarehouses(['ids' => $wIds, 'type' => 1], ['id', 'name']);
        $data['warehouse_list'] = $warehouse_list;
        $warehouse_info = [];
        if (!empty($warehouse_list)) {
            $warehouse_info = array_column($warehouse_list, 'name', 'id');
        }
        //订单类型
        $data['order_pick_type'] = $this->order_pick_type;
        //发货类型
        $data['delivery_type'] = $this->order_delivery_type;
        //操作环节
        $data['order_current_generation'] = $this->order_current_generation;
        //订单发货状态
        $data['order_delivery_status'] = $this->order_delivery_status;
        //订单异常
        $data['order_exception_status'] = $this->order_exception_status;
        //订单商品发货状态
        $data['order_detail_delivery_status'] = $this->order_detail_delivery_status;
        //订单商品货品异常
        $data['order_detail_exception_status'] = $this->order_detail_exception_status;
        //订单开始结束时间
        $data['date'] = date('Y-m-d', strtotime('-90 days')).' - '.date('Y-m-d');

        if ($this->isAjax()) {
            $where = $this->request->all();
            $where['page_size'] = $where['limit'] ?? $this->pageLimit();
            $where['export'] = isset($where['export']) ? $where['export'] : 0;
            $order_where = [];
            $order_where['is_pay'] = 1;
            $order_where['take_goods_type'] = 0;
            //仓库
            if (!isset($where['warehouse']) || empty($where['warehouse'])) {
                $order_where['w_ids'] = $wIds;
            } else {
                $order_where['w_ids'] = [$where['warehouse']];
            }
            //创建时间
            if (isset($where['date']) && !empty($where['date'])) {
                $date_time = explode(' - ', $where['date']);
                $order_where['start_created_at'] = $date_time[0] . ' 00:00:00';
                $order_where['end_created_at'] = $date_time[1] . ' 23:59:59';
            }
            //操作环节
            if (isset($where['order_current_generation']) && !empty($where['order_current_generation'])) {
                $order_where['current_generation'] = $where['order_current_generation'];
            }
            //订单类型
            if (isset($where['order_pick_type']) && !empty($where['order_pick_type'])) {
                $order_where['order_pick_type'] = $where['order_pick_type'];
            }
            //发货类型
            if (isset($where['delivery_type']) && !empty($where['delivery_type'])) {
                $order_where['delivery_type'] = $where['delivery_type'];
            }
            //订单发货状态
            if (isset($where['order_delivery_status']) && !empty($where['order_delivery_status'])) {
                $order_where['order_delivery_status'] = $where['order_delivery_status'];
            }
            //订单异常
            if (isset($where['order_exception_status']) && !empty($where['order_exception_status'])) {
                $order_where['order_exception_status'] = $where['order_exception_status'];
            }
            //订单货品发货状态
            if (isset($where['order_detail_delivery_status']) && !empty($where['order_detail_delivery_status'])) {
                $order_where['order_detail_delivery_status'] = $where['order_detail_delivery_status'];
            }
            //订单货品异常
            if (isset($where['order_detail_exception_status']) && !empty($where['order_detail_exception_status'])) {
                $order_where['order_detail_exception_status'] = $where['order_detail_exception_status'];
            }
            //订单检索
            if (isset($where['order_check_type']) && !empty($where['order_check_type']) && isset($where['order_check_value']) && !empty($where['order_check_value'])) {
                switch ($where['order_check_type']) {
                    case 'order_code':#订单号
                        $order_where['branch_serial_nos'] = array_filter(array_unique(explode("\n", $where['order_check_value'])));
                        break;
                    case 'unique_code':#店内码
                        $order_where['unique_codes'] = array_filter(array_unique(explode("\n", $where['order_check_value'])));
                        break;
                    case 'express_code':#快递单号
                        $order_where['express_codes'] = array_filter(array_unique(explode("\n", $where['order_check_value'])));
//                        $order_where['join_order_logistics'] = true;
                        break;
                    default:
                        break;
                }
            }

            //按照子订单号分组
            $order_where['group_branch_serial_no'] = true;
            $order_where['get_order_detail'] = true;
            $order_where['order_by_branch_id_desc'] = true;
            if ($where['export'] == 0){
                //获取订单列表
                $order_list = $this->OrderService->getList($order_where, intval($where['page_size']), intval($where['page']));
                $order_data = [];
                foreach ($order_list['list'] as $o_info) {
                    $order_data[] = [
                        'order_id' => $o_info['ob_id'],
                        'order_code' => $o_info['branch_serial_no'],
                        'w_name' => $o_info['w_name'],
                        'type_name' => $this->order_pick_type[$o_info['order_pick_type']] ?? '未知',
                        'delivery_status_name' => $this->order_delivery_status[$o_info['order_delivery_status']] ?? '未知',
                        'product_num' => $o_info['sku_nums'],
                        'delivery_type' => $this->order_delivery_type[$o_info['order_delivery_type']] ?? '未知',
                        'exception_status' => $this->order_exception_status[$o_info['order_exception_status']] ?? '未知',
                        'created_at' => $o_info['created_at']
                    ];
                }
                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $order_data, ['count' => $order_list['total'], 'limit' => $where['page_size'], 'export' => $where['export']]);
            }else{
                $page_id = 1;
                $page_size = 1000;
                $order_data = [];
                while (true){
                    //获取订单列表
                    $order_list = $this->OrderService->getList($order_where, $page_size, $page_id);
                    if (empty($order_list['list'])){
                        break;
                    }
                    foreach ($order_list['list'] as $o_info) {
                        $order_data[] = [
                            'order_code' => $o_info['branch_serial_no'],
                            'w_name' => $o_info['w_name'],
                            'type_name' => $this->order_pick_type[$o_info['order_pick_type']] ?? '未知',
                            'delivery_status_name' => $this->order_delivery_status[$o_info['order_delivery_status']] ?? '未知',
                            'product_num' => $o_info['sku_nums'],
                            'delivery_type' => $this->order_delivery_type[$o_info['order_delivery_type']] ?? '未知',
                            'exception_status' => $this->order_exception_status[$o_info['order_exception_status']] ?? '未知',
                            'created_at' => $o_info['created_at']
                        ];
                    }
                    $page_id++;
                }
                try {
                    $url = exportToExcel(config('file_header.order_export'), $order_data ,'订单列表');
                }catch (\Exception $e){
                    throw new BusinessException('导出失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
                }
                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
            }
        }

        return $this->show('order/list', $data);
    }

    /**
     * @RequestMapping(path="/order/detail/{order_code}",methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function detail()
    {
        $order_code = $this->request->route('order_code');

        $data['title'] = '订单详情';
        //订单商品发货状态
        $data['order_detail_delivery_status'] = $this->order_detail_delivery_status;
        //订单商品货品异常
        $data['order_detail_exception_status'] = $this->order_detail_exception_status;
        //获取订单信息
        $order_info = $this->OrderService->getBranchDetail($order_code);
        if (!empty($order_info)) {
            $data['order_code'] = $order_info['branch_serial_no'];
            $data['w_name'] = $order_info['w_name'];

            $data['created_at'] = $order_info['created_at'];
            $data['source'] = $this->order_source[$order_info['source']] ?? '未知';
            $data['receiving_name'] = $order_info['receiving_name'];
            $data['receiving_phone'] = $order_info['receiving_phone'];
            $data['address_info'] = $order_info['receiving_province'] . $order_info['receiving_city'] . $order_info['receiving_area'] . $order_info['receiving_addr'];
            $data['remark'] = $order_info['remark'] ?? '无';

            $data['delivery_type'] = $this->order_delivery_type[$order_info['order_delivery_type']] ?? '未知';
            $data['order_type'] = $this->order_pick_type[$order_info['order_pick_type']] ?? '未知';
            $data['delivery_status_name'] = $this->order_delivery_status[$order_info['order_delivery_status']] ?? '未知';
            $data['exception_status'] = $this->order_exception_status[$order_info['order_exception_status']] ?? '未知';
            return $this->show('order/detail', $data);
        }
    }

    /**
     * @RequestMapping(path="/order/order_product/{order_code}",methods="get,post")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function order_product()
    {
        $order_code = $this->request->route('order_code');

        $where = $this->request->all();

        if (empty($order_code)) {
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', [], ['count' => 0, 'limit' => 0]);
        }

        //获取订单商品
        $detail_where = [
            'get_order_branch_detail' => true,
            'branch_serial_no' => $order_code
        ];
        if (isset($where['delivery_status']) && !empty($where['delivery_status'])) {
            $detail_where['order_detail_delivery_status'] = $where['delivery_status'];
        }
        if (isset($where['exception_status']) && !empty($where['exception_status'])) {
            $detail_where['order_detail_exception_status'] = $where['exception_status'];
        }

        $order_detail = $this->OrderService->getList($detail_where, intval($where['limit']), intval($where['page']));
        if (empty($order_detail['list'])) {
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', [], ['count' => 0, 'limit' => 0]);
        }
        //获取订单的发货物流
        $order_detail_ids = array_column($order_detail['list'], 'id');
        $logistics_where = [
            'order_detail_ids' => $order_detail_ids,
            'status' => 1
        ];
        $logistics_list = $this->OrderLogisticsService->getLogisticsOrder($logistics_where);
        if (!empty($logistics_list)) {
            $logistics_list = array_combine(array_column($logistics_list, 'order_detail_id'), array_values($logistics_list));
        }

        //获取店内码库存数据
        $sku_ids = array_unique(array_column($order_detail['list'], 'sku_id'));
        $stock_list =  $this->UniqueCodeService->getStocks(['w_id' => $order_detail['list'][0]['w_id'] , 'sku_ids' => $sku_ids, 'status' => 1, 'group_w_id' => true, 'group_sku_id' => true]);
        $stock_map = array_column($stock_list, 'stock_nums', 'sku_id');

        $data = [];
        foreach ($order_detail['list'] as $key => $item) {
            $data[] = [
                'key' => $key + 1,
                'item_id' => $item['id'],
                'image_url' => $item['goods_image'],
                'sku_id' => $item['sku_id'],
                'spu_code' => $item['spu_no'],
                'spec_info' => $item['spec_name'],
                'unique_code' => $item['unique_code'] ?? '',
                'delivery_status_name' => $this->order_detail_delivery_status[$item['delivery_status']] ?? '未知',
                'current_generation_name' => $this->order_current_generation[$item['current_generation']] ?? '未知',
                'exception_status_name' => $this->order_detail_exception_status[$item['exception_status']] ?? '未知',
                'current_generation' => $item['current_generation'],
                'logistics_company_name' => isset($logistics_list[$item['id']]) ? $logistics_list[$item['id']]['logistics_company_name'] : '',
                'logistics_serial_no' => isset($logistics_list[$item['id']]) ? $logistics_list[$item['id']]['logistics_serial_no'] : '',
                'stock_nums' => $stock_map[$item['sku_id']] ?? 0
            ];
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $data, ['count' => $order_detail['total'], 'limit' => $where['limit']]);
    }

    /**
     * 批量修改订单商品项的环节
     * @RequestMapping(path="/order/operate_order_item", methods="get,post")
     */
    public function operate_order_item()
    {
        $params = $this->request->all();
        try {
            //获取订单商品项信息
            $where = [
                'detail_ids' => $params['item_ids'],
                'get_order_branch_detail' => true
            ];
            $order_branch_detail = $this->OrderService->getList($where, count($params['item_ids']), 1);
            if (empty($order_branch_detail['list'])) {
                return $this->returnApi(ResponseCode::SERVER_ERROR, '订单商品项信息不存在');
            }
            //订单商品项修改信息
            $order_detail_info = [];
            //需要释放的店内码
            $unique_codes = [];
            foreach ($order_branch_detail['list'] as $item) {
                if ($item['delivery_status'] != 1) {
                    return $this->returnApi(ResponseCode::SERVER_ERROR, '订单商品项发货状态不符合修改条件');
                }
                $order_detail_info[$item['id']]['where']['id'] = $item['id'];
                switch ($params['operate_type']) {
                    case 'redistribution':#导单-重新分配货品
                        $unique_codes[] = $item['unique_code'];
                        $order_detail_info[$item['id']]['data'] = [
                            'unique_code' => '',
                            'current_generation' => 2,
                            'status' => 1,
                            'exception_status' => 1,
                            'pick_mode' => 1//标记二次拣货
                        ];
                        break;
                    case 'reserved_goods':#导单-保留匹配货品
                        if ($item['current_generation'] != 3) {
                            return $this->returnApi(ResponseCode::SERVER_ERROR, '订单商品项当前操作环节不符合修改条件');
                        }
                        $order_detail_info[$item['id']]['data'] = [
                            'current_generation' => 3
                        ];
                        break;
                    case 'cancel_delivery':#取消发货
                        $unique_codes[] = $item['unique_code'];
                        $order_detail_info[$item['id']]['data'] = [
                            'unique_code' => '',
                            'status' => 1,
                            'current_generation' => -1,
                            'exception_status' => 2
                        ];
                        if (in_array($item['exception_status'], [3, 4, 5])) {#取消发货时，如果异常类型是：3货品匹配失败 4出库失败 5质检不通过  则标记成缺货
                            $order_detail_info[$item['id']]['data']['status'] = 0;
                        }
                        break;
                }
            }
            //需要释放的店内码
            $unique_code_where = [
                'unique_codes' => $unique_codes,
                'status' => 3
            ];
            $unique_code_data = [
                'status' => 1,
                'lock_type' => ''
            ];
            //获取店内码信息
            if (!empty($unique_codes)) {
                $unique_code_list = $this->UniqueCodeService->getUniqueCodes($unique_codes, ['*']);
                //WMS库存信息 需要加的库存数
                $wms_shelf_stock = $stock_list = $stock_sup_list = [];
                if (!empty($unique_code_list)) {
                    foreach ($unique_code_list as $s_item) {
                        if ($s_item['status'] == 3) {
                            $key = $s_item['w_id'] . '_' . $s_item['sku_id'] . $s_item['shelf_code'];
                            if (!isset($wms_shelf_stock[$key])) {
                                $wms_shelf_stock[$key]['where'] = [
                                    'w_id' => $s_item['w_id'],
                                    'sku_id' => $s_item['sku_id'],
                                    'shelf_code' => $s_item['shelf_code']
                                ];
                                $wms_shelf_stock[$key]['back_num'] = 1;
                            } else {
                                $wms_shelf_stock[$key]['back_num'] += 1;
                            }

                            //检测是否为残次， 需要释放残次库存
                            if ($s_item['is_imperfect'] != 0){
                                //sku残次库存
                                if (!isset($stock_list[$s_item['sku_id']][$s_item['w_id']])){
                                    $stock_list[$s_item['sku_id']][$s_item['w_id']] = 0;
                                }
                                $stock_list[$s_item['sku_id']][$s_item['w_id']] += 1;

                                //供应商残次库存
                                if (!isset($stock_sup_list[$s_item['sku_id']][$s_item['w_id']][$s_item['in_stock_no']])){
                                    $stock_sup_list[$s_item['sku_id']][$s_item['w_id']][$s_item['in_stock_no']] = 0;
                                }
                                $stock_sup_list[$s_item['sku_id']][$s_item['w_id']][$s_item['in_stock_no']] += 1;
                            }
                        }
                    }
                }
            }

            //修改订单商品项的操作环节
            if (!empty($order_detail_info)) {
                $res = $this->OrderService->editOrderBranchDetail($order_detail_info);
                if ($res) {
                    //解锁店内码和恢复库存
                    $stock_params = [
                        'unique_code_where' => $unique_code_where,
                        'unique_code_data' => $unique_code_data,
                        'shelf_stock_where' => $wms_shelf_stock,
                        'stock_list' => $stock_list,
                        'stock_sup_list' => $stock_sup_list
                    ];
                    $res = $this->UniqueCodeService->releaseStock($stock_params);
                    if ($res && !empty($unique_codes)) {
                        //用户信息
                        $user_info = $this->session->get('userInfo');
                        foreach ($unique_codes as $unique_code) {
                            $uqLineLog = [
                                'unique_code' => $unique_code,
                                'operation_type' => PublicCode::UNIQUE_CODE_LOG_TYPE_MAP['sale_unlock'], // 类型为4（销售解锁）
                                'admin_id' => $user_info['uid'],
                                'admin_name' => $user_info['nickname'],
                                'operation_desc' => "WMS订单明细-" . PublicCode::ORDER_OPERATION_TYPE[$params['operate_type']],
                                'operation_time' => currentTime(),
                                'snow_id' => generateSnowId()
                            ];
                            addUniqueCodeLog($uqLineLog['snow_id'], $uqLineLog);
                        }
                    }
                }
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, '操作失败');
        }
    }


    /**
     * 批量修改订单商品项的环节
     * @RequestMapping(path="/order/operate_order", methods="get,post")
     */
    public function operate_order()
    {
        $params = $this->request->all();

        if (empty($params['operate_type'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '操作类型不能为空');
        }
        if (empty($params['order_ids'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '未选择需要修改的订单');
        }

        $exception_status = 0;
        switch ($params['operate_type']) {
            case "intercept_shipment":#拦截发货
                $exception_status = 2;
                break;
            case "unblock_shipment":#解除拦截发货
                $exception_status = 1;
                break;
            case "remove_exception":#解除生产异常
                $exception_status = 1;
                break;
        }
        if (empty($exception_status)) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '操作异常状态不能为空');
        }

        $data['exception_status'] = $exception_status;
        $where['order_ids'] = $params['order_ids'];

        $res = $this->OrderService->editOrderBranchInfo(['where' => $where, 'data' => $data]);
        if ($res) {
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        } else {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '操作失败');
        }

    }

    /**
     * 制单
     * @RequestMapping(path="/order/make",methods="get,post")
     */
    public function make()
    {
        $data['title'] = "订单制单";

        return $this->show('order/make', $data);
    }

    /**
     * @RequestMapping(path="/order/get_make",methods="get,post")
     */
    public function get_make()
    {
        $params = $this->request->all();

        if (!isset($params['make_type']) || empty($params['make_type'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '扫描类型不能为空');
        }

        //根据店内码制单时获取订单商品项信息
        $order_detail_info = [];
        #制单区数据
        $making_list = [];
        try {
            $where = [
                'group_branch_serial_no' => true,
                'get_order_detail' => true
            ];
            if ($params['make_type'] == 'unique_code') {#店内码
                //检测店内码是否已出库
                $unique_code_info = $this->UniqueCodeService->getUniqueCodes([$params['code']], ['*']);
                if (empty($unique_code_info)) {
                    return $this->returnApi(ResponseCode::SERVER_ERROR, '店内码不存在');
                }
                if ($unique_code_info[0]['status'] != 2) {
                    return $this->returnApi(ResponseCode::SERVER_ERROR, '店内码未出库');
                }

//                $where['unique_code'] = $params['code'];
                //根据店内码获取订单商品项信息
                $detail_where = [
                    'unique_code' => $params['code'],
                    'detail_status' => 2
                ];
                $order_detail_info = $this->OrderService->getOrderDetail($detail_where);
                if (empty($order_detail_info)){
                    return $this->returnApi(ResponseCode::SERVER_ERROR, '此店内码未匹配到订单');
                }
                $where['branch_serial_no'] = $order_detail_info[0]['branch_serial_no'];
            } elseif ($params['make_type'] == 'order_code') {#订单号
                $where['branch_serial_no'] = $params['code'];
            } else {
                return $this->returnApi(ResponseCode::SERVER_ERROR, '扫描类型不符');
            }
            $order_list = $this->OrderService->getList($where);
            if (empty($order_list['list'])) {
                return $this->returnApi(ResponseCode::SERVER_ERROR, '无订单信息');
            }
            $order_info = $order_list['list'][0];

            //订单来源
            $order_source = $this->order_source[$order_info['source']] ?? '未知';

            //缓存key
            $make_order_key = $order_info['branch_serial_no'] . '_make';
            $redis_make_data = [];#缓存数据
            if ($this->HashService->keyIsExist($make_order_key)) {
                $redis_make_data = $this->HashService->getDataAllFromHash($make_order_key, true);
            }

            if (empty($redis_make_data)) {
                //获取快递公司
                $logistics_where = [
                    'company_type' => 1,
                    'company_status' => 1,
                    'status' => 1,
                    'w_id' => $order_info['w_id']
                ];
                $logistics_company = $this->LogisticsService->getLogisticsConfigs($logistics_where);
                $logistics_company_list = [];
                if (!empty($logistics_company)) {
                    foreach ($logistics_company as $c_item) {
                        $logistics_company_list[$c_item['lg_id']] = [
                            'id' => $c_item['lg_id'],
                            'name' => $c_item['name']
                        ];
                    }
                }

                //获取订单商品项信息
                $order_detail_where = [
                    'branch_serial_no' => $order_info['branch_serial_no']
                ];
                $order_detail_list = $this->OrderService->getOrderDetail($order_detail_where);
                if (empty($order_detail_list)) {
                    return $this->returnApi(ResponseCode::SERVER_ERROR, '订单商品项信息不存在');
                }

                //获取已制单数
                $logistics_where = [
                    'branch_serial_no' => $order_info['branch_serial_no'],
                    'status' => 1
                ];
                $logistics_list = $this->OrderLogisticsService->getLogisticsList($logistics_where);

                //订单信息
                $order_branch_info = [
                    'id' => $order_info['ob_id'],
                    'order_code' => $order_info['branch_serial_no'],
                    'source' => $this->order_source[$order_info['source']] ?? '未知',
                    'source_id' => $order_info['source'],
                    'order_delivery_type' => $this->order_delivery_type[$order_info['order_delivery_type']] ?? '未知',
                    'order_exception_status' => $this->order_exception_status[$order_info['order_exception_status']] ?? '未知',
                    'order_pick_type' => $this->order_pick_type[$order_info['order_pick_type']] ?? '未知',
                    'sku_nums' => $order_info['sku_nums'],
                    'receiving_name' => $order_info['receiving_name'],
                    'receiving_phone' => $order_info['receiving_phone'],
                    'receiving_province' => $order_info['receiving_province'],
                    'receiving_city' => $order_info['receiving_city'],
                    'receiving_area' => $order_info['receiving_area'],
                    'receiving_addr' => $order_info['receiving_addr'],
                    'remark' => $order_info['remark'] ?? '无',
                    'maked_nums' => $logistics_list['total'],
                    'not_make_nums' => $order_info['sku_nums'] - $logistics_list['total']
                ];

                //获取待制单的商品数据
                $order_detail_ids = [];
                if (!empty($logistics_list['list'])) {
                    $order_detail_ids = array_column($logistics_list['list'], 'order_detail_id');
                }

                //组装已制单和待制单的数据
                $not_make_list = $maked_list = [];
                foreach ($order_detail_list as $item) {
                    $info = [
                        'id' => $item['id'],
                        'goods_image' => $item['goods_image'] ?? '/static/images/no_pic.png',
                        'unique_code' => $item['unique_code'] ?? '-',
                        'brand_name' => $item['brand_name'] ?? '-',
                        'category_name' => $item['category_name'] ?? '-',
                        'spec_name' => $item['spec_name'] ?? '-'
                    ];
                    if (in_array($item['id'], $order_detail_ids)) {
                        $maked_list[$item['id']] = $info;
                    } else {
                        $not_make_list[$item['id']] = $info;
                    }
                }

                //已制单数据
                $order_logistics_list = [];
                if (!empty($logistics_list['list'])) {
                    foreach ($logistics_list['list'] as $l_item) {
                        if (array_key_exists($l_item['order_detail_id'], $maked_list)) {
                            if (!isset($order_logistics_list[$l_item['logistics_serial_no']])) {
                                $order_logistics_list[$l_item['logistics_serial_no']]['logistics_info'] = [
                                    'logistics_serial_no' => $l_item['logistics_serial_no'],
                                    'id' => $l_item['id'],
                                    'company_name' => $l_item['logistics_company_name']
                                ];
                            }
                            $order_logistics_list[$l_item['logistics_serial_no']]['order_detail_list'][] = $maked_list[$l_item['order_detail_id']];
                        }
                    }
                }
            } else {
                $order_branch_info = $redis_make_data['order_info'];
                $order_logistics_list = $redis_make_data['order_logistics_list'];
                $not_make_list = $redis_make_data['not_make_list'];
                $making_list = $redis_make_data['making_list'];
                $logistics_company_list = $redis_make_data['logistics_company_list'];
            }

            //如果扫描类型是店内码，检测店内码是否已制单
            $error_info = "";
            $is_maked = false;
            if ($params['make_type'] == 'unique_code') {
                foreach ($order_logistics_list as $logistics_info) {
                    foreach ($logistics_info['order_detail_list'] as $logistics_item) {
                        if ($params['code'] == $logistics_item['unique_code']) {
                            $error_info = "订单已制单，请作废发货记录后重新制单";
                            $is_maked = true;
                            break 2;
                        }
                    }
                }
            }

            //制单区,如果未制单则加入制单区
            if (!$is_maked) {
                if (!empty($order_detail_info)) {
                    $order_detail_info = $order_detail_info[0];
                    $making_list[$order_detail_info['unique_code']] = [
                        'id' => $order_detail_info['id'],
                        'goods_image' => $order_detail_info['goods_image'] ?? '/static/images/no_pic.png',
                        'unique_code' => $order_detail_info['unique_code'] ?? '-',
                        'brand_name' => $order_detail_info['brand_name'] ?? '-',
                        'category_name' => $order_detail_info['category_name'] ?? '-',
                        'spec_name' => $order_detail_info['spec_name'] ?? '-'
                    ];
                    unset($not_make_list[$order_detail_info['id']]);
                }
            }

            $data['order_info'] = $order_branch_info;
            $data['making_list'] = $making_list;
            $data['making_nums'] = count($making_list);
            $data['not_make_list'] = $not_make_list;
            $data['not_make_nums'] = count($not_make_list);
            $data['order_logistics_list'] = $order_logistics_list;
            $data['maked_nums'] = count($order_logistics_list);
            $data['logistics_company_list'] = $logistics_company_list;
        }catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
        //将数据写入缓存
        $this->HashService->saveData($make_order_key, $data, 86400);

        $html = $this->getContents('order/makeDetail', $data);

        //检测是否打印面单和小票
        $print_ticket = $print_express = false;
        if (count($not_make_list) == 0 && count($making_list) != 0){
            $print_express = true;
            if ($order_info['source_id'] != 5){
                $print_ticket = true;
            }
        }

        $resul = [
            'html' => $html,
            'order_source' => $order_source,
            'print_ticket' => $print_ticket,
            'print_express' => $print_express
        ];

        if ($is_maked) {
            return $this->returnApi(400, $error_info, $resul);
        } else {
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $resul);
        }
    }

    /**
     * 将店内码加入制单区
     * @RequestMapping(path="/order/add_make",methods="get,post")
     */
    public function add_make()
    {
        $params = $this->request->all();
        if (!isset($params['unique_code']) || empty($params['unique_code'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '参数中的店内码不存在');
        }
        if (count(array_filter($params['unique_code'])) != count($params['unique_code'])){
            return $this->returnApi(ResponseCode::SERVER_ERROR, '选中商品必须为已匹配店内码，请确认后再提交');
        }

        //检测店内码是否已出库
        $unique_code_info = $this->UniqueCodeService->getUniqueCodes($params['unique_code'], ['*']);
        if (empty($unique_code_info)) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '店内码不存在');
        }
        foreach ($unique_code_info as $unique_item) {
            if ($unique_item['status'] != 2) {
                return $this->returnApi(ResponseCode::SERVER_ERROR, '店内码未出库');
            }
        }

        //获取订单信息
        $where = [
            'group_branch_serial_no' => true,
            'get_order_detail' => true,
            'unique_codes' => $params['unique_code'],
            'branch_serial_no' => $params['order_code']
        ];
        $order_list = $this->OrderService->getList($where);
        if (empty($order_list['list'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '无订单信息');
        }
        $order_info = $order_list['list'][0];

        //缓存key，获取缓存中的订单商品数据
        $make_order_key = $order_info['branch_serial_no'] . '_make';
        $redis_make_data = [];#缓存数据
        if ($this->HashService->keyIsExist($make_order_key)) {
            $redis_make_data = $this->HashService->getDataAllFromHash($make_order_key, true);
        }
        if (empty($redis_make_data)) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '未扫描订单的制单数据，请先扫描订单制单数据');
        }

        $order_branch_info = $redis_make_data['order_info'];
        $order_logistics_list = $redis_make_data['order_logistics_list'];
        $not_make_list = $redis_make_data['not_make_list'];
        $making_list = $redis_make_data['making_list'];
        $logistics_company_list = $redis_make_data['logistics_company_list'];

        //根据店内码获取订单商品项信息
        $detail_where = [
            'unique_codes' => $params['unique_code'],
            'branch_serial_no' => $params['order_code']
        ];
        $order_detail_info = $this->OrderService->getOrderDetail($detail_where);
        if (empty($order_detail_info)) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '指定店内码的订单商品项不存在');
        }

        //将选中的待制单数据加入到制单数据中
        foreach ($order_detail_info as $detail_item) {
            $making_list[$detail_item['unique_code']] = [
                'id' => $detail_item['id'],
                'goods_image' => $detail_item['goods_image'] ?? '/static/images/no_pic.png',
                'unique_code' => $detail_item['unique_code'] ?? '-',
                'brand_name' => $detail_item['brand_name'] ?? '-',
                'category_name' => $detail_item['category_name'] ?? '-',
                'spec_name' => $detail_item['spec_name'] ?? '-'
            ];
            unset($not_make_list[$detail_item['id']]);
        }

        $data['order_info'] = $order_branch_info;
        $data['making_list'] = $making_list;
        $data['not_make_list'] = $not_make_list;
        $data['order_logistics_list'] = $order_logistics_list;
        $data['logistics_company_list'] = $logistics_company_list;
        $data['making_nums'] = count($making_list);
        $data['not_make_nums'] = count($not_make_list);
        $data['maked_nums'] = count($order_logistics_list);

        //检测是否打印面单和小票
        $print_ticket = $print_express = false;
        if (count($not_make_list) == 0){
            $print_express = true;
            if ($order_info['source_id'] != 5){
                $print_ticket = true;
            }
        }

        //将数据写入缓存
        $this->HashService->saveData($make_order_key, $data, 86400);
        $html = $this->getContents('order/makeDetail', $data);
        $result = [
            'html' => $html,
            'print_express' => $print_express,
            'print_ticket' => $print_ticket
        ];
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $result);
    }

    /**
     * 删除制单区的货品
     * @RequestMapping(path="/order/del_make", methods="get,post")
     */
    public function del_make()
    {
        $params = $this->request->all();

        if (!isset($params['unique_code']) || empty($params['unique_code'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '店内码参数不能为空');
        }
        if (!isset($params['order_code']) || empty($params['order_code'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '订单号参数不能为空');
        }

        $where = [
            'group_branch_serial_no' => true,
            'get_order_detail' => true,
            'unique_code' => $params['unique_code'],
            'branch_serial_no' => $params['order_code']
        ];
        $order_list = $this->OrderService->getList($where);
        if (empty($order_list['list'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '无订单信息');
        }
        $order_info = $order_list['list'][0];

        //缓存key，获取缓存中的订单商品数据
        $make_order_key = $order_info['branch_serial_no'] . '_make';
        $redis_make_data = [];#缓存数据
        if ($this->HashService->keyIsExist($make_order_key)) {
            $redis_make_data = $this->HashService->getDataAllFromHash($make_order_key, true);
        }
        if (empty($redis_make_data)) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '未扫描订单的制单数据，请先扫描订单制单数据');
        }

        $order_branch_info = $redis_make_data['order_info'];
        $order_logistics_list = $redis_make_data['order_logistics_list'];
        $not_make_list = $redis_make_data['not_make_list'];
        $making_list = $redis_make_data['making_list'];
        $logistics_company_list = $redis_make_data['logistics_company_list'];

        //根据店内码获取订单商品项信息
        $detail_where = [
            'unique_code' => $params['unique_code'],
            'branch_serial_no' => $params['order_code']
        ];
        $order_detail_info = $this->OrderService->getOrderDetail($detail_where);
        if (empty($order_detail_info)) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '指定店内码的订单商品项不存在');
        }

        foreach ($order_detail_info as $item) {
            $not_make_list[$item['id']] = $making_list[$item['unique_code']];
            unset($making_list[$item['unique_code']]);
        }

        $data['order_info'] = $order_branch_info;
        $data['making_list'] = $making_list;
        $data['not_make_list'] = $not_make_list;
        $data['order_logistics_list'] = $order_logistics_list;
        $data['logistics_company_list'] = $logistics_company_list;

        $data['making_nums'] = count($making_list);
        $data['not_make_nums'] = count($not_make_list);
        $data['maked_nums'] = count($order_logistics_list);

        //将数据写入缓存
        $this->HashService->saveData($make_order_key, $data, 86400);
        $html = $this->getContents('order/makeDetail', $data);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $html);
    }

    /**
     * 重置时清空缓存
     * @RequestMapping(path="/order/reset_make",methods="get,post")
     */
    public function reset_make()
    {
        $params = $this->request->all();

        if (!isset($params['order_code']) && empty($params['order_code'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '参数不能为空');
        }

        //缓存key
        $make_order_key = $params['order_code'] . '_make';
        $res = false;
        if ($this->HashService->keyIsExist($make_order_key)) {
            $res = $this->HashService->del($make_order_key);
        }
        if ($res) {
            return $this->returnApi(ResponseCode::SUCCESS, '清空成功');
        } else {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '清空失败');
        }
    }

    /**
     * 取消发货记录
     * @RequestMapping(path="/order/cancel_express", methods="get,post")
     */
    public function cancel_express()
    {
        $params = $this->request->all();
        if (!isset($params['express_code']) && empty($params['express_code'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '参数不能为空');
        }

        //获取快递面单对应的商品项id
        $where = [
            'logistics_serial_no' => $params['express_code']
        ];
        $logistics_list = $this->OrderLogisticsService->getLogisticsOrder($where);
        if (empty($logistics_list)) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '物流信息为空');
        }

        //检测是否已交接
        $delivery_detail_list = $this->OrderDeliveryService->deliveryDetailList(['express_code' => $params['express_code']], 1, 1);
        if ( !empty($delivery_detail_list['list'])){
            return $this->returnApi(ResponseCode::SERVER_ERROR, '快递已交接不能作废');
        }

        //订单商品项id
        $order_detail_ids = array_column($logistics_list, 'order_detail_id');
        //订单发货物流id
        $order_logistics_ids = array_column($logistics_list, 'id');

        //用户信息
        $user_info = $this->session->get('userInfo');

        //取消订单发货物流信息
        $data = [
            'logistics_ids' => $order_logistics_ids,
            'logistics_data' => [
                'cancel_at' => date('Y-m-d H:i:s'),
                'cancel_admin_id' => $user_info['uid'],
                'cancel_admin_name' => $user_info['nickname'],
                'status' => 0
            ],
            'order_detail_ids' => $order_detail_ids
        ];
        $res = $this->OrderLogisticsService->cancelWmsOrderLogistics($data);
        if ($res) {
            //缓存key
            $make_order_key = $logistics_list[0]['branch_serial_no'] . '_make';
            $res = false;
            if ($this->HashService->keyIsExist($make_order_key)) {
                $res = $this->HashService->del($make_order_key);
            }
            return $this->returnApi(ResponseCode::SUCCESS, '作废成功');
        } else {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '作废失败');
        }
    }

    /**
     * @RequestMapping(path="/order/get_order_print", methods="get,post")
     */
    public function get_order_print()
    {
        $params = $this->request->all();

        if (empty($params)) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '参数不能为空');
        }

        if (!isset($params['order_code']) || empty($params['order_code'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '订单号参数不能为空');
        }

        if (!isset($params['unique_code']) || empty($params['unique_code'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '店内码参数不能为空');
        }

        //获取订单信息
        $order_info = $this->OrderService->getBranchDetail($params['order_code']);
        if ($order_info['source_id'] == 5){
            return $this->returnApi(ResponseCode::SERVER_ERROR, '代理订单不打印购物小票');
        }

        $data['order_info'] = [
            'branch_serial_no' => $order_info['branch_serial_no'],
            'add_time' => $order_info['add_time'],
            'receiving_name' => $order_info['receiving_name'],
            'receiving_phone' => $order_info['receiving_phone'],
            'receiving_address' => $order_info['receiving_province'] . $order_info['receiving_city'] . $order_info['receiving_area'] . $order_info['receiving_addr'],
            'remark' => $order_info['remark'] ?? '无'
        ];

        //获取订单商品
        $detail_where = [
            'get_order_branch_detail' => true,
            'branch_serial_no' => $params['order_code'],
            'unique_codes' => $params['unique_code']
        ];
        $order_detail = $this->OrderService->getList($detail_where, count($params['unique_code']), 1);
        if (empty($order_detail['list'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '订单商品项信息不存在');
        }
        $detail_list = [];
        foreach ($order_detail['list'] as $item) {
            $detail_list[] = [
                'goods_name' => $item['brand_name'] . '-' . $item['category_name'],
                'barcode' => $item['barcode'],
                'spec_name' => $item['spec_name'],
                'sale_price' => ($item['sale_price'] / 100),
                'nums' => 1
            ];
        }
        $data['detail_list'] = $detail_list;

        return $this->returnApi(ResponseCode::SUCCESS, '成功', $data);
    }

    /**
     * @RequestMapping(path="/order/confirm_delivery", methods="get,post")
     */
    public function confirm_delivery()
    {
        $params = $this->request->all();

        if (empty($params)) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '参数不能为空');
        }

        if (!isset($params['express_code']) || empty($params['express_code'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '快递单号不能为空');
        }

        if (!isset($params['logistics_company']) || empty($params['logistics_company'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '快递公司不能为空');
        }

        if (!isset($params['order_code']) || empty($params['order_code'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '订单号不能为空');
        }

        if (!isset($params['order_detail_id']) || empty($params['order_detail_id'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '订单详情id不能为空');
        }

        //检测快递单号是否被交接
        $orderDeliveryDetail = $this->OrderDeliveryService->deliveryDetailList(['express_code' => $params['express_code']], 10, 1);
        if (!empty($orderDeliveryDetail['list'])){
            return $this->returnApi(ResponseCode::SERVER_ERROR, '快递单号已被交接，无法再次制单！');
        }

        //用户信息
        $user_info = $this->session->get('userInfo');

        //获取订单的仓库
        $order_info = $this->OrderService->getBranchDetail($params['order_code']);
        if (empty($order_info)){
            return $this->returnApi(ResponseCode::SERVER_ERROR, '订单不存在');
        }
//        if ($order_info['status'] != 2){
//            return $this->returnApi(ResponseCode::SERVER_ERROR, '订单不符合制单状态');
//        }

        //获取订单商品项详情信息，检测是否可以发货
        $order_detail_list = $this->OrderService->getOrderDetail(['order_detail_ids' => $params['order_detail_id']]);
        if (empty($order_detail_list)){
            return $this->returnApi(ResponseCode::SERVER_ERROR, '订单商品信息不存在');
        }
        foreach ($order_detail_list as $detail){
            if ($detail['obd_status'] != 2){
                return $this->returnApi(ResponseCode::SERVER_ERROR, '订单商品不符合制单状态');
            }
        }

        //检测选择的快递公司和快递单号是否属于一个快递公司
        $where = [
            'branch_serial_no' => $params['order_code'],
            'logistics_company_id' => $params['logistics_company'],
            'express_code' => $params['express_code']
        ];
        $order_express_info = $this->OrderExpressApiLogService->getExpressApiInfo($where);
        if (empty($order_express_info)){
            return $this->returnApi(ResponseCode::SERVER_ERROR, '快递单号和快递公司不符');
        }

        //获取快递公司
        $logistics_company = $this->LogisticsService->list(1, 1000, []);
        $logistics_company_list = [];
        if (!empty($logistics_company['data'])){
            $logistics_company_list = array_column($logistics_company['data'], 'name', 'id');
        }

        $data = [];
        foreach ($params['order_detail_id'] as $detail_id) {
            $data[] = [
                'w_id' => $order_info['w_id'],
                'order_detail_id' => $detail_id,
                'branch_serial_no' => $params['order_code'],
                'logistics_company_id' => $params['logistics_company'],
                'logistics_company_name' => $logistics_company_list[$params['logistics_company']],
                'logistics_serial_no' => $params['express_code'],
                'admin_id' => $user_info['uid'],
                'admin_name' => $user_info['nickname'],
                'status' => 1
            ];
        }

        $res = $this->OrderLogisticsService->saveOrderLogistics($data);

        if ($res) {
            //缓存key
            $make_order_key = $params['order_code'] . '_make';
            if ($this->HashService->keyIsExist($make_order_key)) {
                $this->HashService->del($make_order_key);
            }
            return $this->returnApi(ResponseCode::SUCCESS, '确认发货成功');
        } else {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '确认发货失败');
        }
    }

    /**
     * @RequestMapping(path="/order/get_order_express", methods="get,post")
     */
    public function get_order_express()
    {
        $params = $this->request->all();

        if (empty($params)) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '参数不能为空');
        }

        if (!isset($params['order_code']) || empty($params['order_code'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '订单号参数不能为空');
        }

        //获取订单对应的快递面单
        $where = [
            'branch_serial_no' => $params['order_code'],
            'logistics_company_id' => $params['logistics_company'],
            'status' => 1
        ];
        $order_express_list = $this->OrderExpressApiLogService->getExpressApiList($where);
        if (empty($order_express_list)){#重新对接
            try {
                $order_express_info = $this->OrderService->againDistributionExpress(['branch_serial_no' => $params['order_code'], 'logistics_company_id' => $params['logistics_company']]);
            }catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }
            $print_data = json_decode($order_express_info['response_info'], true);#电子面单云打印取号接口返回值
            $express_code = $order_express_info['express_code'];#面单号
        }else{
            //检测面单号是否存在有效的制单信息
            $order_express_info = [];
            foreach ($order_express_list as $expressItem){
                $express_code = $expressItem['express_code'];#面单号
                $order_logistics = $this->OrderLogisticsService->getLogisticsOrder(['status' => 1, 'logistics_serial_no' => $express_code]);
                if (empty($order_logistics)){
                    $order_express_info = $expressItem;
                }
            }

            if (empty($order_express_info)){#无可用的面单信息,重新对接面单
                try {
                    $order_express_info = $this->OrderService->againDistributionExpress(['branch_serial_no' => $params['order_code'], 'logistics_company_id' => $params['logistics_company']]);
                }catch (\Exception $e) {
                    return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
                }
                $print_data = json_decode($order_express_info['response_info'], true);#电子面单云打印取号接口返回值
                $express_code = $order_express_info['express_code'];#面单号
            }else{
                $express_code = $order_express_info['express_code'];#面单号
                $print_data = json_decode($order_express_info['response_info'], true);#电子面单云打印取号接口返回值
            }
        }

        //更新打印次数
        $update_where = [
            'express_code' => $express_code,
            'branch_serial_no' => $params['order_code']
        ];
        $update_data = [
            'print_num' => $order_express_info['print_num'] + 1
        ];
        $this->OrderExpressApiLogService->updateExpressApiInfo($update_where, $update_data);

        $data = [
            'print_data' => $print_data,
            'express_code' => $express_code
        ];
        return $this->returnApi(ResponseCode::SUCCESS, '成功', $data);
    }

    /**
     * 补打快递面单
     * @RequestMapping(path="/order/get_late_print_express", methods="get,post")
     */
    function get_late_print_express(){
        $params = $this->request->all();

        if (empty($params)) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '参数不能为空');
        }

        if (!isset($params['logistics_serial_no']) || empty($params['logistics_serial_no'])) {
            return $this->returnApi(ResponseCode::SERVER_ERROR, '面单号不能为空');
        }

        //获取订单对应的快递面单
        $where = [
            'express_code' => $params['logistics_serial_no'],
            'status' => 1
        ];
        $order_express_info = $this->OrderExpressApiLogService->getExpressApiInfo($where);
        if (empty($order_express_info)){
            return $this->returnApi(ResponseCode::SERVER_ERROR, '面单不存在');
        }else{
            $print_data = json_decode($order_express_info['response_info'], true);#电子面单云打印取号接口返回值
            $express_code = $order_express_info['express_code'];#面单号
        }

        //更新打印次数
        $update_where = [
            'express_code' => $express_code
        ];
        $update_data = [
            'print_num' => $order_express_info['print_num'] + 1
        ];
        $this->OrderExpressApiLogService->updateExpressApiInfo($update_where, $update_data);

        $data = [
            'print_data' => $print_data,
            'express_code' => $express_code
        ];
        return $this->returnApi(ResponseCode::SUCCESS, '成功', $data);

    }
}