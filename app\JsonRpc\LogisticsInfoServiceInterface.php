<?php

namespace App\JsonRpc;


/**
 * 物流登记服务
 */
interface LogisticsInfoServiceInterface
{
    /**
     * 获取物流登记信息
     * @param array $where
     * @param int $page
     * @param int $currentPage
     */
    public function getLogisticsList(array $where, int $perPage, int $currentPage);

    /**
     * 获取物流信息
     * @param array $where
     * @return mixed
     */
    public function getLogisticsInfo(array $where);

    /**
     * 保存物流信息
     * @param array $data
     */
    public function addLogisticsInfo(array $data);

    /**
     * 交接批次ids
     * @param array $ids
     * @return mixed
     */
    public function logisticsPrint(array $ids);

    /**
     * 批次交接列表
     * @param array $where
     * @param int $perPage
     * @param int $currentPage
     * @return mixed
     */
    public function getLogisticsLists(array $where, int $perPage, int $currentPage);

    /**
     * 查多条
     * @param array $where
     * @param array|string[] $filed
     * @return array
     */
    public function getLogisticsInfoS(array $where, array $filed = ['*']);

    /**
     * 更新
     * @param array $where
     * @param array $data
     * @return int
     */
    public function updateLogisticsInfo(array $where, array $data);

    /**
     * 更新
     * @param array $where
     * @param array $data
     * @return int
     */
    public function updateLogisticsInfos(array $where, array $data);

    /**
     * 获取物流登记信息
     * @param array $where
     * @param int $perPage 每页条数
     * @param int $currentPage 页码
     */
    public function getList(array $where, int $perPage, int $currentPage);

    /**
     * 获取物流登记信息
     * @param array $where
     * @param int $perPage 每页条数
     * @param int $currentPage 页码
     */
    public function getNewList(array $where, int $perPage, int $currentPage);

    /**
     * 审核到货登记差异
     * @param array $where
     * @param array $data
     * @return int
     */
    public function verifyLogistics(array $params);
}