<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * 货架商品码映射
 */
Interface  ShelfGoodsCodeMapServiceInterface
{
    /**
     * 根据店内码批量获取对应货架号
     * @param array $uniqueCodes
     */
    public function getShelfCodeByUniqueCodes(array $uniqueCodes);

    /**
     * 店内码理货转架 unique_code 店内码 ； shelf_code ： 新货架号
     * @param array $data [ ['unique_code' => 'aaaaaaa','shelf_code'=>'s-b-c-d'],['unique_code' => 'bbbbbb','shelf_code'=>'a-b-c-d'], ]
     */
    public function uniqueCodeTransferShelf(array $data);

    /**
     * 校验epc码及对应店内码和条码是否存在
     * @param $data 'epc码 数组'
     */
    public function checkEpcCodes($data);

    /**
     * 通过epc码获取对应条码或店内码
     * @param array $data
     */
    public function getGoodsCodeByEpcCodes(array $data);

    /**
     * 校验店内码是否存在
     * @param array $data
     */
    public function checkUniqueCodes(array $data);

    /**
     * 校验店内码是否已出库
     * @param array $data
     */
    public function checkIfUniqueCodesOutStore(array $data);

    /**
     * 获取店内码信息
     * @param array $where
     */
    public function getUniqueCode(array $where);

    /**
     * 获取店内码信息
     * @param array $where
     */
    public function getUniqueCodes(array $where);

    /*
    * 校验epc码及对应店内码和条码是否存在并获取对应epc信息
    * @param $data 'epc码 数组'
    */
    public function checkEpcExistsAndGetEpcInfo($data);

    /**
     * 通过epc码获取对应条码或店内码与epc的映射
     * @param array $data
     */
    public function getGoodsCodeMapByEpcCodes(array $data);

    /**
     * 获取店内对应仓库的映射关系
     * @param array $data
     */
    public function getUniqueCodeWarehouseMap(array $data);

    /**
     * 条码理货转架 old_shelf_code: 原货架号 ； shelf_code ：目标货架号
     * @param array $data [
    ['old_shelf_code' => 's-b-c-j','shelf_code'=>'s-b-c-d','barcode'=>'aaaaa','num'=>'12'],
    ['old_shelf_code' => 's-b-c-k','shelf_code'=>'s-b-c-g','barcode'=>'bbbbb','num'=>'12']
    ]
     */
    public function barcodeTransferShelf(int $wId, array $data);

    /**
     * 根据epc批量获取epc信息
     * @param array $epcCodes
     */
    public function getEpcCodesInfo(array $epcCodes);

}