<?php

namespace App\JsonRpc;


/**
 * 活动服务
 */
interface PromotionServiceInterface
{
    /**
     * 添加活动
     * @param array $params
     * [
     *    'data' => [#活动信息
     *        'name' => '活动名称',
     *        'promotion_type' => '活动类型',
     *    ],
     *    'platform' => [#活动平台信息
     *        [
     *            'type_id' => '活动平台类型',
     *            'platform' => '平台值'
     *        ]
     *    ],
     *    'rule' => [
     *        [
     *            'info' => [
     *                'subtract_type' => '活动规则类型',
     *                'satisfy_value' => '满足(每满足)金额或件数【金额单位：分】',
     *                'reduce_value' => '减免金额或一口价【单位：分】',
     *                'sale_discount' => '销售折扣',
     *                'score_value' => '赠送积分',
     *                'coupon_id' => '代金券批次',
     *            ],
     *          'gift' => [#活动阶梯赠品
     *              [
     *                  'sku_id' => 'sku_id',
     *                  'stock_nums' => '赠品数量',
     *              ]
     *          ]
     *        ]
     *    ],
     *    'spu_list' => [
     *        [
     *            'type_id' => '类型',
     *            'value' => '类型值',
     *            'product_price' => '限时特价金额',
     *        ]
     *    ]
     *]
     */
    public function addPromotion(array $params);

    /**
     * 修改活动信息
     * @param array $where 条件
     * @param array $params 修改内容
     */
    public function editPromotion(array $where, array $params);

    /**
     * 获取活动列表
     * @param array $params
     * @param int $perPage 每页条数
     * @param int $currentPage 页码
     */
    public function getList(array $params, int $perPage = 10, int $currentPage = 1);

    /**
     * 获取单个活动信息
     * @param array $params
     * @return mixed
     */
    public function getPromotionInfo(array $params);

    /**
     * 获取活动平台信息
     * @param array $params
     */
    public function getPromotionPlatform(array $params);

    /**
     * 获取活动规则
     * @param array $params
     */
    public function getPromotionRule(array $params);

    /**
     * 获取活动赠品
     * @param array $params
     */
    public function getPromotionGift(array $params);

    /**
     * 获取活动限定的商品信息
     * @param array $params
     */
    public function getPromotionSpu(array $params);

    /**
     * 根据条件获取活动列表
     * @param array $params
     * @return array
     */
    public function getPromotions(array $params);

    /**
     * 校验spu是否存在于其他同时间段的活动中
     * @param array $params
     * @return array
     */
    public function checkSpuPromotion(array $params);


    /**
     * 根据商品获取活动
     * @param array $params
     * [
     *  "spu_list" => [
     *      [
     *          'brand_id' => 1,
     *          'category_id' => 1,
     *          'spu_id' => 1,
     *      ],
     *      [
     *          'brand_id' => 1,
     *          'category_id' => 1,
     *          'spu_id' => 1,
     *      ],
     *  ],
     * 'platform_type_id' => 1,#活动平台类型 1线上 2线下
     * 'platform' => 1,#平台值【线上：1:小程序 2:IOS 3:Android ；线下平台为门店id
     * ]
     */
    public function getPromotionBySpu(array $params);

    /**
     * spu在门店的 限时特价活动
     * @param array $spuIds
     * @param int $shopId
     * @return array
     */
    public function getXstjPromotionPrice(array $spuIds, int $shopId);
}