<?php

declare(strict_types=1);

namespace App\JsonRpc;


Interface AllotServiceInterface
{
    /**
     * 调拨单列表
     * @param int $page
     * @param int $limit
     * @param array $where [
     *      "serial_no":"调拨单号",
     *      "sku_id":"sku",
     *      "spu_id":"spu",
     *      "out_w_id":"调出方",
     *      "in_w_id":"凋入方",
     *      "admin_id":"制单人",
     *      "spu_name":"商品名",
     *      "status":"状态",
     *      "start_time":"开始时间",
     *      "end_time":"结束时间"
     * ]
     * @param array|string[] $field
     * @return array
     */
    public function list(int $page = 1, int $limit = 10, array $where = [], array $field = ['*']);

    /**
     * 调拨入库文件导入
     * @param $params[
     *   "serial_no":调拨单号,
     *   "admin_id":操作人,
     *   "admin_name":操作人姓名,
     * ]
     */
    public function generateTask($params);

    /**
     * 调拨详请
     * @param int $id
     */
    public function info(int $id);

    /**
     * 创建调拨
     * @param array $data [
     *    "serial_no" => 预约单号，
     *    "type" => 入库类型， 1=采购入库，3调拨入库
     *    "login_admin_id" => 用户id，
     *    "login_realname" => 用户姓名
     * ]
     * @return mixed
     */
    public function add( array $data);

    /**
     * 校验调拨单号是否存在
     * @param $data //单号数组
     */
    public function checkSerialNos(array $data);

    /**
     * 校验店内码是否属于此次调拨任务单
     * @param $data //店内码 数组
     */
    public function checkIfUniqueCodeInAllot(int $allot_id, array $data);

    /**
     * 获取调拨单主表信息
     * @param $allot_id
     */
    public function getAllotBySerialNo($allot_id);

    /**
     * 调拨发货
     * @param $data[
     *   "allot_id":调拨id,
     *   "send_type": 发货类型 1=物流，2=自提,
     *   "express_id": 物流公司id,
     *   "express_code": 物流单号,
     *   "freight_payer": 运费承担方,
     *   "freight_price": 运费,
     *   "linkman": 联系人 (自提必填),
     *   "mobile": 联系方式 (自提必填),
     *   "img_list": 图片列表,
     *   "multi_boxno": 箱号,
     *   "remark": 备注,
     *   "express_time": 发货时间
     * ]
     */
    public function allotSend(array $data);

    /**
     * 调拨发货
     * @param $data[
     *   "id":调拨id
     * ]
     */
    public function finishSend(array $data);

    /**
     * 调拨统计
     * @param $id 调拨id
     * @return \Hyperf\Database\Model\Builder|\Hyperf\Database\Model\Collection|\Hyperf\Database\Model\Model
     */
    public function stat($id);

    /**
     * 完成调拨
     * @param int $id 调拨id
     */
    public function over(int $id);

    /**
     * 详情页
     * @param int $id
     * @param int $page
     * @param int $limit
     */
    public function detailList(int $id,int $page = 1, int $limit = 10);
    /**
     * 校验店内码是否属于此次调拨任务单
     * @param $uniqueCodes //店内码 数组
     */
    public function checkIfUniqueCodeInAllotBySerialNo($serialNo, array $uniqueCodes);

    /**
     * 校验条码是否属于此次调拨任务单
     * @param $barcodes //店内码 数组
     */
    public function checkIfBarcodeInAllotBySerialNo($serialNo, array $barcodes);

    /**
     * 根据单号获取调拨信息
     * @param string $serialNo 调拨单号
     */
    public function infoByNo($serialNo);

    /**
     * 调拨单列表
     * @param int $page
     * @param int $limit
     * @param array $where [
     *      "serial_no":"调拨单号",
     *      "sku_id":"sku",
     *      "spu_id":"spu",
     *      "out_w_id":"调出方",
     *      "in_w_id":"凋入方",
     *      "admin_id":"制单人",
     *      "spu_name":"商品名",
     *      "status":"状态",
     *      "start_time":"开始时间",
     *      "end_time":"结束时间"
     * ]
     * @param array|string[] $field
     * @return array
     */
    public function exportDetailList(array $where = [], array $field = ['*']);

    /**
     * 作废
     * @param int $id
     * @return string[]
     */
    public function cancel( int $id,string $remark='无');

    /**
     * 根据仓库id获取可能查看的出库仓和入库仓
     * @param $wIds
     */
    public function ownWids($wIds);

    /**
     * 根据仓库id获取可能查看的出库仓和入库仓
     * @param $wIds
     */
    public function ownWidOptions($wIds);

    /**
     * 调拨预警列表
     * @param int $page
     * @param int $limit
     * @param array $where [
     *      "serial_no":"调拨单号",
     *      "out_w_id":"调出方",
     *      "in_w_id":"凋入方",
     * ]
     * @param array|string[] $field
     * @return array
     */
    public function warnList(int $page = 1, int $limit = 10, array $where = [], array $field = ['*']);

    /**
     * 调拨预警列表
     * @param int $page
     * @param int $limit
     * @param array $where [
     *      "end_warn_date":"预警时间",
     *      "out_w_id":"调出方",
     *      "in_w_id":"凋入方",
     * ]
     * @param array|string[] $field
     * @return string
     */
    public function exportWarnDetailList(array $where = []);
}
