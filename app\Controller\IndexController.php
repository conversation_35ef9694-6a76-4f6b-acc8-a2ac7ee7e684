<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use App\Library\Facades\GuzzleClientFactory;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;

/**
 * @Controller()
 */
class IndexController extends AbstractController
{
    /**
     * 框架页面
     * @RequestMapping(path="/", methods="get")
     */
    public function index()
    {
        return $this->show('index/index',['token'=>'']);
    }

    /**
     * 主页
     * @RequestMapping(path="/main", methods="get")
     */
    public function main()
    {
        return $this->show('index/main');
    }

    /**
     * 菜单
     * @RequestMapping(path="/menu", methods="get,post")
     */
    public function menu(){
        $client = GuzzleClientFactory::gatewayClient();
        $data = $this->request->all();
        $response = $client->request('POST', '/v1/index/menu',[
            'json' => [
                "app_type"=> env('APP_TYPE','wms')
            ],
            'headers' => [
                'Authorization' => $this->session->get('token'),
                'Content-Type' => 'application/json',
                'Request-Type' => 'api-request'
            ]
        ]);
        $body = json_decode($response->getBody()->getContents(),true);
        logger()->debug('login_result',['body'=>$body,'data'=>$data]);
        if(!$body ){
            return $this->returnApi(400,'网关数据错误！');
        }
        if($body['code'] != 200){
            return $this->returnApi(400,'无法获取菜单数据！');
        }
        return $body['result']['data'] ?? [];
        //return $this->config->get('menu');
    }
}
