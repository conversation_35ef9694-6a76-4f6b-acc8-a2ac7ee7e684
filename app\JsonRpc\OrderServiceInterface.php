<?php
declare(strict_types=1);

namespace App\JsonRpc;

interface OrderServiceInterface
{
    /**
     * 获取父订单信息
     * @param array $params
     */
    public function getOrderInfo(array $params);

    /**
     * 获取订单列表
     * @param array $params
     * @param int $perPage
     * @param int $currentPage
     */
    public function getList(array $params, int $perPage = 10, int $currentPage = 1);

    /**
     * 获取子订单详情
     * @param string $branch_serial_no
     */
    public function getBranchDetail(string $branch_serial_no = '');

    /**
     * 获取子订单号
     * @param array $params
     */
    public function getBranchOrderCodes(array $params);

    /**
     * 获取订单详情
     * @param array $params
     */
    public function getOrderDetail(array $params);

    /**
     * 获取订单的发货信息
     * @param array $params
     */
    public function getOrderlogistics(array $params);

    /**
     * 修改子订单信息
     * @param array $params
     */
    public function editOrderBranchInfo(array $params);

    /**
     * 修改订单信息
     * @param array $params
     */
    public function editOrderInfo(array $params);

    /**
     * 获取售后单信息
     * @param array $params
     */
    public function getOrderSupport(array $params);

    /**
     * 获取退款单信息
     * @param array $params
     */
    public function getOrderRefund(array $params);

    /**
     * 订单发货
     * @param array $params
     * [
     *   [
     *     'order_info' => [
     *          'branch_order_code' => '子订单号'
     *     ],
     *     'order_detail' => [
     *          [
     *              'order_detail_id' => 商品项id,
     *              'logistics_company_id' => 物流公司id,
     *              'logistics_company_name' => 物流公司名称,
     *              'logistics_serial_no' => 物流单号,
     *              'admin_id' => 创建人id,
     *              'admin_mane' => 创建人姓名,
     *          ]
     *      ]
     *   ]
     * ]
     */
    public function deliverOrder(array $params = []);

    /**
     * 父订单 - 查询单个
     * @param array $where
     * @param array|string[] $filed
     */
    public function getOrder(array $where, array $filed = ['*']);

    /**
     * 父订单 - 查询多个
     * @param array $where
     * @param array|string[] $filed
     */
    public function getOrders(array $where, array $filed = ['*']);

    /**
     * 订单明细 - 查单个
     * @param array $where
     * @param array|string[] $filed
     */
    public function getOrderBranchDetail(array $where, array $filed = ['*']);

    /**
     * 订单明细 - 查多个
     * @param array $where
     * @param array|string[] $filed
     * @return array
     */
    public function getOrderBranchDetailS(array $where = [], array $filed = ['*']);

    /**
     * 子订单 - 查单个
     * @param array $where
     * @param array|string[] $filed
     * @return array
     */
    public function getOrderBranch(array $where, array $filed = ['*']);

    /**
     * 获取订单拣货数据
     * @param array $where
     * @return mixed
     */
    public function getOrderSalePickInfo (array $where);

    /**
     * 修改订单当前发货阶段
     * @param array $where
     * @param array $data
     * @return mixed
     */
    public function updateOrderCurrentGeneration(array $where,array $data);

    /**
     * 修改订单商品项信息
     * @param array $params
     */
    public function editOrderBranchDetail(array $params);

    /**
     * 子订单 - 查多个
     * @param array $where
     * @param array|string[] $filed
     * @return array
     */
    public function getOrderBranchS(array $where = [], array $filed = ['*']);


    /**
     * 修改订单商品项信息
     * @param array $params
     */
    public function ModifyOrderBranchDetail(array $params);

    /**
     * 为订单分配店内码 和 条形码
     */
    public function distributionCode();

    /**
     * 分配快递
     */
    public function distributionExpress();

    /**
     * 再次为订单匹配快，用于制单时组合订单、大宗订单发货时需要发多个包裹时追加快递面单
     */
    public function againDistributionExpress(array $where);

    /**
     * 仓库 - 操作货品回库时，修改订单逻辑
     * @param int $type 1、店内码订单 2、条码订单
     * @param array $data
     * @return bool
     */
    public function rebackUpdate(int $type, array $data);

    /**
     * 将支付半小时后的订单改为已接单
     */
    public function receiveOrders();

    /**
     * 解锁已取消订单分配的店内码和条形码
     */
    public function unlockCode();

    /**
     * 根据编码获取订单信息
     * @param $code (订单号|用户编码|店内码)
     */
    public function listByCode($code,$options=[]);

}
