<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <link href="/static/layui/css/layui.css" rel="stylesheet"/>
    <link href="/static/css/pearCommon.css" rel="stylesheet"/>
    <link href="/static/css/myui.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/static/css/myui.css"/>
    <script src="/static/layui/layui.js" charset="utf-8"></script>
    <style id="pearone-bg-color">
        html, body {
            overflow-y: auto; /* 允许垂直滚动 */
        }
        .layui-table-cell {
            overflow: visible !important;
        }
        .layui-table-box {
            overflow: visible;
        }
        .layui-table-body {
            overflow: visible;
        }

        .footer-div {
            z-index: 10;
            position: fixed;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 92px;
            background-color: #fff;
            border-top: 1px solid #f8f8f8;
        }
        .footer-div-inner {
            padding-top: 7px;
            padding-bottom: 7px;
            border: 1px solid #e8ebec;
            width: 100%;
            height: 55px;
            background-color: #fff;
            /*box-shadow: 0 1px 6px #888888;*/
        }
    </style>
</head>
<body class="layui-layout-body pear-admin p-16 pd32">
<div class="mb-100 layui-row layui-col-space10">
    <form class="layui-form" action="">
        <div class="layui-panel">
            <div class="layui-card">
                <div class="layui-card-header">基本信息</div>
                <div class="layui-card-body row h_ard p-14">
                    <table class="layui-table" lay-skin="nob">
                        <tbody>
                        <tr>
                            <td>仓店：{{$detail['ware_name']}}</td>
                            <td>异常类型：{{$detail['exception_type_name']}}</td>
                            <td>状态：{{$detail['status_name']}}</td>
                            <td>备注：{{$detail['remark']}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="layui-card">
                <div class="layui-tab" lay-filter="test-hash">
                    <ul class="layui-tab-title">
                        <li class="layui-this" lay-id="11">货品信息</li>
                        <li lay-id="22">操作记录</li>
                    </ul>
                    <div class="layui-tab-content">
                        <div class="layui-tab-item layui-show">
                            <table class="layui-hide" id="list" lay-filter="tableBar"></table>
                        </div>
                        <div class="layui-tab-item">
                            <table class="layui-hide" id="log_list" lay-filter="tableBar"></table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-card">
                <div class="layui-card-header">异常图片</div>
                <div class="layui-card-body row p-14">
                    @if(!empty($detail['imgs']['exception_img']))
                    @foreach($detail['imgs']['exception_img'] as $image)
                    <img src="{{$image['image']}}" style="width: 100px;height: 100px;margin-right: 10px;cursor: pointer;" onclick="layer.photos({photos: {data: [{src: '{{$image['image']}}'}]}, anim: 5});">
                    @endforeach
                    @endif
                </div>
            </div>
            <div class="layui-card">
                <div class="layui-card-header">去向图片</div>
                <div class="layui-card-body row h-vc p-14">
                    <button type="button" class="layui-btn layui-btn-sm mr8 clickUpload" lay-filter="upload_filter" id="multi_img">多图片上传</button>
                    <div class="layui-upload-list uploader-list row" style="overflow: auto; margin-left:3px" id="uploader_list">
                        @if(!empty($detail['imgs']['destination_img']))
                        @foreach($detail['imgs']['destination_img'] as $image)
                        <div id="" class="file-iteme">
                            <img style="width: 100px;height: 100px;margin-right: 10px;cursor: pointer;" data-rowindex="${d.row_index}" class="upload_img_preview" src="{{$image['image']}}" onclick="layer.photos({photos: {data: [{src: '{{$image['image']}}'}]}, anim: 5});">
                        </div>
                        @endforeach
                        @endif
                    </div>
                </div>
            </div>
            <div class="layui-card">
                <div class="layui-card-header">仓店备注</div>
                <div class="layui-card-body row p-14 mb-100">
                    <textarea class="layui-textarea" name="shop_remark" id="remark" style="width: 100%;height: 100px;">{{$detail['shop_remark']}}</textarea>
                </div>
            </div>
        </div>
        <!--    底部按钮-->
        <div class=" footer-div v-vc " >
            <div class="footer-div-inner row f_end h-vcr">
                <button type="button" class="mlr16 layui-btn layui-btn-lg layui-btn-default" lay-submit lay-filter="submit">提交</button>
            </div>
        </div>
    </form>
</div>
<div>
    <input type="hidden" name="id" value="{{$detail['id']}}">
    <input type="hidden" name="cate1_name" value="{{$detail['cate1_name']}}">
    <input type="hidden" name="brand_name" value="{{$detail['brand_name']}}">
    <input type="hidden" name="spu_no" value="{{$detail['spu_no']}}">
    <input type="hidden" name="goods_code" value="{{$detail['goods_code']}}">
    <input type="hidden" name="sup_name" value="{{$detail['sup_name']}}">
    <input type="hidden" name="recommend_price" value="{{$detail['recommend_price']}}">
    <input type="hidden" name="destination_name" value="{{$detail['destination_name']}}">
    <input type="hidden" name="destination" value="{{$detail['destination']}}">
    <input type="hidden" name="real_price" value="{{$detail['real_price']}}">
</div>
<script src="/static/layui/layui.js"></script>
<script>
    layui.config({
        base: '/static/js/' //自定义模块
    }).use(['form','dropdown','element','layer', 'table' ,'laydate','xmSelect','iframeTools','upload'], function () {
        var $ = layui.jquery;
        var form = layui.form;
        var table = layui.table;
        var iframeTools = layui.iframeTools;
        var closeTools = layui.closeTools;
        var xmSelect = layui.xmSelect; // 下拉多选
        var upload = layui.upload;
        var destination = [
            {name: '线下售卖', value: 1},
            {name: '冲底盘亏', value: 2},
            {name: '报损', value: 3},
        ]

        var detail = {
            'id': $('input[name=id]').val(),
            'cate1_name': $('input[name=cate1_name]').val(),
            'brand_name': $('input[name=brand_name]').val(),
            'spu_no': $('input[name=spu_no]').val(),
            'goods_code': $('input[name=goods_code]').val(),
            'sup_name': $('input[name=sup_name]').val(),
            'recommend_price': $('input[name=recommend_price]').val(),
            'destination_name': $('input[name=destination_name]').val(),
            'destination': $('input[name=destination]').val(),
            'real_price': $('input[name=real_price]').val()
        }
        console.log('详情',detail);

        table.render({
            elem: "#list"
            ,data: [
                {
                    'cate1_name': detail['cate1_name'],
                    'brand_name': detail['brand_name'],
                    'spu_no': detail['spu_no'],
                    'goods_code': detail['goods_code'],
                    'sup_name': detail['sup_name'],
                    'recommend_price': detail['recommend_price'],
                    'destination_name': detail['destination_name'],
                    'destination': detail['destination'],
                    'real_price': detail['real_price']
                }
            ]
            , skin: 'line'
            , cellMinWidth: 80 //全局定义常规单元格的最小宽度
            , cols: [[
                {field: 'cate1_name', title: '一级类目'}
                , {field: 'brand_name', title: '品牌名'}
                , {field: 'spu_no', title: '货号'}
                , {field: 'goods_code', title: '店内码/条码'}
                , {field: 'sup_name', title: '供应商'}
                , {field: 'recommend_price', title: '建议售价'}
                , {title: '货品去向',templet: function (d) {
                        let options = makeOptions('value', destination, d.destination,'货品去向');
                        return `<div class="layui-form-item layui-form-testcss">
                                    <div class="layui-input-wrap">
                                        <select name="destination" lay-filter="destination_filter" lay-append-to="body">${options}</select>
                                    </div>
                                </div>
                                `
                    }}
                , {title: '实际售价', templet: function (d) {
                        return `<div class="layui-form-item layui-form-testcss">
                                    <div class="layui-input-wrap">
                                        <input type="text" name="real_price" value="${d.real_price}" placeholder="实际售价" autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                                `
                    }}
            ]]
        });

        function makeOptions(fieldName, data,selectedValue,title) {
            var options = '<option value="">请选择'+title+'</option>'
            $.each(data,function (index,item) {
                if (item[fieldName] == selectedValue) {
                    options += "<option value="+item[fieldName]+" selected>"+item.name+"</option>"
                } else {
                    options += "<option value="+item[fieldName]+">"+item.name+"</option>"
                }
            })
            return options
        }

        form.on('submit(submit)',function (data){
            var data = data.field;
            console.log('data',data)
            data.id = detail.id
            data.destination_img = upload_img_list
            // 检查货品去向是否为空
            if(!data.destination) {
                layer.msg('请选择货品去向', {icon: 2});
                return false;
            }
            if(data.destination == 1) {
                if(!data.real_price || isNaN(data.real_price) || parseFloat(data.real_price) < 0) {
                    layer.msg('线下售卖实际售价必填', {icon: 2});
                    return false;
                }
            }
            layer.confirm('确认提交吗？', function (index) {
                submit(data)
                return false;
            });

            return false; // 阻止默认 form 跳转
        })
        function submit(params) {
            var index = layer.load(1, {
                shade: [0.1,'#fff'] //0.1透明度的白色背景
            });
            $.ajax({
                type: 'post',
                url: "/exceptionGoods/updateByShop",
                data: params,
                success: function (res) {
                    console.log('返回结果2：',res)
                    layer.close(index)
                    if (parseInt(res.code) !== 200) {
                        console.log('提交失败')
                        layer.msg(res.msg,{icon:2})
                        return false
                    }
                    // 提示res.msg后，跳转到list
                    layer.msg(res.msg, {icon: 1}, function(){
                        console.log('跳转到list2');
                        window.location.href = '/exceptionGoods/list?t=' + new Date().getTime();
                    });
                },error: function (e) {
                    layer.alert("提交失败！")
                    layer.close(index)
                },
            })

        }
        var upload_img_list = []
        upload.render({
            elem: '#multi_img'
            , url: '/common/upload'
            , multiple: true
            ,accept: 'file' //允许上传的文件类型
            ,exts: 'jpg|png|gif|jpeg' //允许上传的文件后缀
            ,size: 102400 //最大文件大小，单位KB，调整为允许上传大于5M的图片
            , before: function (obj) {
                layer.msg('图片上传中...', {
                    icon: 16,
                    shade: 0.01,
                    time: 0
                })
            }
            , done: function (res) {

                console.log('上传结果',res)
                if (res.code == 200) {
                    $('#uploader_list').append(
                        `
                        <div id="" class="file-iteme">
                            <img style="width: 100px;height: 100px;margin-right: 10px;cursor: pointer;" class="upload_img_preview" src="${res.data.url}" onclick="layer.photos({photos: {data: [{src: '${res.data.url}'}]}, anim: 5});">
                        </div>
                        `
                    )
                    upload_img_list.push(res.data.url)
                } else {
                    layer.msg(res.msg)
                }
                layer.close(layer.msg());//关闭上传提示窗口
            },complete: function (e) {
                console.log('上传完成1',e)
            },error: function (e) {
                console.log('上传完成2',e)
            }
        })

        // 获取日志
        getLog('#log_list')
        function getLog(position) {

            table.render({
                elem: position
                , url: '/exceptionGoods/getLog'
                , method: "post"
                , where: {
                    id: {{$detail['id']}},
                }
                , parseData: function (res) { //res 即为原始返回的数据
                        console.log('返回结果==',res)
                        return {
                            "code": res.code, //解析接口状态
                            "msg": res.msg, //解析提示文本
                            "count": res.data.total, //解析数据长度
                            "data": res.data.data //解析数据列表
                        };
                    }
                , skin: 'line'
                , cellMinWidth: 80 //全局定义常规单元格的最小宽度
                , cols: [[
                    {title: '序号',type:"numbers"}// 自增序号
                    , {field: 'op_type',title: '操作类型'}
                    , {field: 'op_content',title: '操作内容'}
                    , {field: 'admin_name',title: '操作人'}
                    , {field: 'op_time',title: '操作时间'}
                ]]
                , page: true
            });
        }

    });
</script>
</body>
</html>
