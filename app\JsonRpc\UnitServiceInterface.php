<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * 单位服务
 */
interface UnitServiceInterface
{
    public function addUnit(array $data);

    public function editUnit(int $id, array $data);

    public function getUnitListByType(int $type);

    public function getUnitAll(array $field = [], array $where = []);

    public function getUnitList(int $page = 1, int $limit = 10, array $where = [], array $field = []);

    public function getUnitRowById(int $unit_id, string $row);

    public function getUnitByField(array $where, $isPage = false, int $page = 1, int $limit = 10);

    /**
     * 批量校验单位是否存在
     * @param array $data
     * @return array
     */
    public function checkUnits (array $data);

    public function addUnits(array $data);
}