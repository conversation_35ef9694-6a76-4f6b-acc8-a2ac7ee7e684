<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * 理货转架
 */
Interface  TallyServiceInterface
{
    /**
     * 保存转架数据
     * @param $tType
     * @param $wId
     * @param $redisKey
     * @param array $adminInfo
     */
    public function addTally($tType,$wId, $redisKey,array $adminInfo);

    /**
     * 理货列表
     * @param int $page
     * @param int $limit
     * @param array $where
     * @param array $field
     * @return array
     */
    public function getTallyList(int $page = 1, int $limit = 10, array $where = [], array $field = []);

    /**
     * 获取理货详情
     * @param $id
     */
    public function getTallyOne($id);

    /**
     * 获取清单列号
     * @param $t_id
     */
    public function getBillList($t_id,int $page = 1, int $limit = 10, array $where = [], array $field = []);

    /**
     * 获取指定仓库的店内码对应sku_id的货架库存
     * @param $wId
     * @param $adminInfo
     * @param array $uniqueCodes
     */
    public function getUniqueCodeStock($adminInfo,$wId,array $uniqueCodes);

    /**
     * 获取清单列表
     * @param $t_id
     * @param array $where
     * @param array $field
     * @return array
     */
    public function getBillAll($t_id, array $where = [], array $field = []);

    /**
     * 获取理货单统计数字
     * @param $tId
     * @return array
     */
    public function getTallSummary($tId);

    /**
     * 查询条码货架库存
     * @param array $data
     * [
     *  ['w_id' => 3005,'shelf_code' => 'LS-01C-101','barcode => 'AA1234345']
     * ]
     */
    public function getBarcodeShelfStock(array $data);

    /**
     * 将校验数据插入临时表
     * @param array $data
     * @param $goodsCodeType
     * @param $adminInfo
     */
    public function insertTempCheckData(array $data,$goodsCodeType,$adminInfo);

    /**
     * 根据店内码批量获取对应货架号
     * @param $adminInfo
     * @param array $uniqueCodes
     */
    public function getShelfCodeByUniqueCodes($adminInfo,array $uniqueCodes);

    /**
     * 获取店内对应仓库的映射关系（店内码找仓库）
     * @param $adminInfo
     * @param array $data
     */
    public function getUniqueCodeWarehouseMap($adminInfo,array $data);

    /**
     * 校验店内码是否已出库
     * @param $adminInfo
     * @param array $data
     */
    public function checkIfUniqueCodesOutStore($adminInfo,array $data);

    /**
     * 删除理货临时表数据
     * @param $adminInfo
     * @param $goodsCodeType
     */
    public function delTallyTemp($adminInfo,$goodsCodeType);


    // 导出数据
    public function exportData(int $page, int $limit, array $search);

    /**
     * 计划理货任务列表
     * @param array $params
     */
    public function getPlanTallyUniqueList(array $params);

    /**
     * 创建计划理货任务
     * @param $data
     * @param $params
     */
    public function createPlanTally($data,$params);

    /**
     * 过滤掉残次条码
     * @param $data
     * @param $params
     * @return mixed
     */
    public function filterImperfect($data,$params);

    // 获取条码货架库存
    public function getBarcodeStock($data,$params);

    /**
     * 校验店内码是否中转批次
     * @param $uniqueCodes
     */
    public function checkUniqueIfTransfer($uniqueCodes);
}