<?php

namespace App\Library\Facades;


use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\UniqueCodeServiceInterface;
use Hyperf\Guzzle\ClientFactory;

class UniqueCodeService extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor ()
    {
        return UniqueCodeServiceInterface::class;
    }
}
