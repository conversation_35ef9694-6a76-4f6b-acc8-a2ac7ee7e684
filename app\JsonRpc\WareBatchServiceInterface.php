<?php

namespace App\JsonRpc;

/**
 * 出入库单
 */
interface WareBatchServiceInterface
{
    /**
     * 添加入库单信息
     * @param array $params
     * @return int
     */
    public function addInWare(array $params);

    /**
     * 获取入库单列表
     * @param array|null $params
     * @return array
     */
    public function getInWareList(array $params, int $perPage = 10, int $currentPage = 1);

    /**
     * 检查入库单是否存在
     * @param array $where
     * @return bool
     */
    public function checkInWareIsExist(array $where);

    /**
     * 修改入库单信息
     * @param int $id
     * @param array $params
     */
    public function editInWare(int $id, array $params);

    /**
     * 获取入库单详情
     * @param array $params
     * @return mixed
     */
    public function getInWareInfo(array $params);

    /**
     * 获取入库单明细信息
     * @param array $params
     * @param int $perPage
     * @param int $currentPage
     * @return array
     */
    public function getInWareDetail(array $params, int $perPage = 10, int $currentPage = 1);

    /**
     * 根据入库单id获取入库单的spu
     * @param array $in_ware_batch_ids
     * @return array
     */
    public function getInWareDetailSpu(array $in_ware_batch_ids );

    /**
     * 添加出库单信息
     * @param array $params
     */
    public function addOutWare(array $params);

    /**
     * 获取出库单列表
     * @param array|null $params
     * @param int $perPage
     * @param int $currentPage
     * @return array
     */
    public function getOutWareList(array $params, int $perPage = 10, int $currentPage = 1);

    /**
     * 修改出库单信息
     * @param int $id
     * @param array $params
     */
    public function editOutWare(int $id, array $params);

    /**
     * 获取出库单详情信息
     * @param int $id
     * @return mixed
     */
    public function getOutWareInfo(int $id);

    /**
     * 获取出库单的详情
     * @param array $params
     * @param int $perPage
     * @param int $currentPage
     */
    public function getOutWareDetail(array $params, int $perPage = 10, int $currentPage = 1);

    /**
     * 根据出库单id获取出库单的spu
     * @param array $out_ware_batch_ids
     * @return array
     */
    public function getOutWareDetailSpu(array $out_ware_batch_ids);

    /**
     * 获取出入库单据列表
     * @param array $params
     * @param int $perPage
     * @param int $currentPage
     */
    public function getWareList(array $params, int $perPage = 10, int $currentPage = 1);

    /**
     * 进销存流水获取入库信息
     * @param array $params
     * @return mixed
     */
    public function getInWareByInventoryStream (array $params);

    /**
     * 进销存流水获取出库信息
     * @param array $params
     * @return mixed
     */
    public function getOutWareByInventoryStream (array $params);
}