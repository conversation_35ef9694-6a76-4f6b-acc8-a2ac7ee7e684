<?php
declare(strict_types=1);

namespace App\Controller;

use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\ShelfServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;

/**
 * @Controller()
 */
class ShelfController extends AbstractController
{
    /**
     * @Inject()
     * @var ValidatorFactoryInterface
     */
    private $validator;
    /**
     * @Inject()
     * @var WarehouseServiceInterface
     */
    private $WarehouseService;
    /**
     * @Inject()
     * @var ShelfServiceInterface
     */
    private $ShelfService;
    /**
     * @Inject()
     * @var AdminServiceInterface
     */
    private $AdminService;

    /**
     * 添加
     * @RequestMapping(path="/shelf/add", methods="get,post")
     */
    public function add()
    {
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->AdminService->organizeWareHouseData($userInfo['uid']);

        if ($this->isAjax()) {
            $params = $this->request->all();
            $rule = [
                'w_id' => ['required', 'integer'],
                'shelf_type' => ['required', 'integer', 'between:1,4'],
                'shelf_codes' => ['required', 'array']
            ];
            $errors = $this->validator->make($params, $rule);
            if ($errors->fails()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
            }
            $snowId = $this->request->getAttribute('snow_id');
            // 货架号转大写
            foreach ($params['shelf_codes'] as &$code) {
                $code = strtoupper($code);
                $codes = explode("-", $code);
                if (4 == $params['shelf_type'] && $codes[0] != 'ZCQ') {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '暂存区货架区域前缀必须是ZCQ');
                }

                if (4 != $params['shelf_type'] && $codes[0] == 'ZCQ') {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非暂存货架区域前缀不允许是ZCQ');
                }
            }

            // 仓库校验
            if (!in_array($params['w_id'], $userWIds)) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无权操作此仓库');
            }
            $warehouse = $this->WarehouseService->getWarehouse(['id' => $params['w_id'], 'status' => PublicCode::warehouse_status_valid], ['id']);
            if (!$warehouse) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '仓库不存在或已无效');
            }
            // 货架号校验
            $shelfError = $this->checkData($params['w_id'], $params['shelf_codes']);
            if ($shelfError) {
                $errorRedisKey = "shelf_add:error:{$snowId}";
                redis()->setex($errorRedisKey, 3600, json_encode($shelfError, JSON_UNESCAPED_UNICODE));
                return $this->returnApi(ResponseCode::SERVER_ERROR, '错误', ['error_key' => $errorRedisKey]);
            }

            // 批量添加
            try {
                $this->ShelfService->batchCreate(
                    $params['w_id'],
                    $params['shelf_type'],
                    $params['shelf_codes'],
                    [
                        'admin_id' => $userInfo['uid'],
                        'admin_name' => $userInfo['nickname']
                    ]
                );
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }

            return $this->returnApi(ResponseCode::SUCCESS, '创建成功');
        }

        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses(['ids' => $userWIds, 'status' => PublicCode::warehouse_status_valid], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        // 货架类型
        $shelfType = PublicCode::shelf_type;

        return $this->show('shelf/add', [
            'warehouse_list' => $warehouse,
            'shelf_type' => $shelfType
        ]);
    }

    /**
     * 添加，有错下载
     * @RequestMapping(path="/shelf/downAddError", methods="get,post")
     */
    public function downAddError()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();

            $data = redis()->get($params['error_key']);
            if (!$data) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '过期，请重新提交');
            }
            $url = exportToExcel(['shelf' => '货架号', 'error' => '错误'], json_decode($data, true), '创建货架错误列表');
            return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 列表
     * @RequestMapping(path="/shelf/list", methods="get,post")
     */
    public function list()
    {
        // 用户
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->AdminService->organizeWareHouseData($userInfo['uid']);
        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        // 货架类型
        $shelfType = PublicCode::shelf_type;
        // 状态
        $shelfStatus = PublicCode::shelf_status;
        // 批量操作方式
        $handelType = [
            1 => '变更至常规货架',
            2 => '变更至高位货架',
            3 => '变更至陈列货架',
            4 => '变更至有效',
            5 => '变更至作废'
        ];

        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            $search['w_ids'] = $userWIds;
            $export = $params['export'] ?? 0;// 0列表 1导出

            $list = $this->ShelfService->list($export, (int)$page, (int)$pageLimit, $search);
            if ($list['data']) {
                foreach ($list['data'] as &$item) {
                    $item['w_name'] = $warehouse[$item['w_id']];
                    $item['type'] = $shelfType[$item['type']];
                    $item['status'] = $shelfStatus[$item['status']];
                }
            }

            if ($export) {
                if (!$list['data']) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
                }
                $url = exportToExcel($this->exportListHeader(), $list['data'], '货架号');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
        }

        return $this->show('shelf/list', [
            'warehouse_list' => $warehouse,
            'shelf_type' => $shelfType,
            'shelf_status' => $shelfStatus,
            'handel_type' => $handelType,
        ]);
    }

    private function exportListHeader()
    {
        return [
            'id' => 'ID',
            'w_name' => '仓库',
            'shelf_code' => '货架号',
            'type' => '类型',
            'status' => '状态',
            'area' => '货架区',
            'line' => '货架排',
            'seat' => '货架位',
            'grid' => '货架格',
            'created_at' => '创建时间'
        ];
    }

    /**
     * 批量操作
     * @RequestMapping(path="/shelf/batchHandel", methods="get,post")
     */
    public function batchHandel()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $rule = [
                'shelf_ids' => ['required', 'array'],
                'handel_type' => ['required', 'integer', 'between:1,5']
            ];
            $errors = $this->validator->make($params, $rule);
            if ($errors->fails()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
            }

            // 货架号是否存在
            $shelfList = $this->ShelfService->getShelfS(['ids' => $params['shelf_ids']], ['id', 'w_id', 'shelf_code','type']);
            if (in_array($params['handel_type'], [1, 2, 3])) {
                $check = collect($shelfList)->where('type', 4)->pluck('id')->implode(';');
                if ($check) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, 'ID为：' . $check . '的货架号属于暂存货架不允许修改货架类型');
                }
            }
            $hasShelf = $shelfList ? array_column($shelfList, 'id') : [];
            $error = array_diff($params['shelf_ids'], $hasShelf);
            if ($error) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, 'ID为：' . implode(';', $error) . '的货架号不存在');
            }

            if ($params['handel_type'] == 1) { // 变更至常规货架
                $upData = ['type' => 1];
            } elseif ($params['handel_type'] == 2) { // 变更至高位货架
                $upData = ['type' => 2];
            } elseif ($params['handel_type'] == 3) { // 变更至陈列货架
                $upData = ['type' => 3];
            } elseif ($params['handel_type'] == 4) { // 变更至有效
                $upData = ['status' => PublicCode::shelf_status_valid];
            } else { // 变更至作废
                // 检查对应货架是否有库存
                foreach ($shelfList as $shelf) {
                    $num = $this->ShelfService->getShelfStockNum($shelf['w_id'], $shelf['shelf_code']);
                    if ($num > 0) {
                        $error[] = $shelf['id'];
                    }
                }
                if ($error) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, 'ID为：' . implode(';', $error) . '的货架号还有库存，不能作废');
                }
                $upData = ['status' => PublicCode::shelf_status_invalid];
            }
            // 批量操作
            try {
                $this->ShelfService->update($params['shelf_ids'], $upData);
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    private function checkData(int $w_id, array $data)
    {
        $error = [];

        // 重复值
        $unique_arr = array_unique($data);
        $repeat_arr = array_diff_assoc($data, $unique_arr);
        // 数据库校验
        $hasShelf = $this->ShelfService->getShelfS(['w_id' => $w_id, 'shelf_codes' => $data], ['shelf_code']);
        $hasShelf = $hasShelf ? array_column($hasShelf, 'shelf_code') : [];

        foreach ($data as $value) {
            if (in_array($value, $repeat_arr)) {
                $error[$value][] = '有重复值';
            }
            if (strlen($value) > 40) {
                $error[$value][] = '长度超过40个字符了';
            }
            $item = explode('-', $value);
            if (!in_array(count($item), [3, 4])) {
                $error[$value][] = '格式错误';
            }
            if (in_array($value, $hasShelf)) {
                $error[$value][] = '已存在';
            }
        }

        $newError = [];
        if ($error) {
            foreach ($error as $shelf => $item) {
                $newError[] = [
                    'shelf' => $shelf,
                    'error' => implode('；', array_unique($item))
                ];
            }
        }

        return $newError;
    }

    private function returnError(array $error)
    {
        foreach ($error as $key => $value) {
            unset($error[$key]);
            $error[] = implode('；', $value);
        }
        return implode("\n", $error);
    }

    /**
     * 货架号占用查询
     * @RequestMapping(path="/shelf/occupy", methods="get,post")
     */
    public function occupy()
    {
        // 用户
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->AdminService->organizeWareHouseData($userInfo['uid']);
        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];

        // 状态
        $shelfStatus = PublicCode::shelf_status;
        // 批量操作方式
        $handelType = [
            1 => '升序',
            2 => '降序',
        ];

        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            if(empty($search['w_id'])){
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '仓库为必选字段');
                $search['w_ids'] = [0];
            }else{
                $search['w_ids'] = [$search['w_id']];
            }
            $export = $params['export'] ?? 0;// 0列表 1导出

            if(!$export){
                $list = $this->ShelfService->occupy((int)$page, (int)$pageLimit, $search);
                if ($list['data']) {
                    foreach ($list['data'] as &$item) {
                        $item['show_brand_names'] = "";
                        if(!empty($item['brand_names'])){
                            $brandNames = explode(",", $item['brand_names']);
                            if (isset($brandNames[0])) $item['show_brand_names'] .= $brandNames[0];
                            if (isset($brandNames[1])) $item['show_brand_names'] .= "," . $brandNames[1];
                            if (isset($brandNames[2])) $item['show_brand_names'] .= "...";
                        }

                        $item['show_category_names'] = "";
                        if(!empty($item['category_names'])){
                            $brandNames = explode(",", $item['category_names']);
                            if (isset($brandNames[0])) $item['show_category_names'] .= $brandNames[0];
                            if (isset($brandNames[1])) $item['show_category_names'] .= "," . $brandNames[1];
                            if (isset($brandNames[2])) $item['show_category_names'] .= "...";
                        }

                        unset($item['brand_names'],$item['category_names']);
                    }
                }
            }else{
                $exportLists = [];
                $pageLimit = 2000;
                while(true){
                    $list = $this->ShelfService->occupy((int)$page, (int)$pageLimit, $search);
                    if (empty($list['data'])) break;

                    $exportLists = array_merge($exportLists,$list['data']);
                    $page++;
                }
                if (empty($exportLists)) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
                }
                $url = exportToExcel(
                    [
                        'w_name'=>'仓库名称',
                        'area'=>'货架区',
                        'line'=>'货架排',
                        'seat'=>'货架位',
                        'shelf_code'=>'货架号',
                        'brand_names'=>'货品品牌分布',
                        'category_names'=>'货品分类分布',
                        'total'=>'货品数量',
                    ], $exportLists, '货架号占用查询');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
        }

        return $this->show('shelf/occupy', [
            'warehouse_list' => $warehouse,
            'shelf_status' => $shelfStatus,
            'handel_type' => $handelType,
        ]);
    }
}