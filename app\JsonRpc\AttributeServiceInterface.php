<?php
declare(strict_types=1);

namespace App\JsonRpc;


interface AttributeServiceInterface
{

    public function addKey(array $data);

    public function editKey(int $id = 0, array $data);

    public function keyList(int $page=0,string $name='',int $prePage=20, array $ids = []);

    public function addValue(array $data);

    public function editValue(int $id = 0, array $data);

    public function valueList(int $page = 0, int $prePage = 20 ,int $sk_id = 0, array $ids = []);

    public function keyValueMapByName( array $names);

    public function checkSpecKey(array $data);

    public function checkSpecVal(array $data);

    public function getSpecBySku(array $skuIds);
    
    public function valuesByKeyId(array $skIds);
}

