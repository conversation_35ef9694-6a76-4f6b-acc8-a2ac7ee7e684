<?php
declare(strict_types=1);

namespace App\Controller;

use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Constants\SerialType;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\BrandServiceInterface;
use App\JsonRpc\ImperfectServiceInterface;
use App\JsonRpc\MessageLogServiceInterface;
use App\JsonRpc\SerialNoServiceInterface;
use App\JsonRpc\ShelfServiceInterface;
use App\JsonRpc\SkuServiceInterface;
use App\JsonRpc\StockSupServiceInterface;
use App\JsonRpc\SupplierServiceInterface;
use App\JsonRpc\TaskServiceInterface;
use App\JsonRpc\UniqueCodeServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use App\Library\Facades\AdminService;
use App\Library\Facades\SyncTaskService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;

/**
 * @Controller()
 */
class ImperfectController extends AbstractController
{
    /**
     * @Inject()
     * @var ValidatorFactoryInterface
     */
    private $validator;
    /**
     * @Inject()
     * @var ImperfectServiceInterface
     */
    private $ImperfectService;
    /**
     * @Inject()
     * @var WarehouseServiceInterface
     */
    private $WarehouseService;
    /**
     * @Inject()
     * @var UniqueCodeServiceInterface
     */
    private $UniqueCodeService;
    /**
     * @Inject()
     * @var AdminServiceInterface
     */
    private $AdminService;
    /**
     * @Inject()
     * @var MessageLogServiceInterface
     */
    private $MessageLogService;
    /**
     * @Inject()
     * @var SkuServiceInterface
     */
    private $SkuService;
    /**
     * @Inject()
     * @var SupplierServiceInterface
     */
    private $SupplierService;
    /**
     * @Inject()
     * @var BrandServiceInterface
     */
    private $BrandService;
    /**
     * @Inject()
     * @var SerialNoServiceInterface
     */
    private $SerialNoService;
    /**
     * @Inject()
     * @var TaskServiceInterface
     */
    private $TaskService;

    private function checkUniqueCodes($codes, $needNew = false)
    {
        // 重复店内码
        $repeatCodes = array_diff_assoc($codes, array_unique($codes));

        // 是否已标残
        $hasImpList = $this->ImperfectService->getImperfectS(['status_list' => [0, 1], 'unique_codes' => array_unique($codes)]);
        $hasImCodes = $hasImpList ? array_column($hasImpList, 'status', 'unique_code') : [];

        // 是否要新店内码
        if ($needNew == true) {
            $uqList = $this->UniqueCodeService->getUniqueCodes(array_unique($codes));
            $hasCodes = $uqList ? array_column($uqList, 'unique_code') : [];
        }

        $error = [];
        foreach ($codes as $code) {
            if (checkUniqueCodeFormat($code)) {
                $error[] = "{$code}格式错误";
            }
            if (in_array($code, $repeatCodes)) {
                $error[] = "{$code}重复";
            }
            if (array_key_exists($code, $hasImCodes)) {
                $error[] = "{$code}已有待审核或生效记录";
            }
            if ($needNew == true && in_array($code, $hasCodes)) {
                $error[] = "{$code}已存在，使用新店内码";
            }
        }

        return $error;
    }

    /**
     * 获取 已扫描货品数据
     * @RequestMapping(path="/imperfect/getScanData", methods="get,post")
     */
    public function getScanData()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $rule = [
                'w_id' => ['integer'],
                'code_type' => ['required', 'integer'],
                'codes' => ['required', 'array']
            ];
            $errors = $this->validator->make($params, $rule);
            if ($errors->fails()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
            }
            $codes = $params['codes'];
            $wId = (int)$params['w_id'];

            $data = [];
            $error = [];
            // 店内码
            if ($params['code_type'] == 1) {
                $error = $this->checkUniqueCodes($codes);
                if ($error) {
                    return $this->returnApi(ResponseCode::SERVICE_ERROR, implode("<br>", array_unique($error)));
                }
                foreach ($codes as $code) {
                    $data[] = [
                        'unique_code' => $code
                    ];
                }
            } else {
                // 条码
                $barcodes = [];
                $uniqueCodes = [];
                foreach ($codes as $k => $code) {
                    if (count(explode(',', $code)) != 2) {
                        $error[] = "{$code}格式错误，格式为 条码,店内码";
                    } else {
                        $barcodes[$k] = explode(',', $code)[0];
                        $uniqueCodes[$k] = explode(',', $code)[1];
                    }
                }
                if ($error) {
                    return $this->returnApi(ResponseCode::SERVICE_ERROR, implode("<br>", array_unique($error)));
                }

                // 新店内码校验
                $error = $this->checkUniqueCodes($uniqueCodes, true);
                if ($error) {
                    return $this->returnApi(ResponseCode::SERVICE_ERROR, implode("<br>", array_unique($error)));
                }

                // 匹配批次号
                $matchRes = $this->ImperfectService->matchCanImperInStockNo($wId, $barcodes);
                if ($matchRes['code'] != 0) {
                    return $this->returnApi(ResponseCode::SERVICE_ERROR, implode("<br>", $matchRes['data']));
                }
                $matchInStockNo = $matchRes['data'];

                // 匹配货架号
                $matchRes = $this->ImperfectService->matchCanImperShelf($wId, $barcodes);
                if ($matchRes['code'] != 0) {
                    return $this->returnApi(ResponseCode::SERVICE_ERROR, implode("<br>", $matchRes['data']));
                }
                $matchShelf = $matchRes['data'];

                $data = [];
                foreach ($codes as $k => $code) {
                    $data[] = [
                        'barcode' => $barcodes[$k],
                        'unique_code' => $uniqueCodes[$k],
                        'in_stock_no' => $matchInStockNo[$k]['in_stock_no'],
                        'shelf_code' => $matchShelf[$k]['shelf_code']
                    ];
                }
            }

            return $this->returnApi(ResponseCode::SUCCESS, $data);
        }

        return $this->returnApi(ResponseCode::SERVICE_ERROR, '请求错误');
    }

    /**
     * 添加
     * @RequestMapping(path="/imperfect/add", methods="get,post")
     */
    public function add()
    {
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->AdminService->organizeWareHouseData($userInfo['uid']);

        if ($this->isAjax()) {
            $params = $this->request->all();
            $rule = [
                'w_id' => ['required', 'integer'],
                'code_type' => ['required', 'integer', 'between:1,2'],
                'is_used' => ['required', 'integer', 'between:0,1'],
                'type' => ['required', 'integer', 'between:1,6'],
                'codeData' => ['required', 'array'],

                'categoryS' => ['required', 'array'],
                'remarkS' => ['array'],
                'image' => ['array']
            ];
            $message = [
                'w_id.required' => '请选择仓库',
                'codeData.required' => '请确定货品信息',
            ];
            $errors = $this->validator->make($params, $rule, $message);
            if ($errors->fails()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
            }
            if (!in_array($params['w_id'], $userWIds)) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无权操作此仓库');
            }

            $extend = ['admin_id' => $userInfo['uid'], 'admin_name' => $userInfo['nickname']];
            $checkRes = $this->checkData($params, $extend);
            if ($checkRes['code'] != 0) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $checkRes['msg']);
            }

            try {
                $result = $this->ImperfectService->batchCreate($checkRes['data']);
                if ($result) {
                    foreach ($result as $item) {
                        // 标残日志
                        $logData = [
                            'snow_id' => generateSnowId(),
                            'op_id' => $item['id'],
                            'status' => $item['status'],
                            'admin_id' => $extend['admin_id'],
                            'admin_name' => $extend['admin_name'],
                            'handle_time' => date('Y-m-d H:i:s'),
                            'handle_type' => '新增残次信息',
                            'remark' => ''
                        ];
                        wlog($logData['snow_id'], $logData);
                        // 店内码流程日志
                        if ($item['status'] == PublicCode::imperfect_status_verify_pass) {
                            $uqLineLog = [
                                'unique_code' => $item['unique_code'],
                                'operation_type' => in_array($item['type'], [1, 2]) ? PublicCode::UNIQUE_CODE_LOG_TYPE_MAP['change_to_original_imperfect'] : PublicCode::UNIQUE_CODE_LOG_TYPE_MAP['change_to_after_imperfect'],
                                'admin_id' => $extend['admin_id'],
                                'admin_name' => $extend['admin_name'],
                                'operation_desc' => '残次类型：' . PublicCode::imperfect_type[$item['type']],
                                'operation_time' => date('Y-m-d H:i:s'),
                                'snow_id' => newSnowId()
                            ];
                            addUniqueCodeLog($uqLineLog['snow_id'], $uqLineLog);
                        }
                    }
                }
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }

            return $this->returnApi(ResponseCode::SUCCESS, '创建成功');
        }

        $warehouse = $this->WarehouseService->getWarehouses(['ids' => $userWIds, 'status' => PublicCode::warehouse_status_valid], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];

        $imperfect_code_type = PublicCode::imperfect_code_type;
        $imperfect_used = PublicCode::imperfect_used;
        $imperfect_type = PublicCode::imperfect_type;
        $imperfect_category = json_encode($this->ImperfectService->getCategoryS(['status' => 1]), JSON_UNESCAPED_UNICODE);

        return $this->show('imperfect/add', [
            'warehouse_list' => $warehouse,
            'imperfect_code_type' => $imperfect_code_type,
            'imperfect_used' => $imperfect_used,
            'imperfect_type' => $imperfect_type,
            'imperfect_type_barcode' => PublicCode::imperfect_type_barcode,
            'imperfect_category' => $imperfect_category
        ]);
    }

    private function checkData(array $data, array $extend)
    {
        $skuMap = [];
        $uniqueCodes = array_column($data['codeData'], 'unique_code');

        if ($data['code_type'] == PublicCode::imperfect_code_type_unique) { // 店内码
            $error = $this->checkUniqueCodes($uniqueCodes);
            if ($error) {
                return ['code' => -1, 'msg' => implode('<br>', array_unique($error))];
            }

            $stockList = $this->UniqueCodeService->getUniqueCodes($uniqueCodes);
            switch ($data['type']) {
                case PublicCode::imperfect_type_arrival: // 到货原残
                    if ($stockList) {
                        return ['code' => -1, 'msg' => implode('、', array_column($stockList, 'unique_code')) . '系统已存在不能标到货原残'];
                    }
                    break;
                case PublicCode::imperfect_type_allot: // 调拨在途后残
                    $shelfUqList = collect($stockList)->groupBy(['unique_code'])->toArray();
                    foreach ($uniqueCodes as $code) {
                        if (!array_key_exists($code, $shelfUqList)) {
                            $error[] = "{$code}不存在";
                        } elseif ($shelfUqList[$code][0]['status'] != 2) {
                            $error[] = "{$code}状态不是出库状态";
                        } elseif (!$shelfUqList[$code][0]['in_w_id']) {
                            $error[] = "{$code}不是调拨出库";
                        } elseif ($data['w_id'] != $shelfUqList[$code][0]['in_w_id']) {
                            $error[] = "{$code}不是此仓库的在途店内码";
                        } else {
                            $skuMap[$code] = $shelfUqList[$code][0]['sku_id'];
                            $uniqueStockMap[$code] = $shelfUqList[$code][0];
                        }
                    }
                    if ($error) {
                        return ['code' => -1, 'msg' => implode('<br>', $error)];
                    }
                    break;
                default: // 其他标残
                    $stockCodeWIdMap = $stockCodeStaMap = $uniqueStockMap = [];
                    foreach ($stockList as $item) {
                        $stockCodeWIdMap[$item['unique_code']] = $item['w_id'];
                        $stockCodeStaMap[$item['unique_code']] = $item['status'];
                        $skuMap[$item['unique_code']] = $item['sku_id'];
                        $uniqueStockMap[$item['unique_code']] = $item;
                    }
                    foreach ($uniqueCodes as $code) {
                        if (!array_key_exists($code, $stockCodeWIdMap)) {
                            $error[] = "{$code}不存在";
                        } elseif ($data['w_id'] != $stockCodeWIdMap[$code]) {
                            $error[] = "{$code}不属于此仓库";
                        } elseif (!in_array($stockCodeStaMap[$code], [1, 2, 3])) {
                            $error[] = "{$code}库存状态不能标残";
                        } elseif ($stockCodeStaMap[$code] == 3) {
                            // 退返锁定不能标残
                            if ($uniqueStockMap[$code]['lock_type']
                                && explode('_', $uniqueStockMap[$code]['lock_type'])[0] == 'back') {
                                $error[] = "{$code}为退返锁定不能标残";
                            }
                        }
                    }
                    if ($error) {
                        return ['code' => -1, 'msg' => implode('<br>', $error)];
                    }
                    break;
            }
        } else { // 条码
            if ($data['type'] == PublicCode::imperfect_type_arrival) {
                return ['code' => -1, 'msg' => '条码类型不能标到货原残'];
            }

            $error = $this->checkUniqueCodes($uniqueCodes, true);
            if ($error) {
                return ['code' => -1, 'msg' => implode('<br>', array_unique($error))];
            }

            // 对应sku
            $barcodeSku = $this->SkuService->getSkuBarcodeMap(['barcodes' => array_unique(array_column($data['codeData'], 'barcode'))]);
            $skuMap = array_column($barcodeSku, 'sku_id', 'barcode');
            foreach ($data['codeData'] as $k => $datum) {
                if (!isset($skuMap[$datum['barcode']])) {
                    $error[] = "{$datum['barcode']}没对应的sku";
                } else {
                    $data['codeData'][$k]['sku_id'] = $skuMap[$datum['barcode']];
                }
            }
            if ($error) {
                return ['code' => -1, 'msg' => implode('<br>', array_unique($error))];
            }

            // 校验匹配的批次库存
            $error = $this->ImperfectService->checkMatchInStockNo((int)$data['w_id'], $data['codeData']);
            if ($error) {
                return ['code' => -1, 'msg' => implode('<br>', array_unique($error))];
            }
            // 校验匹配的货架库存
            $error = $this->ImperfectService->checkMatchShelf((int)$data['w_id'], $data['codeData']);
            if ($error) {
                return ['code' => -1, 'msg' => implode('<br>', array_unique($error))];
            }
        }

        // 检查：残次分类
        $categoryList = $this->ImperfectService->getCategoryS(['status' => 1, 'ids' => $data['categoryS']], ['id']);
        $hasCategory = $categoryList ? array_column($categoryList, 'id') : [];
        foreach ($data['categoryS'] as $category) {
            if (!in_array($category, $hasCategory)) {
                $error[] = $category;
            }
        }
        if ($error) {
            return ['code' => -1, 'msg' => '残次分类失效，请刷新页面'];
        }
        // 检查：备注格式
        if ($data['remarkS']) {
            foreach ($data['remarkS'] as $remark) {
                if (strlen($remark) > 255) {
                    $error[] = $remark;
                }
            }
        }
        if ($error) {
            return ['code' => -1, 'msg' => '备注最多不能超过255个字符'];
        }

        // 组合数据
        $status = in_array($data['type'], [PublicCode::imperfect_type_arrival, PublicCode::imperfect_type_allot])
            ? PublicCode::imperfect_status_verify_pass
            : PublicCode::imperfect_status_wait_verify;// 到货原残和调拨在途后残 免审
        $imperfectDetail = [];
        foreach ($data['categoryS'] as $key => $category) {
            $imperfectDetail[] = [
                'category_id' => $category,
                'remark' => $data['remarkS'][$key]
            ];
        }
        $saveData = [];
        foreach ($data['codeData'] as $datum) {
            $saveData[] = [
                'code_type' => $data['code_type'],
                'unique_code' => $datum['unique_code'],
                'barcode' => $data['code_type'] == 2 ? $datum['barcode'] : ($uniqueStockMap[$datum['unique_code']]['barcode'] ?? ''),
                'sku_id' => $data['code_type'] == 1 ? $skuMap[$datum['unique_code']] : $skuMap[$datum['barcode']],
                'w_id' => $data['w_id'],
                'in_stock_no' => $data['code_type'] == 2 ? $datum['in_stock_no'] : ($uniqueStockMap[$datum['unique_code']]['in_stock_no'] ?? ''),
                'shelf_code' => $data['code_type'] == 2 ? $datum['shelf_code'] : ($uniqueStockMap[$datum['unique_code']]['shelf_code'] ?? ''),
                'is_used' => $data['is_used'],
                'type' => $data['type'],
                'status' => $status,
                'image' => implode(';', $data['image']),
                'admin_id' => $extend['admin_id'],
                'admin_name' => $extend['admin_name'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),

                'detail_data' => $imperfectDetail
            ];
        }

        return ['code' => 0, 'data' => $saveData];
    }

    /**
     * 批量上传图片
     * @RequestMapping(path="/imperfect/upload", methods="get,post")
     */
    public function upload()
    {
        $file = $this->request->file('file')->toArray();
        if (!$file) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '未接收到图片');
        }

        $upload_path = 'runtime/upload/imperfect/';
        $image_name = $file['name'];
        $image_path = $file['tmp_file'];
        if (!is_dir($upload_path)) {
            @mkdir($upload_path, 0777, true);
        }
        $save_path = $upload_path . $image_name;

        move_uploaded_file($image_path, $save_path);

        $uniqueName = md5(time() . mt_rand(1, 10000));
        $up_res = upload($uniqueName . '.' . pathinfo($file['name'])['extension'], $save_path, 4);
        if (!$up_res) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '上传失败');
        }

        return $this->returnApi(ResponseCode::SUCCESS, '上传成功', ['url' => $up_res]);
    }

    /**
     * 列表
     * @RequestMapping(path="/imperfect/list", methods="get,post")
     */
    public function list()
    {
        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses([], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        // 残次状态
        $imperfect_status = PublicCode::imperfect_status;
        // 库存状态
        $stock_status = [1 => '在库', 2 => '出库', 3 => '锁定', 4 => '入库暂未生成库存'];
        // 残次类型
        $imperfect_type = PublicCode::imperfect_type;
        // 供应商
        $sups = $this->SupplierService->getSuppliers([], ['id', 'name']);
        $sups = $sups ? array_column($sups, 'name', 'id') : [];
        // 品牌
        $brands = $this->BrandService->getBrands([], ['id', 'name']);
        $brands = $brands ? array_column($brands, 'name', 'id') : [];

        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            if ($search['date_range']) {
                $search['start_time'] = explode(' ~ ', $search['date_range'])[0] . ' 00:00:00';
                $search['end_time'] = explode(' ~ ', $search['date_range'])[1] . ' 23:59:59';
            }

            $list = $this->ImperfectService->list(0, (int)$page, (int)$pageLimit, $search);
            if ($list['data']) {
                foreach ($list['data'] as &$item) {
                    $item['w_name'] = $warehouse[$item['w_id']];
                    $item['code_type_text'] = PublicCode::imperfect_code_type[$item['code_type']];
                    $item['status_text'] = $imperfect_status[$item['status']];
                    $item['type'] = $imperfect_type[$item['type']];
                    $item['stock_status'] = $stock_status[$item['stock_status']];
                }
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
        }

        return $this->show('imperfect/list', [
            'warehouse_list' => $warehouse,
            'imperfect_status' => $imperfect_status,
            'stock_status' => $stock_status,
            'imperfect_type' => $imperfect_type,
            'sup_list' => $sups,
            'brand_list' => $brands,
            'has_img' => [0 => '否', 1 => '是']
        ]);
    }

    /**
     * 列表导出
     * @RequestMapping(path="/imperfect/exportList", methods="get,post")
     */
    public function exportList()
    {
        if ($this->isAjax()) {
            // 用户
            $userInfo = $this->session->get('userInfo');

            $params = $this->request->all();
            $search = $params['search'] ?? [];
            if ($search['date_range']) {
                $search['start_time'] = explode(' ~ ', $search['date_range'])[0] . ' 00:00:00';
                $search['end_time'] = explode(' ~ ', $search['date_range'])[1] . ' 23:59:59';
            }

            $exportHeader = [
                'id' => 'ID',
                'sup_name' => '供应商',
                'w_name' => '仓库',
                'current_w_name' => '当前仓',
                'purchase_no' => '采购单号',
                'send_order_no' => '发货单号',
                'reserve_plan_no' => '订货计划',
                'brand_name' => '品牌',
                'spu_no' => '货号',
                'category_one' => '一级分类',
                'category_two' => '二级分类',
                'category_three' => '三级分类',
                'sex' => '性别',
                'season' => '季节',
                'sku' => 'sku',
                'code_type' => '码类型',
                'packag_size' => '包装尺码',
                'barcode' => '条码',
                'unique_code' => '店内码',
                'imperfect_status' => '残次状态',
                'imperfect_type' => '残次类型',
                'imperfect_score' => '残次分值',
                'imperfect_level' => '残次等级',
                'imperfect_detail' => '残次明细（部位=>原因=>程度）',
                'imperfect_remark' => '残次备注',
                'imperfect_image' => '残次图片',
                'stock_status' => '库存状态',
                'is_used' => '是否使用',
                'admin_name' => '创建人',
                'created_at' => '创建时间',
                'in_time' => '入库时间',
                'instore_time' => '到店时间',
                'spu_id' => 'SPU',
                'cost_price' => '成本价',
                'tag_price' => '吊牌价',
                'co_model' => '合作模式',
                'pur_name' => '采购商',
                'in_stock_no' => '批次号',
                'sale_price' => '销售价',
                //'kv_names' => '规格'
            ];
            $header = [
                'fields' => array_keys($exportHeader),
                'names' => array_values($exportHeader),
                'fix_specs' => [
                    'template' => ["商品尺码"],
                    'field' => 'kv_names',
                    'is_fixed' => true
                ]
            ];

            $data = [];
            $data['serial_no'] = $this->SerialNoService->generate(SerialType::WO_IMPERFECT);
            $data['name'] = '残次导出-' . date('YmdHis');
            $data['where'] = json_encode($search, JSON_UNESCAPED_UNICODE);
            $data['status'] = 1;
            $data['admin_id'] = $userInfo['uid'];
            $data['admin_name'] = $userInfo['nickname'];
            $data['header'] = json_encode($header, JSON_UNESCAPED_UNICODE);
            $data['type'] = 2;
            $data['service'] = 'imperfect/exportByPage';
            $data['is_limit'] = 1;
            $data['system'] = 'wms';
            try {
                $this->TaskService->addTask($data);
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }
            return $this->returnApi(ResponseCode::SUCCESS, '任务已创建，请到下载任务列表查看');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 详情
     * @RequestMapping(path="/imperfect/detail", methods="get,post")
     */
    public function detail()
    {
        try {
            $data = $this->ImperfectService->getImperfectDetail((int)$this->request->input('id'));
        } catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
        $info = [
            'id' => $data['imperfect']['id'],
            'status' => $data['imperfect']['status'],
            'code_type' => $data['imperfect']['code_type'],
            'code_type_text' => PublicCode::imperfect_code_type[$data['imperfect']['code_type']],
            'unique_code' => $data['imperfect']['unique_code'],
            'barcode' => $data['imperfect']['barcode'],
            'spu' => $data['spu_id'],
            'brand_name' => $data['brand_name'],
            'category' => $data['category'],
            'w_name' => $data['w_name'],
            'status_text' => PublicCode::imperfect_status[$data['imperfect']['status']],
            'admin_name' => $data['imperfect']['admin_name'],
            'created_at' => $data['imperfect']['created_at'],
            'is_used' => PublicCode::imperfect_used[$data['imperfect']['is_used']],
            'type' => $data['imperfect']['type'],
            'type_text' => PublicCode::imperfect_type[$data['imperfect']['type']],
            'level' => $data['level'],
            'detail' => $data['detail'],
            'images' => $data['imperfect']['image'] ? explode(';', $data['imperfect']['image']) : []
        ];
        // 日志
        $log = $this->MessageLogService->getLogList(['system' => 'wms', 'model_name' => 'Imperfect', 'op_id' => $info['id']]);
        $log_list = $log['data'] ? array_column($log['data'], 'res_params') : [];
        // 从日志里获取审核人、审核时间、审核备注 展示
        $verifyInfo = [];
        if ($log_list) {
            foreach ($log_list as $item) {
                if ($item['handle_type'] == '审核') {
                    $verifyInfo = $item;
                }
            }
        }
        // 当前用户是否有审核权限
        $userInfo = $this->session->get('userInfo');
        $verifyAuth = $this->AdminService->can($userInfo['uid'], '/imperfect/verify', getAppType());

        return $this->show('imperfect/detail', [
            'info' => $info,
            'verify_info' => $verifyInfo,
            'log' => $log_list,
            'verify_auth' => $verifyAuth
        ]);
    }

    /**
     * 审核
     * @RequestMapping(path="/imperfect/batchVerify", methods="post")
     */
    public function batchVerify()
    {
        if ($this->isAjax()) {
            // 当前用户是否有审核权限
            $userInfo = $this->session->get('userInfo');
            $verifyAuth = $this->AdminService->can($userInfo['uid'], '/imperfect/verify', getAppType());
            if (!$verifyAuth) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无权操作');
            }

            $params = $this->request->all();
            $rule = [
                'ids' => ['required', 'string'],
                'imperfect_verify_status' => ['required', 'integer', 'between:1,2'],
                'imperfect_remark' => ['string', 'max:255']
            ];
            $errors = $this->validator->make($params, $rule);
            if ($errors->fails()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
            }

            $wIds = AdminService::organizeWareHouseData($this->getUserId());

            $ids = explode(",",$params['ids']);
            // 审核操作
            try {
                $res = $this->ImperfectService->batchVerify($ids,[
                    'status' => $params['imperfect_verify_status'],
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                    'w_ids' => $wIds
                ]);
                if ($res) {
                    foreach ($ids as $id){
                        // 标残日志
                        $logData = [
                            'snow_id' => $this->request->getAttribute('snow_id').$id,
                            'op_id' => $id,
                            'status' => $params['imperfect_verify_status'],
                            'admin_id' => $userInfo['uid'],
                            'admin_name' => $userInfo['nickname'],
                            'handle_time' => date('Y-m-d H:i:s'),
                            'handle_type' => '审核',
                            'verify_remark' => $params['imperfect_remark'],
                            'remark' => "结果：" . PublicCode::imperfect_status[$params['imperfect_verify_status']] . "，备注：{$params['imperfect_remark']}"
                        ];
                        logger()->info("写入审核日志",[$logData,$ids]);
                        wlog((string)$logData['snow_id'].$id, $logData);
                    }
                }
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 审核
     * @RequestMapping(path="/imperfect/verify", methods="get,post")
     */
    public function verify()
    {
        if ($this->isAjax()) {
            // 当前用户是否有审核权限
            $userInfo = $this->session->get('userInfo');
            $verifyAuth = $this->AdminService->can($userInfo['uid'], '/imperfect/verify', getAppType());
            if (!$verifyAuth) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无权操作');
            }

            $params = $this->request->all();
            $rule = [
                'id' => ['required', 'integer'],
                'verify_status' => ['required', 'integer', 'between:1,2'],
                'verify_remark' => ['string', 'max:255']
            ];
            $errors = $this->validator->make($params, $rule);
            if ($errors->fails()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
            }

            // 残次是否存在
            $imperfect = $this->ImperfectService->getImperfect(['id' => $params['id']]);
            if (!$imperfect) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '残次记录不存在');
            }
            if ($imperfect['status'] != PublicCode::imperfect_status_wait_verify) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '残次状态已更新，请刷新页面');
            }

            $wIds = AdminService::organizeWareHouseData($this->getUserId());

            // 审核操作
            try {
//                $res = $this->ImperfectService->verify($params['id'], [
//                    'status' => $params['verify_status']
//                ]);
                $res = $this->ImperfectService->batchVerify([$params['id']],[
                    'status' => $params['verify_status'],
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                    'w_ids' => $wIds
                ]);
                if ($res) {
                    // 标残日志
                    $logData = [
                        'snow_id' => $this->request->getAttribute('snow_id'),
                        'op_id' => $params['id'],
                        'status' => $params['verify_status'],
                        'admin_id' => $userInfo['uid'],
                        'admin_name' => $userInfo['nickname'],
                        'handle_time' => date('Y-m-d H:i:s'),
                        'handle_type' => '审核',
                        'verify_remark' => $params['verify_remark'],
                        'remark' => "结果：" . PublicCode::imperfect_status[$params['verify_status']] . "，备注：{$params['verify_remark']}"
                    ];
                    wlog((string)$logData['snow_id'], $logData);
                    // 店内码流程日志
                    /*if ($params['verify_status'] == PublicCode::imperfect_status_verify_pass) {
                        $uqLineLog = [
                            'unique_code' => $imperfect['unique_code'],
                            'operation_type' => in_array($imperfect['type'], [1, 2]) ? PublicCode::UNIQUE_CODE_LOG_TYPE_MAP['change_to_original_imperfect'] : PublicCode::UNIQUE_CODE_LOG_TYPE_MAP['change_to_after_imperfect'],
                            'admin_id' => $userInfo['uid'],
                            'admin_name' => $userInfo['nickname'],
                            'operation_desc' => '残次类型：' . PublicCode::imperfect_type[$imperfect['type']],
                            'operation_time' => date('Y-m-d H:i:s'),
                            'snow_id' => newSnowId()
                        ];
                        addUniqueCodeLog($uqLineLog['snow_id'], $uqLineLog);
                    }*/
                }
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 取消店内码标残
     * @RequestMapping(path="/imperfect/cancel", methods="get,post")
     */
    public function cancel()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $rule = [
                'unique_codes' => ['required', 'array'],
                'reason' => ['required', 'integer', 'between:1,2'],
                'remark' => ['string', 'max:255']
            ];
            $errors = $this->validator->make($params, $rule);
            if ($errors->fails()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
            }

            $userInfo = $this->session->get('userInfo');
            $userWIds = $this->AdminService->organizeWareHouseData($userInfo['uid']);
            $unique_codes = array_filter(array_unique($params['unique_codes']));

            try {
                $result = $this->ImperfectService->cancel($unique_codes, $userWIds);
                if ($result['code'] != 0) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, implode('<br>', $result['data']));
                }
                $cancelIds = $result['data'];
                if ($cancelIds) {
                    foreach ($cancelIds as $id) {
                        $cancelReason = PublicCode::imperfect_cancel_reason[$params['reason']];
                        $logData = [
                            'snow_id' => generateSnowId(),
                            'op_id' => $id,
                            'status' => PublicCode::imperfect_status_cancel,
                            'admin_id' => $userInfo['uid'],
                            'admin_name' => $userInfo['nickname'],
                            'handle_time' => date('Y-m-d H:i:s'),
                            'handle_type' => '取消残次',
                            'remark' => "取消原因：{$cancelReason}，备注：{$params['remark']}"
                        ];
                        wlog($logData['snow_id'], $logData);
                    }
                }
                // 店内码流程日志
                foreach ($unique_codes as $unique_code) {
                    $uqLineLog = [
                        'unique_code' => $unique_code,
                        'operation_type' => PublicCode::UNIQUE_CODE_LOG_TYPE_MAP['cancel_mark_imperfect'], // 取消标残
                        'admin_id' => $userInfo['uid'],
                        'admin_name' => $userInfo['nickname'],
                        'operation_desc' => '取消标残',
                        'operation_time' => date('Y-m-d H:i:s'),
                        'snow_id' => newSnowId()
                    ];
                    addUniqueCodeLog($uqLineLog['snow_id'], $uqLineLog);
                }
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        $imperfect_cancel_reason = PublicCode::imperfect_cancel_reason;

        return $this->show('imperfect/cancel', [
            'imperfect_cancel_reason' => $imperfect_cancel_reason
        ]);
    }

    /**
     * 获取店内码生效标残（暂没用）
     * @RequestMapping(path="/imperfect/record", methods="get,post")
     */
    public function record()
    {
        $info = [];

        $params = $this->request->all();
        if (!(isset($params['unique_code']) && $params['unique_code'])) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
        }

        $record = $this->ImperfectService->getImperfect(['unique_code' => $params['unique_code'], 'status' => PublicCode::imperfect_status_verify_pass]);
        if ($record) {

            $info['unique_code'] = $record['unique_code'];
            $info['type'] = $record['type'];
            $info['type_text'] = PublicCode::imperfect_type[$record['type']];
            $info['is_used'] = $record['is_used'];
            $info['is_used_text'] = PublicCode::imperfect_used[$record['is_used']];

            $detail = $this->ImperfectService->getDetailById($record['id']);
            foreach ($detail as $val) {
                $info['category_list'][] = $this->ImperfectService->categoryList($val['category_id']);
            }

            $info['fix_text'] = '暂无';
        }

        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['info' => $info]);
    }
}