<?php
declare(strict_types=1);

namespace App\Controller;

use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\BlockServiceInterface;
use App\JsonRpc\ShelfServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;

/**
 * @Controller()
 */
class BlockController extends AbstractController
{
    /**
     * @Inject()
     * @var ValidatorFactoryInterface
     */
    private $validator;
    /**
     * @Inject()
     * @var WarehouseServiceInterface
     */
    private $WarehouseService;
    /**
     * @Inject()
     * @var ShelfServiceInterface
     */
    private $ShelfService;
    /**
     * @Inject()
     * @var BlockServiceInterface
     */
    private $BlockService;
    /**
     * @Inject()
     * @var AdminServiceInterface
     */
    private $AdminService;

    /**
     * 添加
     * @RequestMapping(path="/block/add", methods="get,post")
     */
    public function add()
    {
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->AdminService->organizeWareHouseData($userInfo['uid']);

        if ($this->isAjax()) {
            $params = $this->request->all();
            $rule = [
                'w_id' => ['required', 'integer'],
                'name' => ['required', 'string', 'max:50'],
                'shelf_codes' => ['required', 'array']
            ];
            $errors = $this->validator->make($params, $rule);
            if ($errors->fails()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
            }
            $snowId = $this->request->getAttribute('snow_id');

            // 仓库校验
            if (!in_array($params['w_id'], $userWIds)) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无权操作此仓库');
            }
            $warehouse = $this->WarehouseService->getWarehouse(['id' => $params['w_id'], 'status' => PublicCode::warehouse_status_valid], ['id']);
            if (!$warehouse) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '仓库不存在或已无效');
            }
            // 货架号校验
            $shelfRes = $this->checkData($params['w_id'], $params['shelf_codes']);
            if ($shelfRes['code'] != 0) {
                $errorRedisKey = "block_add:error:{$snowId}";
                redis()->setex($errorRedisKey, 3600, json_encode($shelfRes['data'], JSON_UNESCAPED_UNICODE));
                return $this->returnApi(ResponseCode::SERVER_ERROR, '错误', ['error_key' => $errorRedisKey]);
            }

            // 添加
            $addData = [
                'w_id' => $params['w_id'],
                'name' => $params['name'],
                'status' => PublicCode::block_status_valid,
                'admin_id' => $userInfo['uid'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            try {
                $this->BlockService->create($addData, $shelfRes['data']);
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }

            return $this->returnApi(ResponseCode::SUCCESS, '创建成功');
        }

        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses(['ids' => $userWIds, 'status' => PublicCode::warehouse_status_valid], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];

        return $this->show('block/add', [
            'warehouse_list' => $warehouse
        ]);
    }

    /**
     * 添加/编辑，有错下载
     * @RequestMapping(path="/block/downAddError", methods="get,post")
     */
    public function downAddError()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();

            $data = redis()->get($params['error_key']);
            if (!$data) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '过期，请重新提交');
            }
            $url = exportToExcel(['shelf' => '货架号', 'error' => '错误'], json_decode($data, true), '错误货架列表');
            return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 列表
     * @RequestMapping(path="/block/list", methods="get,post")
     */
    public function list()
    {
        // 用户
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->AdminService->organizeWareHouseData($userInfo['uid']);
        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        // 状态
        $blockStatus = PublicCode::block_status;

        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            $search['w_ids'] = $userWIds;
            $export = $params['export'] ?? 0;// 0列表 1导出

            $list = $this->BlockService->list($export, (int)$page, (int)$pageLimit, $search);
            if ($list['data']) {
                foreach ($list['data'] as &$item) {
                    $item['w_name'] = $warehouse[$item['w_id']];
                    $item['status_text'] = $blockStatus[$item['status']];
                }
            }

            if ($export) {
                if (!$list['data']) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
                }
                $url = exportToExcel($this->exportListHeader(), $list['data'], '存储区');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
        }

        return $this->show('block/list', [
            'warehouse_list' => $warehouse,
            'block_status' => $blockStatus
        ]);
    }

    private function exportListHeader()
    {
        return [
            'id' => 'ID',
            'w_name' => '仓库',
            'name' => '存储区',
            'shelf_codes' => '货架号',
            'status_text' => '状态',
            'created_at' => '创建时间'
        ];
    }

    /**
     * 作废
     * @RequestMapping(path="/block/cancel", methods="get,post")
     */
    public function cancel()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            if (!isset($params['id']) || !$params['id']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
            }

            $block = $this->BlockService->getBlock(['id' => $params['id']], ['id', 'w_id']);
            if (!$block) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '不存在');
            }
            $userInfo = $this->session->get('userInfo');
            $userWIds = $this->AdminService->organizeWareHouseData($userInfo['uid']);
            if (!in_array($block['w_id'], $userWIds)) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无权操作');
            }

            try {
                $this->BlockService->cancel((int)$params['id']);
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }

            return $this->returnApi(ResponseCode::SUCCESS, '作废成功');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 编辑
     * @RequestMapping(path="/block/edit", methods="get,post")
     */
    public function edit()
    {
        $id = (int)$this->request->input('id');
        $block = $this->BlockService->getDetailById($id);
        if (!$block) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '该存储区不存在');
        }
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->AdminService->organizeWareHouseData($userInfo['uid']);
        if (!in_array($block['w_id'], $userWIds)) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无权操作');
        }
        $block['shelf_codes'] = $block['shelf_codes'] ? explode(',', $block['shelf_codes']) : [];

        if ($this->isAjax()) {
            $params = $this->request->all();
            $rule = [
                'w_id' => ['required', 'integer'],
                'name' => ['required', 'string', 'max:50'],
                'status' => ['required', 'integer', 'between:0,1'],
                'shelf_codes' => ['required', 'array']
            ];
            $errors = $this->validator->make($params, $rule);
            if ($errors->fails()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
            }
            $snowId = $this->request->getAttribute('snow_id');

            // 仓库校验
            if (!in_array($params['w_id'], $userWIds)) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无权操作此仓库');
            }
            $warehouse = $this->WarehouseService->getWarehouse(['id' => $params['w_id'], 'status' => PublicCode::warehouse_status_valid], ['id']);
            if (!$warehouse) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '仓库不存在或已无效');
            }
            // 货架号校验
            $shelfRes = $this->checkData($params['w_id'], $params['shelf_codes']);
            if ($shelfRes['code'] != 0) {
                $errorRedisKey = "block_edit:error:{$snowId}";
                redis()->setex($errorRedisKey, 3600, json_encode($shelfRes['data'], JSON_UNESCAPED_UNICODE));
                return $this->returnApi(ResponseCode::SERVER_ERROR, '错误', ['error_key' => $errorRedisKey]);
            }

            // 编辑
            $upData = [
                'w_id' => $params['w_id'],
                'name' => $params['name'],
                'status' => $params['status']
            ];
            try {
                $this->BlockService->update($id, $upData, $shelfRes['data']);
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }

            return $this->returnApi(ResponseCode::SUCCESS, '编辑成功');
        }

        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        // 状态
        $blockStatus = PublicCode::block_status;

        return $this->show('block/edit', [
            'warehouse_list' => $warehouse,
            'block_status' => $blockStatus,
            'block' => $block
        ]);
    }

    private function checkData(int $w_id, array $data)
    {
        $error = [];

        // 重复值
        $unique_arr = array_unique($data);
        $repeat_arr = array_diff_assoc($data, $unique_arr);
        // 数据库校验
        $hasShelf = $this->ShelfService->getShelfS(['w_id' => $w_id, 'shelf_codes' => $data, 'status' => PublicCode::shelf_status_valid], ['id', 'shelf_code']);
        $hasShelf = $hasShelf ? array_column($hasShelf, 'shelf_code', 'id') : [];

        foreach ($data as $value) {
            if (in_array($value, $repeat_arr)) {
                $error[$value][] = '有重复值';
            }
            if (strlen($value) > 40) {
                $error[$value][] = '长度超过40个字符了';
            }
            $item = explode('-', $value);
            if (!in_array(count($item), [3, 4])) {
                $error[$value][] = '格式错误';
            }
            if (!in_array($value, $hasShelf)) {
                $error[$value][] = '不存在或已作废';
            }
        }

        $newError = [];
        if ($error) {
            foreach ($error as $shelf => $item) {
                $newError[] = [
                    'shelf' => $shelf,
                    'error' => implode('；', array_unique($item))
                ];
            }
        }

        if ($newError) {
            return ['code' => -1, 'data' => $newError];
        }

        return ['code' => 0, 'data' => array_keys($hasShelf)];
    }
}