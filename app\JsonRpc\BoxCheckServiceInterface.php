<?php
declare(strict_types=1);

namespace App\JsonRpc;

interface BoxCheckServiceInterface
{
    /**
     * 清点列表
     * @param int $export
     * @param int $page
     * @param int $pageSize
     * @param array $search
     * @return array
     */
    public function checkList(int $export = 0, int $page = 1, int $pageSize = 10, array $search);

    /**
     * 查单个
     * @param array $where
     * @param array $filed
     * @return \Hyperf\Database\Model\Builder|\Hyperf\Database\Model\Model|object|null
     */
    public function getOne(array $where = [], array $filed = ['*']);

    /**
     * 查多个
     * @param array $where
     * @param array $filed
     * @return array
     */
    public function getCheckS(array $where = [], array $filed = ['*']);

    /**
     * 更新
     * @param array $where
     * @param array $data
     * @return int
     */
    public function update(array $where, array $data);
}
