<?php
declare(strict_types=1);

namespace App\Controller;

use App\Library\Facades\SendService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;

use App\Constants\ResponseCode;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\BoxCheckServiceInterface;
use App\JsonRpc\BoxQualityServiceInterface;
use App\JsonRpc\BrandServiceInterface;
use App\JsonRpc\InStoreServiceInterface;
use App\JsonRpc\SupplierServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;

/**
 * @Controller
 */
class BoxQualityController extends AbstractController
{
    /**
     * @Inject()
     * @var ValidatorFactoryInterface
     */
    private $validator;

    /**
     * @Inject()
     * @var AdminServiceInterface
     */
    private $adminService;

    /**
     * @Inject()
     * @var WarehouseServiceInterface
     */
    private $warehouseService;

    /**
     * @Inject()
     * @var SupplierServiceInterface
     */
    private $supplierService;

    /**
     * @Inject()
     * @var BrandServiceInterface
     */
    private $brandService;

    /**
     * @Inject()
     * @var BoxQualityServiceInterface
     */
    private $boxQualityService;

    /**
     * @Inject()
     * @var BoxCheckServiceInterface
     */
    private $boxCheckService;

    /**
     * @Inject()
     * @var InStoreServiceInterface
     */
    private $inStoreService;

    const is_new = [ // 是否是新品
        0 => '否',
        1 => '是'
    ];
    const CHECK_STATUS_VERIFY = 2; // 清点待审核状态
    const CHECK_STATUS_OK = 1; // 清点完成状态
    const CHECK_STATUS_REJECT = 3; // 清点驳回状态

    /**
     * 首页
     * @RequestMapping(path="/boxQuality/list", methods="get,post")
     */
    public function list()
    {
        // 用户
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->adminService->organizeWareHouseData($userInfo['uid']);
        // 仓库列表
        $warehouse = $this->warehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        // 供应商
        $sups = $this->supplierService->getSuppliers([], ['id', 'name']);
        $sups = $sups ? array_column($sups, 'name', 'id') : [];
        // 品牌
        $brands = $this->brandService->getBrands([], ['id', 'name']);
        $brands = $brands ? array_column($brands, 'name', 'id') : [];
        // 人
        $users = $this->adminService->idsToNameList();

        return $this->show('boxQuality/list', [
            'warehouse_list' => $warehouse,
            'supplier_list' => $sups,
            'brand_list' => $brands,
            'user_list' => $users
        ]);
    }

    private function dateRange(string $dateRange)
    {
        $startTime = explode(' ~ ', $dateRange)[0] . ' 00:00:00';
        $endTime = explode(' ~ ', $dateRange)[1] . ' 23:59:59';
        return [$startTime, $endTime];
    }

    /**
     * 到货质检 - 列表/导出
     * @RequestMapping(path="/boxQuality/qualityList", methods="get,post")
     */
    public function qualityList()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            $search['data_type'] = 1; // 1质检 2质检明细
            if ($search['date_range']) {
                list($search['quality_at_start'], $search['quality_at_end']) = $this->dateRange($search['date_range']);
            }
            $userInfo = $this->session->get('userInfo');
            $userWIds = $this->adminService->organizeWareHouseData($userInfo['uid']);
            $search['w_ids'] = $userWIds;

            if ($params['export'] == 1) {
                $data = $this->boxQualityService->qualityList(1, 0, 0, $search)['data'];
                if (!$data) {
                    return $this->returnApi(ResponseCode::SERVER_ERROR, '无数据可导出');
                }
                $url = exportToExcel($this->qualityExportHeader(), $data, '到货质检导出');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            } else {
                $data = $this->boxQualityService->qualityList(0, (int)$page, (int)$pageLimit, $search);
                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $data['data'], ['count' => $data['total'], 'limit' => $pageLimit]);
            }
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 导出明细 - 质检数据
     * @RequestMapping(path="/boxQuality/qualityDetailExport", methods="get,post")
     */
    public function qualityDetailExport()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $search = $params['search'] ?? [];
            $search['data_type'] = 2; // 1质检 2质检明细
            if ($search['date_range']) {
                list($search['quality_at_start'], $search['quality_at_end']) = $this->dateRange($search['date_range']);
            }

            $data = $this->boxQualityService->qualityList(1, 0, 0, $search)['data'];
            if (!$data) {
                return $this->returnApi(ResponseCode::SERVER_ERROR, '无数据可导出');
            }
            $url = exportToExcel($this->qualityDetailExportHeader(), $data, '到货质检明细导出');
            return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 到货质检 - 子箱明细/导出
     * @RequestMapping(path="/boxQuality/qualityDetail", methods="get,post")
     */
    public function qualityDetail()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $qualityId = (int)$params['quality_id'];
            $tabType = $params['tab_type'];
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $export = $params['export'] ?? 0;
            if ($tabType == 1) { // 质检详情
                $data = $this->boxQualityService->getDetailS(['quality_id' => $qualityId]);
                if ($data) {
                    foreach ($data as &$datum) {
                        $datum['is_new_text'] = self::is_new[$datum['is_new']];
                    }
                }
                $header = ['barcode' => '条码', 'num' => '数量', 'is_new_text' => '是否新品'];
                $excelName = "ID$qualityId-质检详情导出";
            } else { // 入库详情
                $data = $this->inStoreService->getInDataByBoxQualityId($qualityId);
                if ($data) {
                    foreach ($data as &$datum) {
                        $datum['is_new_text'] = $datum['spu_id'] > 0 ? '否' : '是';
                    }
                }
                $header = ['barcode' => '条码', 'unique_code' => '店内码', 'is_new_text' => '是否新品'];
                $excelName = "ID$qualityId-入库详情导出";
            }

            if ($export == 1) {
                if (!$data) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
                }
                $url = exportToExcel($header, $data, $excelName);
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }
            $pageData = array_slice($data, ($page - 1) * $pageLimit, $pageLimit);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $pageData, ['count' => count($data), 'limit' => $pageLimit]);
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 清点 - 列表/导出
     * @RequestMapping(path="/boxQuality/checkList", methods="get,post")
     */
    public function checkList()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            $search['status'] = (int)$params['status'];
            if ($search['status'] == 1) {
                $search['no_quality'] = 1;
            }
            if ($search['date_range']) {
                list($search['quality_at_start'], $search['quality_at_end']) = $this->dateRange($search['date_range']);
            }
            $userInfo = $this->session->get('userInfo');
            $userWIds = $this->adminService->organizeWareHouseData($userInfo['uid']);
            $search['w_ids'] = $userWIds;

            if ($params['export'] == 1) {
                $data = $this->boxCheckService->checkList(1, 0, 0, $search)['data'];
                if (!$data) {
                    return $this->returnApi(ResponseCode::SERVER_ERROR, '无数据可导出');
                }
                $fileName = [0 => '清点暂存导出', 1 => '待质检导出', 2 => '差异待审导出', 3 => '差异驳回导出'];
                $url = exportToExcel($this->checkExportHeader($search['status']), $data, $fileName[$search['status']] ?? '清点导出');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            } else {
                $data = $this->boxCheckService->checkList(0, (int)$page, (int)$pageLimit, $search);
                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $data['data'], ['count' => $data['total'], 'limit' => $pageLimit]);
            }
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    private function qualityExportHeader()
    {
        return [
            'created_at' => '质检时间',
            'admin_name' => '质检人',
            'third_party_no' => '三方发货单号',
            'arrival_no' => '预约单号',
            'purchase_admin_name' => '负责人',
            'third_box_no' => '三方箱号',
            'box_no' => '我方箱号',
            'sub_box_no' => '拆箱子箱号',
            'is_same_sku_text' => '是否SKU装箱',
            'total_num' => '主箱货品数量',
            'check_num' => '主箱清点数量',
            'total_quality_num' => '主箱质检数',
            'check_admin_name' => '清点人',
            'quality_num' => '质检数量',
            'in_num' => '绑吊牌入库数量',
            'in_admin_name' => '绑定入库人',
            'tally_num' => '转架数量',
            'tally_admin_name' => '转架人',
            'reset_admin_name' => '重置人',
            'reset_at' => '重置时间',
            'is_has_new_text' => '是否含新品',
            'email_status_text' => '新品邮件是否发送',
            'email_admin_name' => '新品邮件发送人',
            'verify_admin_name' => '差异审核人',
            'verify_at' => '差异审核时间',
            'verify_remark' => '差异审核备注'
        ];
    }

    private function qualityDetailExportHeader()
    {
        $header = $this->qualityExportHeader();
        $header['barcode'] = '条码';
        $header['num'] = '数量';
        $header['normal_num'] = '正品数量';
        $header['imperfect_num'] = '残次数量';
        return $header;
    }

    private function checkExportHeader(int $checkStatus)
    {
        $header = [
            'check_at' => '清点时间',
            'admin_name' => '清点人',
            'third_party_no' => '三方发货单号',
            'arrival_no' => '预约单号',
            'purchase_admin_name' => '负责人',
            'third_box_no' => '三方箱号',
            'box_no' => '我方箱号',
            'num' => '货品数量',
            'check_num' => '清点数量'
        ];
        if ($checkStatus == self::CHECK_STATUS_VERIFY || $checkStatus == self::CHECK_STATUS_REJECT) {
            $header['email_status_text'] = '差异邮件是否发送';
            $header['email_admin_name'] = '差异邮件发送人';
            if ($checkStatus == self::CHECK_STATUS_REJECT) {
                $header['verify_admin_name'] = '差异审核人';
                $header['verify_remark'] = '差异审核备注';
            }
        }
        return $header;
    }

    /**
     * 清点 - 差异审核
     * @RequestMapping(path="/boxQuality/checkDiffVerify", methods="get,post")
     */
    public function checkDiffVerify()
    {
        if ($this->isAjax()) {
            $userInfo = $this->session->get('userInfo');
            $auth = $this->adminService->can($userInfo['uid'], '/boxQuality/checkDiffVerify', getAppType());
            if (!$auth) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无权操作');
            }

            $params = $this->request->all();
            if (!$params['ids']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '请选择要审核的差异记录');
            }
            if (!$params['verify_remark']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '请填写审核备注');
            }
            $checkData = $this->boxCheckService->getCheckS(['ids' => $params['ids'], 'status' => self::CHECK_STATUS_VERIFY], ['id']);
            if (!$checkData) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '没有需要审核的差异记录');
            }
            $checkIds = array_column($checkData, 'id');
            $noIds = [];
            foreach ($params['ids'] as $id) {
                if (!in_array($id, $checkIds)) {
                    $noIds[] = $id;
                }
            }
            if ($noIds) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, implode(',', $noIds) . '不是待审核状态');
            }

            $this->boxCheckService->update(['ids' => $params['ids']], [
                'status' => $params['verify'] == 1 ? self::CHECK_STATUS_OK : self::CHECK_STATUS_REJECT,
                'verify_admin_id' => $userInfo['uid'],
                'verify_admin_name' => $userInfo['nickname'],
                'verify_remark' => $params['verify_remark'],
                'verify_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 清点 - 重置质检差异（主箱清点数 = 主箱质检数）
     * @RequestMapping(path="/boxQuality/checkReset", methods="get,post")
     */
    public function checkReset()
    {
        if ($this->isAjax()) {
            $userInfo = $this->session->get('userInfo');
            $auth = $this->adminService->can($userInfo['uid'], '/boxQuality/checkReset', getAppType());
            if (!$auth) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无权操作');
            }

            $params = $this->request->all();
            if (!$params['id']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
            }
            $check = $this->boxCheckService->getOne(['id' => $params['id'], 'is_need_reset' => 1], ['id', 'quality_num']);
            if (!$check) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '没有需要重置的质检差异');
            }

            $this->boxCheckService->update(['id' => $check['id']], [
                'check_num' => $check['quality_num'],
                'reset_admin_id' => $userInfo['uid'],
                'reset_admin_name' => $userInfo['nickname'],
                'reset_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 新品邮件
     * @RequestMapping(path="/boxQuality/emailNew", methods="get,post")
     */
    public function emailNew()
    {
        $params = $this->request->all();
        $data = $this->boxQualityService->qualityList(1, 0, 0, [
            'data_type' => 1,
            'ids' => explode(',', $params['ids']),
            'is_has_new' => 1])['data'];
        if (!$data) {
            return $this->show('boxQuality/layerError', [
                'error_msg' => '没有新品数据可发'
            ]);
        }
        $users = $this->adminService->getUsers(['ids' => array_unique(array_column($data, 'purchase_admin_id'))], ['id', 'email']);
        $userEmails = array_filter(array_unique(array_column($users, 'email')));
        if (!$userEmails) {
            return $this->show('boxQuality/layerError', [
                'error_msg' => '没有收件人邮箱'
            ]);
        }
        $sendData = [];
        foreach ($data as $datum) {
            $sendData[] = [
                'id' => $datum['id'],
                'arrival_no' => $datum['arrival_no'],
                'sub_box_no' => $datum['sub_box_no'],
                'num' => $datum['quality_num'],
                'new_num' => $datum['new_num']
            ];
        }
        $arrivalNos = array_unique(array_column($sendData, 'arrival_no'));
        $arrivalNos = $arrivalNos ? implode(',', $arrivalNos) : '';
        $totalNewNum = array_sum(array_column($sendData, 'new_num'));
        $title = "预约单{$arrivalNos}新品件数{$totalNewNum}，请及时处理新品档案";

        return $this->show('boxQuality/newEmail', [
            'email_data' => $sendData,
            'email_users' => implode(',', $userEmails),
            'email_title' => $title
        ]);
    }

    /**
     * 新品邮件 - 发送
     * @RequestMapping(path="/boxQuality/sendEmailNew", methods="get,post")
     */
    public function sendEmailNew()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            if (!$params['email_data'] || !$params['email_users'] || !$params['email_title']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
            }
            $data = json_decode(json_encode($params['email_data'], 1), true);
            $emailUsers = explode(',', $params['email_users']);
            foreach ($emailUsers as $emailUser) {
                if (!filter_var($emailUser, FILTER_VALIDATE_EMAIL)) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, "{$emailUser}邮箱格式不正确");
                }
            }

            $sendParam = [];
            $sendParam['subject'] = $params['email_title'];
            $sendParam['to_email'] = $emailUsers[0];
            unset($emailUsers[0]);
            if ($emailUsers) {
                $sendParam['cc'] = $emailUsers;
            }
            $sendParam['tpl'] = 'emails/arrival/qualityNewSend';
            $sendParam['data'] = [
                'data' => $data,
                'remark' => $params['email_remark'],
            ];
            $result = SendService::email($sendParam);
            if (isset($result['success']) && $result['success'] == true) {
                // 更新表新品邮件发送字段
                $userInfo = $this->session->get('userInfo');
                $this->boxQualityService->update(['ids' => array_column($data, 'id')], [
                    'email_status' => 1,
                    'email_admin_id' => $userInfo['uid'],
                    'email_admin_name' => $userInfo['nickname'],
                    'email_remark' => $params['email_remark'],
                    'email_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                return $this->returnApi(ResponseCode::SUCCESS, '发送成功');
            }
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '发送失败');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 差异邮件
     * @RequestMapping(path="/boxQuality/emailDiff", methods="get,post")
     */
    public function emailDiff()
    {
        $params = $this->request->all();
        $data = $this->boxCheckService->checkList(1, 0, 0, [
            'status' => self::CHECK_STATUS_VERIFY,
            'ids' => explode(',', $params['ids']),
            'is_has_diff' => 1])['data'];
        if (!$data) {
            return $this->show('boxQuality/layerError', [
                'error_msg' => '没有差异数据可发'
            ]);
        }
        $users = $this->adminService->getUsers(['ids' => array_unique(array_column($data, 'purchase_admin_id'))], ['id', 'email']);
        $userEmails = array_filter(array_unique(array_column($users, 'email')));
        if (!$userEmails) {
            return $this->show('boxQuality/layerError', [
                'error_msg' => '没有收件人邮箱'
            ]);
        }
        $sendData = [];
        foreach ($data as $datum) {
            $sendData[] = [
                'id' => $datum['id'],
                'arrival_no' => $datum['arrival_no'],
                'box_no' => $datum['box_no'],
                'num' => $datum['num'],
                'diff_num' => $datum['diff_num']
            ];
        }
        $arrivalNos = array_unique(array_column($sendData, 'arrival_no'));
        $arrivalNos = $arrivalNos ? implode(',', $arrivalNos) : '';
        $totalDiffNum = array_sum(array_column($sendData, 'diff_num'));
        $title = "预约单{$arrivalNos}差异件数{$totalDiffNum}，请及时处理差异";

        return $this->show('boxQuality/diffEmail', [
            'email_data' => $sendData,
            'email_users' => implode(',', $userEmails),
            'email_title' => $title
        ]);
    }

    /**
     * 差异邮件 - 发送
     * @RequestMapping(path="/boxQuality/sendEmailDiff", methods="get,post")
     */
    public function sendEmailDiff()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            if (!$params['email_data'] || !$params['email_users'] || !$params['email_title']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
            }
            $data = json_decode(json_encode($params['email_data'], 1), true);
            $emailUsers = explode(',', $params['email_users']);

            $sendParam = [];
            $sendParam['subject'] = $params['email_title'];
            $sendParam['to_email'] = $emailUsers[0];
            unset($emailUsers[0]);
            if ($emailUsers) {
                $sendParam['cc'] = $emailUsers;
            }
            $sendParam['tpl'] = 'emails/arrival/checkDiffSend';
            $sendParam['data'] = [
                'data' => $data,
                'remark' => $params['email_remark'],
            ];
            $result = SendService::email($sendParam);
            if (isset($result['success']) && $result['success'] == true) {
                // 更新表差异邮件发送字段
                $userInfo = $this->session->get('userInfo');
                $this->boxCheckService->update(['ids' => array_column($data, 'id')], [
                    'email_status' => 1,
                    'email_admin_id' => $userInfo['uid'],
                    'email_admin_name' => $userInfo['nickname'],
                    'email_remark' => $params['email_remark'],
                    'email_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                return $this->returnApi(ResponseCode::SUCCESS, '发送成功');
            }
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '发送失败');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }
}