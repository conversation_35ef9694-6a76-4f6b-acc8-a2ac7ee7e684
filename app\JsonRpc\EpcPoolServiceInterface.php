<?php


namespace App\JsonRpc;


interface EpcPoolServiceInterface
{
    /**
     * 新增磁扣
     * @param array $data
     * @param array $adminInfo
     */
    public function add(array $data,array $adminInfo);

    /**
     * 磁扣池列表
     * @param array $where
     * @param int $page
     * @param int $limit
     */
    public function list($page=1,$limit=10,array $where=[]);

    /**
     * 标记状态
     * @param array $ids
     * @param $status
     * @param array $adminInfo
     */
    public function markStatus(array $ids,int $status,array $adminInfo);

    /**
     * 获取所有数据
     * @param $where
     * @return \Hyperf\Database\Model\Builder[]|\Hyperf\Database\Model\Collection
     */
    public function all($where=[]);
}