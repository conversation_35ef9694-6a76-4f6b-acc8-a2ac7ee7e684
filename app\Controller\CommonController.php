<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use App\Constants\ErrorCode;
use App\Constants\ResponseCode;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\AreaServiceInterface;
use App\JsonRpc\BrandServiceInterface;
use App\JsonRpc\CategoryServiceInterface;
use App\JsonRpc\LogisticsServiceInterface;
use App\JsonRpc\ShelfServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use FFI\Exception;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;
use App\Library\Facades\AdminService;
/**
 * @Controller()
 */
class CommonController extends AbstractController
{
    /**
     * @Inject()
     * @var \App\JsonRpc\SupplierServiceInterface
     */
    private $SupplierService;

    /**
     * @Inject()
     * @var \App\JsonRpc\WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject()
     * @var AdminServiceInterface
     */
    private $AdminService;

    /**
     * @Inject ()
     * @var LogisticsServiceInterface
     */
    private $LogisticsService;

    /**
     * @Inject ()
     * @var BrandServiceInterface
     */
    private $BrandService;

    /**
     * @Inject()
     * @var ShelfServiceInterface
     */
    private $ShelfService;

    /**
     * @Inject()
     * @var CategoryServiceInterface
     */
    private $CategoryService;

    /**
     * 获取所有下拉框数据
     * @RequestMapping(path="/common/getSelectData", methods="get,post")
     */
    public function getSelectData()
    {
        try {
            // 仓库
            // 地区
            $area = getAreaAll();
            // 快递公司
            $expresses = $this -> LogisticsService -> getLogisticsS(['type' => 3]); // 类型：1快递 2物流 3菜鸟快递公司
            $result = [
                'areas' => $area,
                'warehouses' => $this -> getAuthWarehouses(),
                'expresses' => $expresses
            ];
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }

    }

    /**
     * 获取所有下拉框数据
     * @RequestMapping(path="/common/getWarehouses", methods="get,post")
     */
    public function getWarehouses()
    {
        try {
            $result = $this -> getAuthWarehouses();
            foreach ($result as $k => &$v) {
                $v['value'] = $v['id'];
            }
            // 仓库
            $result = [
                'warehouses' => $result,
            ];
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }

    }

    /**
     * 获取当前用户权限下的仓库
     * @return mixed
     */
    private function getAuthWarehouses()
    {
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        //获取仓库信息
        return $this->WarehouseService->getWarehouses(['ids'=>$wIds], ['id', 'name']);
    }

    /**
     * 获取省市区下拉框数据
     * @RequestMapping(path="/common/getAreas", methods="get,post")
     */
    public function getAreas()
    {
        try {
            $result = [
                'areas' => getAreaAll(),
            ];
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }

    }

    /**
     * 获取品牌
     * @RequestMapping(path="/common/getBrands", methods="get,post")
     */
    public function getBrands()
    {
        try {
            $authBrandIds = getBrandIdsByAdminId(intval($this -> getUserId()));
            $result = $this -> BrandService ->getBrands(['ids'=>$authBrandIds]);
            foreach ($result as $k => &$v) {
                $v['value'] = $v['id'];
            }
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 获取所有下拉框数据
     * @RequestMapping(path="/common/getShelfLinesByWId", methods="get,post")
     */
    public function getShelfLinesByWId(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'w_id' => 'required|numeric|gt:0',
        ],[
            'w_id.required'        => '仓库ID必填',
        ]);

        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        try {
            $result = $this -> ShelfService -> getShelfLinesByWId($params['w_id']);
            var_dump("rsult=========",$result);
            foreach ($result as $k => &$v) {
                $v['name'] = $v['shelf_line'];
                $v['value'] = $v['shelf_line'];
            }
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 获取所有下拉框数据
     * @RequestMapping(path="/common/getCategoryTree", methods="get,post")
     */
    public function getCategoryTree(RequestInterface $request)
    {
        $result = $this -> CategoryService -> getCategoryTree();
        return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
    }

    /**
     * 获取所有admin
     * @RequestMapping(path="/common/adminList", methods="get,post")
     */
    public function adminList(RequestInterface $request)
    {
        $result = $this -> AdminService -> users();
        foreach ($result as &$v) {
            $v['name'] = $v['real_name'];
            $v['value'] = $v['id'];
        }
        return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
    }

    /**
     * 获取所有下拉框数据（货架排AB面合并分组）
     * @RequestMapping(path="/common/getMergedShelfLinesByWId", methods="get,post")
     */
    public function getMergedShelfLinesByWId(RequestInterface $request)
    {

        $params = $request -> all();
        $validator = validate()->make($params, [
            'w_id' => 'required|numeric|gt:0',
        ],[
            'w_id.required'        => '仓库ID必填',
        ]);

        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        try {
            $result = $this -> ShelfService -> getShelfLinesByWId($params['w_id']);

            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",makeMergedShelfLine ($result));
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 获取防重复点击令牌
     * @RequestMapping(path="/common/getIdempotenceToken", methods="get,post")
     */
    public function getIdempotenceToken(RequestInterface $request)
    {
        try {
            $userInfo = $this -> session -> get('userInfo');
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),['token' => getIdempotenceToken($userInfo['uid'])]);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 获取供应商列表
     * @RequestMapping(path="/common/getSuppliers", methods="get,post")
     */
    public function getSuppliers()
    {
        try {
            // 是否获取无效及过期状态合同的供应商： 0 否 1 是
            $supIds = getSupIdsByAdminBrand(intval($this->getUserId()), 0);
            $supWhere = ['status' => 1];
            if (!empty($supIds))  $supWhere["ids"] = $supIds;

            // 供应商
            logger()->info("权限供应商 supIds:",[$supWhere]);
            $result = $this->SupplierService->getSuppliers($supWhere, ['id', 'name']);

            foreach ($result as $k => &$v) {
                $v['value'] = $v['id'];
            }
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 上传图片
     * @RequestMapping(path="/common/upload", methods="get,post")
     */
    public function upload()
    {
        $file = $this->request->file('file')->toArray();
        if (!$file) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '上传文件不能为空！');
        }

        $uniqueName = md5(time() . mt_rand(1, 10000));
        $up_res = upload($uniqueName . '.' . pathinfo($file['name'])['extension'], $file['tmp_file'], 4);
        if (!$up_res) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '上传失败');
        }

        return $this->returnApi(ResponseCode::SUCCESS, '上传成功', ['url' => $up_res]);
    }

    /**
     * 获取当前用户
     * @RequestMapping(path="/common/currentUserInfo", methods="get,post")
     */
    public function currentUserInfo()
    {
        try {
            $userInfo = $this -> getUserInfo();
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$userInfo);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 新增日志
     * @RequestMapping(path="/common/setLog", methods="get,post")
     * @return mixed
     */
    public function setLog(RequestInterface $request)
    {
        try {
            $params = $request->all();
            $data = [];
            $data['snow_id'] = strval($request->getAttribute('snow_id'));
            $data['op_id'] = $params['id'];
            $data['req_router_name'] = $params['router_name'];
            $data['model_name'] = $params['model_name'];
            $data['remark'] = $params['remark'];
            wlog($data['snow_id'], $data);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        } catch (\Exception $exception) {
            return $this->returnApi(ErrorCode::SERVER_ERROR, $exception->getMessage());
        }
    }
    /**
     * 获取一级类目列表
     * @RequestMapping(path="/common/getCate1List", methods="get,post")
     */
    public function getCate1List()
    {
        $cateList = $this->CategoryService->getCategory(['parent_id'=>0]);
        $cateList = collect($cateList) -> map(function ($v) {
            return [
                'value' => $v['id'],
                'name' => $v['name'],
            ];
        })->toArray();
        return $this->returnApi(ErrorCode::SUCCESS, '操作成功', $cateList);
    }
}
