<?php

declare(strict_types=1);

namespace App\JsonRpc;


interface FindGoodsServiceInterface
{

    //寻货列表
    public function getFindGoodsList($where, int $page = 1, int $limit = 30);

    /**
     * 根据id获取任务
     * @param int $id 根据类型选填对应ID
     * @param int $op 【1:ID，2:serial_no】
     * @return mixed
     */
    public function getFindGoodsInfo($id,$op=1);

    //根据id获取详情列表
    public function getFindGoodsDetailList($params = [], $page = 1, $limit = 30);

    //创建
    public function createTask(array $params = []);

    //按品牌分组列表
    public function getFindGoodsBrandCountList($where, int $page = 1, int $limit = 30);

    /**
     * 执行操作
     * @param int $id 根据类型选填对应ID
     * @param array $data
     * @param int $type 【1任务单，2详细】
     * @return mixed
     */
    public function op($id = 0, $data = [],$type=1);

    public function zf($id = 0);

    /**
     * 根据店内码或条码获取商品信息
     * @param  string $code  店内码或条码获
     * @param int $op 1:条码,2:店内码
     * @return mixed
     */
    public function searchByCode($code,$op=1);

    public function recordLog(array $data=[]);

    public function getCodeByInfo($codes,$type=1,$w_id=0);

    public function getCodeByEpc($unique_code=[],$w_id=0);
}