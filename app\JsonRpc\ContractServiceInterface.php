<?php
declare(strict_types=1);

namespace App\JsonRpc;


/**
 * 供应商服务消费者
 */
Interface  ContractServiceInterface
{
    public function addContract(array $data);
    public function editContract(int $id, int $cvId, array $data);
    public function getEffectiveOne(int $id, int $cvId);
    public function getAuditOne(int $id, int $cvId);
    public function getContractList(int $page = 1, int $limit = 10, array $where = [], array $field = [], $cListType = 1);
    public function deleteContractOne(int $id);
    public function checkBrandAuth(array $data);
    public function getContractByNo(string $cNo = '');

    /**
     * 根据合同副表id更新合同审核状态
     * @param int $id
     * @param array $data
     * @return mixed
     */
    public function editContractViceById(int $cId,int $cvId, array $data);
    /**
     * 根据合同编号获取合同信息
     * @param string $contractNo 合同编号
     * @return mixed
     */
    public function getContractByCode(string $contractNo);
    /**
     * 获取过程合同数
     * @param array $where
     */
    public function getProceccingNum();
    /**
     * 获取一个（草稿，驳回）合同数据（编辑页面获取）
     * @param int $cId
     * @param int $cvId
     */
    public function getEditOne(int $cId, int $cvId);

    /**
     * 作废合同
     * @param int $cvId 合同副表ID
     * @param int $cType 合同类型，1=正式合同，2=审核合同
     */
    public function makeContractDie(int $cvId, int $cType);

    /**
     * 根据合同编号获取数据
     * @param string $contractNo 合同编号
     * @return array
     */
    public function getContractByCodes(string $contractNos);

    /**
     * 根据合同编号获取多条数据
     * @param string $contractNo 合同编号
     * @return array
     */
    public function getContractsByCodes(array $contractNos);

    public function getSupIdsByAdminBrand($adminId, $isCheckStatus);
}