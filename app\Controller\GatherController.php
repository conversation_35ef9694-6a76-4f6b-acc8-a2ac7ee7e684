<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use App\Service\JavaRpcService;
use App\Constants\ResponseCode;
use App\Exception\BusinessException;
use Hyperf\Validation\Rule;
use phpDocumentor\Reflection\PseudoTypes\False_;
use PhpParser\Node\Stmt\Throw_;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;
use Hyperf\HttpServer\Contract\RequestInterface;

/**
 * @Controller()
 */
class GatherController extends AbstractController
{

    /**
     * @Inject()
     * @var JavaRpcService
     */
    private $JavaRpcService;



    /**
     * 采集列表
     * @RequestMapping(path="/gother/list", methods="get,post")
     */
    public function list(RequestInterface $request)
    {
        $params = $request -> all();
        logger()->info('采集列表请求参数',['params'=>$params]);

        if ($request -> isMethod('POST')) {
            $page = $params['page'] ?? [];
            $limit = $params['limit'] ?? [];
            $wsId = $params['w_id'];
            if(empty($wsId)){
                //仓库必选
                return $this->returnApi(ResponseCode::SUCCESS, '请选择仓库', [], []);
            }

            if (!empty($params['create_date_range'])){
                $dateRangeArray = explode(' - ',$params['create_date_range']);
                $params['staTime'] = $dateRangeArray[0];
                $params['endTime'] = date('Y-m-d 23:59:59',strtotime($dateRangeArray[1]));
            }
            if (!empty($params['adminId'])){
                $params['adminIdList'] = explode(',',$params['adminId']);
            }

            //将参数拼接成json
            $data_json =array("page" => $page, "limit" => $limit,"wsId" => $wsId,"type" => $params['type'],"adminIdList" => $params['adminIdList'],"staTime" => $params['staTime'],"endTime" => $params['endTime'],"goodsType" => $params['goodsType'],"goodsTypeStr" => $params['goodsTypeStr']);
            $data =json_encode($data_json);

            $method ="POST";
            $url =$this ->JavaRpcService ->getStorageUrl() . "/api/gather/getPage";//接口url
            $token =$this ->session ->get('token');
            $result_json =$this ->JavaRpcService ->api_execute($method,$url,$data,$token);//获取接口返回值
            $result_data =$result_json['result'];
           // logger()->info('采集列表返回值：',['result_json'=>$result_json]);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $result_data['rows'], ['count' => $result_data['total'], 'limit' => $limit]);
        }

        $admins = $this -> getAdminList();
        return $this->show('gother/list',$admins);
    }

    /**
     * 采集明细列表
     * @RequestMapping(path="/gother/detail", methods="get,post")
     */
    public function detail(RequestInterface $request)
    {
        $params = $request -> all();
        $gdlId = $params['gdlId'];
        logger()->info('采集明细列表请求参数',['params'=>$params]);

        if ($request -> isMethod('POST')) {
            $page = $params['page'] ?? [];
            $limit = $params['limit'] ?? [];
            //将参数拼接成json
            $data_json = array("page" => $page, "limit" => $limit,"gdlId" => $gdlId);
            $data = json_encode($data_json);

            $method ="POST";
            $url =$this ->JavaRpcService ->getStorageUrl() . "/api/gather/getDetailPage";
            $token =$this ->session->get('token');
            $result_json =$this ->JavaRpcService ->api_execute($method,$url,$data,$token);
            $result_data =$result_json['result'];
            // logger()->info('采集明细列表返回值：',['result_json'=>$result_json]);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $result_data['rows'], ['count' => $result_data['total'], 'limit' => $limit]);
        }

        return $this->show('gother/detail',['gdlId' => $gdlId]);
    }

    /**
     * 采集导出
     * @RequestMapping(path="/gother/export", methods="post")
     */
    public function export(RequestInterface $request)
    {
        $params = $request -> all();
        logger()->info('采集导出请求参数',['params'=>$params]);

        if(empty($params['gdlId']) || $params['gdlId']==0){
            //导出1
            if(empty($params['w_id'])){
                //仓库必选
                return $this->returnApi(ResponseCode::SUCCESS, '请选择仓库', [], []);
            }

            if (!empty($params['create_date_range'])){
                $dateRangeArray = explode(' - ',$params['create_date_range']);
                $params['staTime'] = $dateRangeArray[0];
                $params['endTime'] = date('Y-m-d 23:59:59',strtotime($dateRangeArray[1]));
            }
            if (!empty($params['adminId'])){
                $params['adminIdList'] = explode(',',$params['adminId']);
            }

            $data_json = array("wsId" => $params['w_id'],"type" => $params['type'],"adminIdList" => $params['adminIdList'],"staTime" => $params['staTime'],"endTime" => $params['endTime'],"goodsType" => $params['goodsType'],"goodsTypeStr" => $params['goodsTypeStr']);
        }else{
            //导出2
            $data_json = array("gdlId" => $params['gdlId']);
        }
        $data =json_encode($data_json);
        //logger()->info('采集导出请求参数data:',['data'=>$data]);

        $method ="POST";
        $url =$this ->JavaRpcService ->getStorageUrl() . "/api/gather/exportData";
        $token =$this ->session ->get('token');
        $result_json =$this ->JavaRpcService ->api_execute($method,$url,$data,$token);
        $result_data=$result_json['result'];

        if($result_data){
            try {
                $oss_url = exportToExcel(config('file_header.gather_export'),$result_data,'采集明细数据');
            }catch (\Exception $e){
                throw new BusinessException('导出失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
            }
        }else{
            throw new BusinessException('无数据，导出失败！');
        }

        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $oss_url]);
    }


    //获取采集人列表
    public function getAdminList()
    {
        try {
            $methodGet ="GET";
            $urlGet =$this ->JavaRpcService ->getStorageUrl() . "/api/gather/getAdminList";
            $token =$this ->session->get('token');
            $result_json =$this ->JavaRpcService ->api_execute($methodGet, $urlGet,false,$token);
            $adminList = $result_json['result'];
            $admins = [
                'admin_list' => $adminList
            ];

            return $admins;
        } catch (\Exception $e) {
            logger()->info('getAdminList ---error:', $e);
            return [];
        }
    }

}
