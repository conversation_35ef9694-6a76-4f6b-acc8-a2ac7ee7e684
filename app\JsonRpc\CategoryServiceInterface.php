<?php
declare(strict_types=1);

namespace App\JsonRpc;

interface CategoryServiceInterface
{

    public function addCategory(array $info);

    public function editCategory(array $info, array $where);

    public function getCategoryList(array $where);

    public function getCategoryInfo(array $where);

    public function getCategoryId(array $where);

    public function categoryList(int $cId);

    public function checkCategory(array $data);

    public function getCategory(array $where);

    public function idNameMap(array $ids);

    /**
     * 获取类目树形结构
     */
    public function getCategoryTree();

    /**
     * 根据类目id批量获取对应路径path名称
     * @param array $cateIds
     * @return array
     */
    public function getCategoryNamePathByBatch(array $cateIds);

    /**
     * 根据品类获取所有相关的子集品类
     * @param array $category_ids
     * @return array
     */
    public function getChildCategory($category_ids = []);

    /**
     * 根据type获取类目树形结构
     */
    public function getCategoryTreeByParams(array $params);
}
