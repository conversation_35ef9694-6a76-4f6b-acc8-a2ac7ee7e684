<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use App\Constants\ErrorCode;
use App\Constants\ResponseCode;
use App\Exception\BusinessException;
use App\Exception\ValidateException;
use App\JsonRpc\TemplateSetServiceInterface;
use App\JsonRpc\UnitServiceInterface;
use App\Library\Facades\TemplateSetService;
use App\Request\SupplierRequest;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\Di\Annotation\Inject;

use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Request;
use Hyperf\HttpServer\Response;

/**
 * @Controller(prefix="/excel")
 */
class ExcelController extends AbstractController
{
    /**
     * @RequestMapping(path="getTplInfo", methods="get,post")
     * @return mixed
     */
    public function getTplInfo(RequestInterface $request) {
        $data = $this->validate($request->all());
        $tpl_ret = TemplateSetService::getTemplate($data['name']);
        if(empty($tpl_ret)){
            return $this->returnApi(ResponseCode::SERVER_ERROR, '模板信息不存在');
        }
        $table_tpl_list = json_decode($tpl_ret['table_header'],true);
        $tpl_list = [];
        foreach ($table_tpl_list as $key=>$table_tpl) {
            $tpl_list[$key]=$table_tpl['tpl_name'];
        }

        return $this->returnApi(ResponseCode::SUCCESS, '请求成功',$tpl_list);
    }

    /**
     * @RequestMapping(path="exportTpl", methods="get,post")
     * @return mixed
     */
    public function exportTpl(RequestInterface $request){
        $data = $this->validate($request->all(),'tpl');
        $tpl_ret = TemplateSetService::getTemplate($data['name']);
        if(empty($tpl_ret)){
            return $this->returnApi(ResponseCode::SERVER_ERROR, '模板信息不存在');
        }
        $table_header_list = json_decode($tpl_ret['table_header'],true);
        $table_headers = $table_header_list[$data['type']];
        if(empty($table_headers)){
            return $this->returnApi(ResponseCode::SERVER_ERROR, '模板不存在');
        }
        $fileName = $table_headers['tpl_name'].'.csv';
        return exportCsv(explode(',',$table_headers['val']),[],$fileName);
    }

    /**
     * 数据验证
     * @param array $data
     * @return array
     */
    protected function validate(array $data,$secne='default'){

        $message = [
            'name.required' => '模板标识不能为空!',
            'type.required' => '模板类型不能为空!',
            'type.numeric' => '模板类型参数有误!'
        ];
        $rules = [
            'name' => 'required',
            'type' => 'required|numeric'
        ];

        $secnes = [
            'default' => ['name'],
            'tpl' => ['name','type'],
        ];
        $useRule = [];
        if(isset($secnes[$secne])){
            foreach ($secnes[$secne] as $item){
                $useRule[$item] = $rules[$item];
            }
        }else{
            throw new BusinessException('验证场景值有误');
        }

        $validator = validate()->make(
            $data,
            $useRule,
            $message
        );

        return $validator->validate(); //验证数据有效性
    }
}



