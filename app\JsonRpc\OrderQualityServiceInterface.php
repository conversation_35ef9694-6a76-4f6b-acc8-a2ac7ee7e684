<?php
declare(strict_types=1);

namespace App\JsonRpc;

interface OrderQualityServiceInterface
{
    /**
     * 获取质检列表
     * @param array $where //条件
     * @param int $page //页码
     * @param int $pageSize //每页条数
     * @return mixed
     */
    public function getOrderQualityLists (array $where = [], int $page = 1, int $pageSize = 10);

    /**
     * 检测质检信息数据
     * @param array $where
     * @return mixed
     */
    public function checkOrderQualityInfo (array $where);

    /**
     * 处理质检结果信息
     * @param array $data
     * @return mixed
     */
    public function doQuality (array $data);
}