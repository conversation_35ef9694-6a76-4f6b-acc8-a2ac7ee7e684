<?php
declare(strict_types=1);

namespace App\Controller;

use App\Constants\CachePre;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Exception\BusinessException;
use App\JsonRpc\MessageLogServiceInterface;
use App\JsonRpc\ProduceAreaServiceInterface;
use App\JsonRpc\RebackHandoverServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use App\JsonRpc\AdminServiceInterface;

/**
 * @Controller()
 */
class RebackHandoverController extends AbstractController
{
    /**
     * @Inject()
     * @var AdminServiceInterface
     */
    private $adminService;
    /**
     * @Inject()
     * @var WarehouseServiceInterface
     */
    private $warehouseService;
    /**
     * @Inject()
     * @var RebackHandoverServiceInterface
     */
    private $rebackHandoverService;
    /**
     * @Inject()
     * @var MessageLogServiceInterface
     */
    private $messageLogService;
    private $statusMap = [
        -1 => '已作废',
        1 => '待接收',
        2 => '已回库',
        3 => '未见实物'
    ];

    /**
     * 列表
     * @RequestMapping(path="/rebackHandover/list", methods="get,post")
     */
    public function list()
    {
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->adminService->organizeWareHouseData($userInfo['uid']);

        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            $search['w_ids'] = $userWIds;
            $export = $params['export'] ?? 0;// 0列表 1导出
            if ($search['goods_search'] && $search['goods_content']) {
                $search[$search['goods_search']] = $search['goods_content'];
            }
            if ($search['date_range']) {
                $dateRange = explode(' ~ ', $search['date_range']);
                $search['created_at_start'] = $dateRange[0] . ' 00:00:00';
                $search['created_at_end'] = $dateRange[1] . ' 23:59:59';
            }

            if ($export == 1) {
                $listData = [];
                $sPage = 1;
                $sLimit = 2000;
                while (true) {
                    $data = $this->rebackHandoverService->getList(0, $sPage, $sLimit, $search);
                    if (!$data['data']) {
                        break;
                    }
                    $listData = array_merge($listData, $data['data']);
                    unset($data);
                    $sPage++;
                }
                if (!$listData) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
                }
                $url = exportToExcel($this->exportListHeader(), $listData, '回库交接记录');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }
            $list = $this->rebackHandoverService->getList(0, (int)$page, (int)$pageLimit, $search);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
        }

        $warehouse = $this->warehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];

        return $this->show('rebackHandover/list', [
            'warehouse_list' => $warehouse,
            'source_type_list' => PublicCode::produce_area_source_type,
            'status_list' => $this->statusMap,
            'user_list' => $this->adminService->idsToNameList(),
            'goods_search_list' => ['spu_nos' => '货号', 'barcodes' => '商品条码', 'unique_codes' => '店内码']
        ]);
    }

    private function exportListHeader()
    {
        return [
            'id' => 'ID',
            'w_name' => '仓库',
            'source_type_text' => '生产类型',
            'source_order_no' => '来源单号',
            'status_text' => '状态',
            'category_one' => '一级类目',
            'brand_name' => '品牌',
            'spu_no' => '货号',
            'unique_code' => '店内码',
            'remark' => '备注',
            'admin_name' => '创建人',
            'take_admin_name' => '接收人',
            'created_at' => '创建时间'
        ];
    }

    /**
     * 发起
     * @RequestMapping(path="/rebackHandover/add", methods="get,post")
     */
    public function add()
    {
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->adminService->organizeWareHouseData($userInfo['uid']);

        if ($this->isAjax()) {
            $params = $this->request->all();
            if (!$params['w_id']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
            }
            $cacheKey = CachePre::getKey(CachePre::REBACK_HANDOVER_ADD_LIST, $userInfo['uid'], $params['w_id']);
            $redisData = redis()->lrange($cacheKey, 0, -1);
            if (!$redisData) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '暂无数据');
            }
            $data = array_map(function ($item) {
                return json_decode($item, true);
            }, $redisData);
            try {
                $this->rebackHandoverService->batchAdd([
                    'code_data' => $data,
                    'remark' => $params['remark'] ?? '',
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                ]);
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $e->getMessage());
            }
            redis()->del($cacheKey);
            redis()->del($cacheKey . ':uni');
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        $warehouse = $this->warehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];

        return $this->show('rebackHandover/add', [
            'warehouse_list' => $warehouse
        ]);
    }

    /**
     * 发起 - 获取货品信息列表
     * @RequestMapping(path="/rebackHandover/addCodeList", methods="get,post")
     */
    public function addCodeList()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            if (!$params['w_id']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
            }
            $userInfo = $this->session->get('userInfo');
            $cacheKey = CachePre::getKey(CachePre::REBACK_HANDOVER_ADD_LIST, $userInfo['uid'], $params['w_id']);
            $redisData = redis()->lrange($cacheKey, 0, -1);
            $codeList = [];
            if ($redisData) {
                foreach ($redisData as $item) {
                    $codeList[] = json_decode($item, true);
                }
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $codeList);
        }

        return $this->returnApi(ResponseCode::REQUEST_ERROR, '非法请求');
    }

    /**
     * 发起 - 货品信息 - 扫描店内码/批量粘贴
     * @RequestMapping(path="/rebackHandover/addCodes", methods="get,post")
     */
    public function addCodes()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            if (!$params['unique_codes'] || !$params['w_id']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
            }
            // 校验店内码
            $res = $this->rebackHandoverService->addCheckUniqueCode($params['unique_codes'], (int)$params['w_id']);
            if ($res['error']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, implode("\n", $res['error']));
            }
            // 存缓存
            $error = [];
            $userInfo = $this->session->get('userInfo');
            $cacheKey = CachePre::getKey(CachePre::REBACK_HANDOVER_ADD_LIST, $userInfo['uid'], $params['w_id']);
            if (!redis()->exists($cacheKey)) {
                redis()->expireAt($cacheKey, strtotime(date('Y-m-d 23:59:59')));
                redis()->expireAt($cacheKey . ':uni', strtotime(date('Y-m-d 23:59:59')));
            } else {
                foreach ($res['data'] as $datum) {
                    if (redis()->sIsMember($cacheKey . ':uni', $datum['unique_code'])) {
                        $error[] = $datum['unique_code'] . "重复扫描";
                    }
                }
            }
            if ($error) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, implode("\n", $error));
            }
            foreach ($res['data'] as $datum) {
                ksort($datum);
                redis()->lpush($cacheKey, json_encode($datum, JSON_UNESCAPED_UNICODE));
                redis()->sAdd($cacheKey . ':uni', $datum['unique_code']);
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        return $this->returnApi(ResponseCode::REQUEST_ERROR, '非法请求');
    }

    /**
     * 发起 - 货品信息 - 删除
     * @RequestMapping(path="/rebackHandover/addCodeDelete", methods="get,post")
     */
    public function addCodeDelete()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            if (!$params['w_id'] || !$params['data']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
            }
            // 从缓存里删除
            $userInfo = $this->session->get('userInfo');
            $cacheKey = CachePre::getKey(CachePre::REBACK_HANDOVER_ADD_LIST, $userInfo['uid'], $params['w_id']);
            $redisData = redis()->lrange($cacheKey, 0, -1);
            foreach ($redisData as $datum) {
                if (!array_diff_assoc($params['data'], json_decode($datum, true))) {
                    redis()->lrem($cacheKey, $datum, 0);
                    redis()->sRem($cacheKey . ':uni', $params['data']['unique_code']);
                    break;
                }
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        return $this->returnApi(ResponseCode::REQUEST_ERROR, '非法请求');
    }

    /**
     * 详情
     * @RequestMapping(path="/rebackHandover/detail", methods="get,post")
     */
    public function detail()
    {
        $params = $this->request->all();
        $info = $this->rebackHandoverService->getOne((int)$params['id']);
        $log = $this->messageLogService->getLogList([
            'system' => 'wms',
            'model_name' => 'rebackHandover',
            'op_id' => $params['id'],
        ], 1, 9999);
        $log_list = $log['data'] ? array_column($log['data'], 'res_params') : [];

        return $this->show('rebackHandover/detail', [
            'info' => $info,
            'log_list' => $log_list
        ]);
    }

    /**
     * 接收
     * @RequestMapping(path="/rebackHandover/take", methods="get,post")
     */
    public function take()
    {
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->adminService->organizeWareHouseData($userInfo['uid']);

        if ($this->isAjax()) {
            $params = $this->request->all();
            if (!$params['w_id']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
            }
            $cacheKey = CachePre::getKey(CachePre::REBACK_HANDOVER_TAKE_LIST, $userInfo['uid'], $params['w_id']);
            $redisData = redis()->lrange($cacheKey, 0, -1);
            if (!$redisData) {
                return $this->returnApi(ResponseCode::SERVER_ERROR, '暂无数据');
            }
            $data = array_map(function ($item) {
                return json_decode($item, true);
            }, $redisData);
            try {
                $this->rebackHandoverService->batchTake([
                    'w_id' => $params['w_id'],
                    'code_data' => $data,
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                ]);
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $e->getMessage());
            }
            redis()->del($cacheKey);
            redis()->del($cacheKey . ':uni');
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        $warehouse = $this->warehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];

        return $this->show('rebackHandover/take', [
            'warehouse_list' => $warehouse
        ]);
    }

    /**
     * 接收 - 未接收列表数据
     * @RequestMapping(path="/rebackHandover/takeAddCodeList", methods="get,post")
     */
    public function takeAddCodeList()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            if (!$params['w_id']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
            }
            $addList = $this->rebackHandoverService->getList(1, 0, 0, ['w_id' => $params['w_id'], 'status' => 1])['data'];
            // 去掉已接收
            $userInfo = $this->session->get('userInfo');
            $cacheKey = CachePre::getKey(CachePre::REBACK_HANDOVER_TAKE_LIST, $userInfo['uid'], $params['w_id']);
            $redisData = redis()->lrange($cacheKey, 0, -1);
            if ($redisData) {
                $takeCodes = [];
                array_map(function ($datum) use (&$takeCodes) {
                    $arr = json_decode($datum, true);
                    $takeCodes[] = $arr['unique_code'];
                }, $redisData);
                foreach ($addList as $key => $datum) {
                    if (in_array($datum['unique_code'], $takeCodes)) {
                        unset($addList[$key]);
                    }
                }
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', array_values($addList), ['count' => count($addList)]);
        }

        return $this->returnApi(ResponseCode::REQUEST_ERROR, '非法请求');
    }

    /**
     * 接收 - 已接收列表数据
     * @RequestMapping(path="/rebackHandover/takeCodeList", methods="get,post")
     */
    public function takeCodeList()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            if (!$params['w_id']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
            }
            $userInfo = $this->session->get('userInfo');
            $cacheKey = CachePre::getKey(CachePre::REBACK_HANDOVER_TAKE_LIST, $userInfo['uid'], $params['w_id']);
            $redisData = redis()->lrange($cacheKey, 0, -1);
            $codeList = [];
            if ($redisData) {
                foreach ($redisData as $item) {
                    $codeList[] = json_decode($item, true);
                }
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $codeList, ['count' => count($codeList)]);
        }

        return $this->returnApi(ResponseCode::REQUEST_ERROR, '非法请求');
    }

    /**
     * 接收 - 货品信息 - 扫描店内码/批量粘贴
     * @RequestMapping(path="/rebackHandover/takeCodes", methods="get,post")
     */
    public function takeCodes()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            if (!$params['unique_codes'] || !$params['w_id']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
            }
            // 校验店内码
            $res = $this->rebackHandoverService->takeCheckUniqueCode($params['unique_codes'], (int)$params['w_id']);
            if ($res['error']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, implode("\n", $res['error']));
            }
            // 存缓存
            $error = [];
            $userInfo = $this->session->get('userInfo');
            $cacheKey = CachePre::getKey(CachePre::REBACK_HANDOVER_TAKE_LIST, $userInfo['uid'], $params['w_id']);
            if (!redis()->exists($cacheKey)) {
                redis()->expireAt($cacheKey, strtotime(date('Y-m-d 23:59:59')));
                redis()->expireAt($cacheKey . ':uni', strtotime(date('Y-m-d 23:59:59')));
            } else {
                foreach ($res['data'] as $datum) {
                    if (redis()->sIsMember($cacheKey . ':uni', $datum['unique_code'])) {
                        $error[] = $datum['unique_code'] . "重复扫描";
                    }
                }
            }
            if ($error) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, implode("\n", $error));
            }
            foreach ($res['data'] as $datum) {
                ksort($datum);
                redis()->lpush($cacheKey, json_encode($datum, JSON_UNESCAPED_UNICODE));
                redis()->sAdd($cacheKey . ':uni', $datum['unique_code']);
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        return $this->returnApi(ResponseCode::REQUEST_ERROR, '非法请求');
    }

    /**
     * 接收 - 货品信息 - 删除
     * @RequestMapping(path="/rebackHandover/takeCodeDelete", methods="get,post")
     */
    public function takeCodeDelete()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            if (!$params['w_id'] || !$params['data']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
            }
            // 从缓存里删除
            $userInfo = $this->session->get('userInfo');
            $cacheKey = CachePre::getKey(CachePre::REBACK_HANDOVER_TAKE_LIST, $userInfo['uid'], $params['w_id']);
            $redisData = redis()->lrange($cacheKey, 0, -1);
            foreach ($redisData as $datum) {
                if (!array_diff_assoc($params['data'], json_decode($datum, true))) {
                    redis()->lrem($cacheKey, $datum, 0);
                    redis()->sRem($cacheKey . ':uni', $params['data']['unique_code']);
                    break;
                }
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        return $this->returnApi(ResponseCode::REQUEST_ERROR, '非法请求');
    }

    /**
     * 作废/未见实物
     * @RequestMapping(path="/rebackHandover/updateStatus", methods="get,post")
     */
    public function updateStatus()
    {
        if ($this->isAjax()) {
            try {
                $params = $this->request->all();
                if (!$params['id'] || !$params['status']) {
                    throw new BusinessException('参数错误');
                }
                $this->rebackHandoverService->updateStatus((int)$params['id'], (int)$params['status']);
                // 日志
                $userInfo = $this->session->get('userInfo');
                $snowId = (string)$this->request->getAttribute('snow_id');
                wlog($snowId, [
                    'snow_id' => $snowId,
                    'op_id' => $params['id'],
                    'op_type' => $params['status'],
                    'op_type_text' => $this->statusMap[$params['status']],
                    'remark' => '',
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                    'op_time' => currentTime(),
                    'model_name' => 'rebackHandover',
                ]);
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $e->getMessage());
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        return $this->returnApi(ResponseCode::REQUEST_ERROR, '非法请求');
    }
}