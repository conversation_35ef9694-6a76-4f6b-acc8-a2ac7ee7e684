<?php

declare(strict_types=1);

namespace App\JsonRpc;

interface StockAdjustServiceInterface
{
    /**
     * 库存调整列表
     * @param $where
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getStockAdjustList($where, int $page = 1, int $limit = 10);

    //根据id获取调整单详情
    public function getStockAdjustInfo(int $id=0);

    //创建或更新库存调整单
    public function createStockAdjust(array $params=[]);

    /**
     * 审核
     * @param array $params
     * @param int $type 1为初审，2为复审
     */
    public function check($params=[],$type=1);


    public function doStockAdjust($id,$type);

}