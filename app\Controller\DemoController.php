<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;

/**
 * @Controller()
 */
class DemoController extends AbstractController
{

    /**
     * @Inject()
     * @var \App\JsonRpc\OrderServiceInterface
     */
    private $OrderService;

    /**
     * Demo页面
     * @RequestMapping(path="/demo", methods="get")
     */
    public function index()
    {
        if ($this->isAjax()) {
            $list = [['create_time'=>"2020-06-23 13:21:31",
                    'head_img'=>null,
                    'id'=>31,
                    'nickname'=>"门店业务员1",
                    'open_id'=>null,
                    'password'=>"8c6ece665c6dff672c4e12b0f7f05bef",
                    'salt'=>"srv5AxkSn5jJJkA1",
                    'status'=>1,
                    'update_time'=>"2021-01-23 21:03:13",
                    'username'=>"test"],
                ['create_time'=>"2020-06-23 13:21:31",
                    'head_img'=>null,
                    'id'=>31,
                    'nickname'=>"门店业务员2",
                    'open_id'=>null,
                    'password'=>"8c6ece665c6dff672c4e12b0f7f05bef",
                    'salt'=>"srv5AxkSn5jJJkA1",
                    'status'=>1,
                    'update_time'=>"2021-01-23 21:03:13",
                    'username'=>"test"]];
            return $this->returnApi('200', '操作成功', $list,['count' =>count($list), 'limit' => 1]);
        }
        return $this->show('demo/list',['token'=>time()]);
    }

    /**
     * Demo页面
     * @RequestMapping(path="/demo/info", methods="get,post")
     */
    public function info(){
        return $this->returnApi('200', '操作成功',["info"=>[["title"=>"兑奖周期","value"=>"第三期"],["title"=>"兑奖号码","value"=>"S123-765-4390-321"],["title"=>"兑奖人","value"=>"刘德青山在"],["title"=>"姓别","value"=>"男"]]]);
    }

    /**
     * Demo页面
     * @RequestMapping(path="/demo/log", methods="get,post")
     */
    public function log(){
        $log_list = [["id"=>"12","title"=>"操作了添加用户","time"=>"2021-03-01 12:43:28"],["id"=>"13","title"=>"操作了编辑用户","time"=>"2021-03-01 13:21:39"]];
        return $this->returnApi('200', '操作成功',$log_list,['count' =>count($log_list), 'limit' => 1]);
    }

    /**
     * Demo2页面，多列表切换
     * @RequestMapping(path="/demo2", methods="get")
     */
    public function index2()
    {
        if ($this->isAjax()) {
            var_dump($this->request->all());
            $list = [['create_time'=>"2020-06-23 13:21:31",
                'head_img'=>null,
                'id'=>17,
                'nickname'=>"门店业务员1",
                'open_id'=>null,
                'password'=>"8c6ece665c6dff672c4e12b0f7f05bef",
                'salt'=>"srv5AxkSn5jJJkA1",
                'status'=>1,
                'update_time'=>"2021-01-23 21:03:13",
                'username'=>"test"],
                ['create_time'=>"2020-06-23 13:21:31",
                    'head_img'=>null,
                    'id'=>31,
                    'nickname'=>"门店业务员2",
                    'open_id'=>null,
                    'password'=>"8c6ece665c6dff672c4e12b0f7f05bef",
                    'salt'=>"srv5AxkSn5jJJkA1",
                    'status'=>1,
                    'update_time'=>"2021-01-23 21:03:13",
                    'username'=>"test"]];
            return $this->returnApi('200', '操作成功', $list,['count' =>count($list), 'limit' => 1]);
        }
        return $this->show('demo/list2',['token'=>time()]);
    }

    /**
     * Demo2页面，多列表切换
     * @RequestMapping(path="/demo/distributionCode", methods="get")
     */
    public function distributionCode(){
        $this->OrderService->distributionCode();
    }

    /**
     * Demo2页面，多列表切换
     * @RequestMapping(path="/demo/distributionExpress", methods="get")
     */
    public function distributionExpress(){
        $this->OrderService->distributionExpress();
    }

    /**
     * 将支付半小时后的订单改为已接单
     * @RequestMapping(path="/demo/receiveOrders", methods="get")
     */
    public function receiveOrders(){
        $this->OrderService->receiveOrders();
    }

    /**
     * 解锁已取消订单分配的店内码和条形码
     * @RequestMapping(path="/demo/unlockCode", methods="get")
     */
    public function unlockCode(){
        $this->OrderService->unlockCode();
    }
}
