<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * 快递规则配置
 * Interface ExpressRuleServiceInterface
 * @package App\JsonRpc
 */
interface ExpressRuleServiceInterface
{
    /**
     * 创建快递规则
     * @param array $data
     * @return mixed
     */
    public function creteExpressRules (array $data);

    /**
     * 获取最大的规则id
     * @param array $where
     * @return mixed
     */
    public function getExpressMaxRuleId (array $where = []);

    /**
     *
     * @param array $where
     * @return mixed
     */
    public function getExpressRuleLists (array $where);

    /**
     * 修改快递规则
     * @param array $updateDate
     * @param array $insertDate
     * @return mixed
     */
    public function updateExpressRules (array $updateDate, array $insertDate);

    /**
     * 获取规则详情
     * @param int $id
     * @param array|string[] $field
     * @return mixed
     */
    public function getExpressRuleDetail (int $id, array $field = ["*"]);

    /**
     * 获取规则详情
     * @param int $ruleId
     * @param array|string[] $field
     * @return mixed
     */
    public function getExpressRuleDetailByRuleId(int $ruleId, array $field = ["*"]);
}