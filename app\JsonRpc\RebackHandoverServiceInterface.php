<?php
namespace App\JsonRpc;

interface RebackHandoverServiceInterface
{
    public function getList(int $all, int $page, int $pageSize, array $search);

    public function updateStatus($id, $status);

    public function addCheckUniqueCode(array $uniqueCodes, int $wId);

    public function batchAdd(array $data);

    public function getOne(int $id);

    public function takeCheckUniqueCode(array $uniqueCodes, int $wId);

    public function batchTake(array $data);
}
