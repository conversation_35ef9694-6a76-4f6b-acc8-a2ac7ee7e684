<?php

namespace App\Library\Facades;


use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\GoodsPackingServiceInterface;
use App\JsonRpc\InStoreServiceInterface;
use App\JsonRpc\LogisticsServiceInterface;
use Hyperf\Guzzle\ClientFactory;

class LogisticsService extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor ()
    {
        return LogisticsServiceInterface::class;
    }
}
