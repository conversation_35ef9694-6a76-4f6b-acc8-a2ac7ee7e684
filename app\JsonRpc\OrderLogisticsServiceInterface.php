<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * Interface OrderLogisticsServiceInterface
 * @package App\JsonRpc
 */
interface OrderLogisticsServiceInterface
{
    /**
     * 获取订单的发货信息
     * @param array $where
     */
    public function getLogisticsOrder(array $where);

    /**
     * 获取订单的发货记录
     * @param array $where
     * @param int $perPage
     * @param int $currentPage
     * @return array
     */
    public function getLogisticsList(array $where, int $perPage = 10, int $currentPage = 1);

    /**
     * wms - 制单 - 订单物流 - 作废物流信息
     * @param array $params
     * @return bool
     */
    public function cancelWmsOrderLogistics(array $params);

    /**
     * 保存wms的制单信息
     * @param array $data
     */
    public function saveOrderLogistics(array $data);
}
