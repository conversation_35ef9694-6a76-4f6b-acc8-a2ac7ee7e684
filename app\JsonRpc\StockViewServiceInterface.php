<?php
declare(strict_types=1);

namespace App\JsonRpc;

Interface  StockViewServiceInterface
{
    /**
     * 列表
     * @param $authWIds
     * @param $adminId
     * @param int $export
     * @param int $page
     * @param int $limit
     * @param array $where
     * @param array $field
     */
    public function getList($authWIds,$adminId, int $export = 0,int $page = 1, int $limit = 10, array $where = [], array $field = []);

    /**
     * 店内码查询列表
     * @param $authWIds
     * @param $adminId
     * @param $export
     * @param int $page
     * @param int $limit
     * @param array $where
     * @param array $field
     */
    public function getUniqueCodeList($authWIds,$adminId, $export = 0,int $page = 1, int $limit = 10, array $where = [], array $field = []);

    /**
     * 店内码查询列表(新)
     * @param $authWIds
     * @param $adminId
     * @param $export
     * @param int $page
     * @param int $limit
     * @param array $where
     * @param array $field
     */
    public function getUniqueCodeListNew($authWIds,$adminId, $export = 0,int $page = 1, int $limit = 10, array $where = [], array $field = []);

    /**
     * 获取店内码详情
     * @param string $uniqueCode
     */
    public function getUniqueCodeOne(string $uniqueCode);

    /**
     * 获取成本价列表
     * @param $wIds array 仓库id
     * @param $inStockNos array 批次列表
     * @param $skuIds array sku列表
     * @return \Hyperf\Database\Model\Builder[]|\Hyperf\Database\Model\Collection
     */
    public function costPriceList(array $wIds, array $inStockNos, array $skuIds);

    /**
     * 获取spu属性信息
     * @param $spuIds
     */
    public function getSpuProperties(array $spuIds);

    /**
     * 店内码查询列表总数
     * @param $authWIds
     * @param array $where
     */
    public function getUniqueCodeListTotal($authWIds,array $where = []);

    /**
     * 库存查询列表总数
     * @param $authWIds
     * @param array $where
     */
    public function getShelfStockListTotal($authWIds,array $where = []);

    /**
     * 手工锁定
     * @param $params
     */
    public function handLock($params);

    /**
     * 手工解锁
     * @param $params
     */
    public function handUnlock($params);

    /**
     * 获取店内码总数
     * @param $where
     * @return int
     */
    public function getUniqueTotal($where);

    /**
     * 获取店内码总数Ads
     * @param $where
     * @param $auth_w_ids
     * @return int
     */
    public function getUniqueTotalAds($where);

}