<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <link href="/static/layui/css/layui.css" rel="stylesheet"/>
    <link href="/static/css/pearCommon.css" rel="stylesheet"/>
    <link href="/static/css/myui.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/static/layui/formSelects-v4.css"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form layui-form-pane" action="">
            <div class="layui-row mt-7">
                <div class="layui-col-xs6">
                    <label class="layui-form-label">仓库</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <select name="w_id" lay-search>
                            <option value="">请选择</option>
                            @if ($warehouse_list)
                            @foreach ($warehouse_list as $key => $value)
                            <option value={{ $key }}>{{ $value }}</option>
                            @endforeach
                            @endif
                        </select>
                    </div>
                </div>
                <div class="layui-col-xs6">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-inline" style="width: 30%">
                        <select name="status" lay-search>
                            <option value="">请选择状态</option>
                            @if ($status_list)
                            @foreach ($status_list as $key => $value)
                            <option value={{ $key }}>{{ $value }}</option>
                            @endforeach
                            @endif
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-row mt-7">
                <div class="layui-col-xs6">
                    <label class="layui-form-label">客退日期</label>
                    <div class="layui-input-inline" style="width: 60%">
                        <input type="text" name="date_range" id="date_range" placeholder="开始时间 - 结束时间"
                               autocomplete="off" class="layui-input" id="pick-order-time" value="{{$default_date}}">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div style="display: flex;justify-content: flex-end;">
                    <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="query" id="daily_query"
                            style="margin: 5px">
                        <i class="layui-icon layui-icon-search"></i>
                        查询
                    </button>
                    <button type="reset" class="pear-btn pear-btn-md" style="margin: 5px">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button>
                    <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="export"
                            style="margin: 5px">
                        <i class="layui-icon layui-icon-normal"></i>
                        导出
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
<div class="layui-card">
    <div class="layui-card-body">
        <table id="dataTable" lay-filter="dataTable"></table>
    </div>
</div>

<script src="/static/layui/layui.js"></script>
<script>
    layui.use(['jquery', 'table', 'element', 'form', 'layer', 'iframeTools', 'laydate', 'formSelects'], function () {
        var table = layui.table;
        var $ = layui.jquery;
        var form = layui.form;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var iframeTools = layui.iframeTools;

        //表格初始化
        var dataTable = table.render({
            elem: '#dataTable'
            , url: "/dailyCustomerRefund/list"
            , method: 'post'
            , page: true //开启分页
            , skin: 'line'
            , toolbar: '#lay-toolbar'
            , cols: [[
                {field: 'created_at', title: '客退日期'}
                , {field: 'branch_serial_no', title: '订单号'}
                , {field: 'category_names', title: '类目'}
                , {field: 'brand_name', title: '品牌'}
                , {field: 'barcode', title: '条形码'}
                , {field: 'unique_code', title: '店内码'}
                , {field: 'shop_name', title: '仓库'}
                , {field: 'shelf_code', title: '当前货位'}
                , {field: 'status_text', title: '理货处理'}
            ]]
            , defaultToolbar: [{
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print']
            , done: function (res, curr, count) {
            }
        });
        //查询按钮
        var median;
        form.on('submit(query)', function (data) {
            if (median != null) {
                median.where = {};
            }
            table.reload('dataTable', {//重载
                where: {search: data.field},
                page: {curr: 1},
                done: function (res) {
                    median = this;
                }, page: {
                    curr: 1 //重新从第 1 页开始
                }
            });
            return false;
        });
        //导出按钮
        form.on('submit(export)', function (data) {
            $.ajax({
                url: '/dailyCustomerRefund/list',
                type: 'post',
                data: JSON.stringify({export: 1, search: data.field}),
                dataType: "json",
                contentType: 'application/json',
                processData: false,
                beforeSend: function () {
                    layer.msg('正在处理，请稍等', {icon: 1, time: 1800000})
                },
                success: function (data) {
                    if (data.code == 200) {
                        layer.msg(data.msg, {icon: 1, time: 3000}, function () {
                            layer.close(index);
                        })
                        window.open(data.data.url);
                    } else {
                        layer.msg(data.msg, {icon: 2, time: 10000})
                    }
                }
            });
            return false;
        });

        refresh = function (param) {
            table.reload('dataTable');
        };
        //时间插件
        laydate.render({
            elem: '#date_range'
            , type: 'date'
            , range: '~'
            , trigger: 'click'
        });
    })
</script>
</body>
</html>
