<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use App\Service\JavaRpcService;
use App\Constants\ResponseCode;
use Hyperf\Validation\Rule;
use phpDocumentor\Reflection\PseudoTypes\False_;
use PhpParser\Node\Stmt\Throw_;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;
use Hyperf\HttpServer\Contract\RequestInterface;

/**
 * @Controller()
 */
class EmpWorkController extends AbstractController
{

    /**
     * @Inject()
     * @var JavaRpcService
     */
    private $JavaRpcService;


    /**
     * 考勤岗位类型列表
     * @RequestMapping(path="/empWork/list", methods="get,post")
     */
    public function list(RequestInterface $request)
    {
        $params = $request -> all();
        logger()->info('考勤岗位类型列表请求参数',['params'=>$params]);

        if ($request -> isMethod('POST')) {
            $limit = $params['limit'] ?? [];

            //参数json
            $data =json_encode($params);
            //logger()->info('考勤岗位类型列表参数：',['data'=>$data]);
            $method ="POST";
            $url =$this ->JavaRpcService ->getStorageUrl() . "/api/emp/getTypePage";//接口url
            $token =$this ->session ->get('token');
            $result_json =$this ->JavaRpcService ->api_execute($method,$url,$data,$token);//获取接口返回值
            $result_data =$result_json['result'];
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $result_data['rows'], ['count' => $result_data['total'], 'limit' => $limit]);
        }

        $shop =$this ->getShopList();
        return $this->show('empWork/list',$shop);
    }

    /**
     * 考勤岗位类型:新增/修改/删除
     * @RequestMapping(path="/empWork/addOrUpdate", methods="get,post")
     */
    public function addOrUpdate(RequestInterface $request)
    {
        $params = $request -> all();
        logger()->info('考勤岗位类型请求参数',['params'=>$params]);

        if ($request -> isMethod('POST')) {
            $userInfo = $this->session->get('userInfo');
            $params['adminId'] =$userInfo['uid'];
            if(empty($params['delFlag'])){
                $admins = $this -> getAdminList(false);
                foreach ($admins['adminList'] as $admin){
                    if($admin['id'] == $params['auditId']){
                        $params['auditName'] =$admin['realName'];
                    }
                }
            }

            $data =json_encode($params);
            $method ="POST";
            $url =$this ->JavaRpcService ->getStorageUrl() . "/api/emp/addOrUpdate";//接口url
            $token =$this ->session ->get('token');
            $result_json =$this ->JavaRpcService ->api_execute($method,$url,$data,$token);//获取接口返回值
            $result_code =$result_json['code'];
            $result_msg =$result_json['msg'];
            return $this->returnApi($result_code,$result_msg);
        }

        $shop =$this ->getShopList();
        $params['shopList'] =$shop['shopList'];
        $params['adminList'] =[];
        if(!empty($params['shopId'])){
            $admins = $this -> getAdminList($params['shopId']);
            $params['adminList'] =$admins['adminList'];
        }
        logger()->info('考勤岗位类型GET请求返回参数',['params'=>$params]);
        return $this->show('empWork/addOrUpdate',$params);
    }

    /**
     * 考勤审核人列表
     * @RequestMapping(path="/empWork/adminList", methods="get")
     */
    public function adminList(RequestInterface $request)
    {
        $params = $request->all();
        logger()->info('考勤审核人列表请求参数', ['params' => $params]);
        $admins = $this -> getAdminList($params['shopId']);
        return $this->returnApi(ResponseCode::SUCCESS,'操作成功',$admins['adminList']);
    }


    /**
     * 考勤劳务费用列表
     * @RequestMapping(path="/empWork/costList", methods="get,post")
     */
    public function costList(RequestInterface $request)
    {
        $params = $request -> all();
        logger()->info('考勤劳务费用列表请求参数',['params'=>$params]);

        if ($request -> isMethod('POST')) {
            $limit = $params['limit'] ?? [];

            //参数json
            $data =json_encode($params);
            $method ="POST";
            $url =$this ->JavaRpcService ->getStorageUrl() . "/api/emp/getCostPage";//接口url
            $token =$this ->session ->get('token');
            $result_json =$this ->JavaRpcService ->api_execute($method,$url,$data,$token);//获取接口返回值
            $result_data =$result_json['result'];
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $result_data['rows'], ['count' => $result_data['total'], 'limit' => $limit]);
        }

        $shop =$this ->getShopList();
        return $this->show('empWork/costList',$shop);
    }

    /**
     * 考勤劳务费用:新增/修改
     * @RequestMapping(path="/empWork/costAddOrUpdate", methods="get,post")
     */
    public function costAddOrUpdate(RequestInterface $request)
    {
        $params = $request -> all();
        logger()->info('考勤劳务费用请求参数',['params'=>$params]);

        if ($request -> isMethod('POST')) {
            $userInfo = $this->session->get('userInfo');
            $params['adminId'] =$userInfo['uid'];

            $data =json_encode($params);
            $method ="POST";
            $url =$this ->JavaRpcService ->getStorageUrl() . "/api/emp/costAddOrUpdate";//接口url
            $token =$this ->session ->get('token');
            $result_json =$this ->JavaRpcService ->api_execute($method,$url,$data,$token);//获取接口返回值
            $result_code =$result_json['code'];
            $result_msg =$result_json['msg'];
            return $this->returnApi($result_code,$result_msg);
        }

        $shop =$this ->getShopList();
        $params['shopList'] =$shop['shopList'];
        logger()->info('考勤劳务费用GET请求返回参数',['params'=>$params]);
        return $this->show('empWork/costAddOrUpdate',$params);
    }


     //获取审核人列表
    public function getAdminList($params)
    {
        try {
            $data =array("shopId" => $params);
            logger()->info('获取审核人列表', ['params' => $params,'data' =>$data]);
            $urlGet =$this ->JavaRpcService ->getStorageUrl() . "/api/storage/base/getMeAdminList";
            $methodGet ="GET";
            $token =$this ->session ->get('token');
            $result_json =$this ->JavaRpcService ->api_execute($methodGet, $urlGet,$data,$token);
            $adminList = $result_json['result'];
            $admins = [
                'adminList' => $adminList
            ];

            return $admins;
        } catch (\Exception $e) {
            logger()->info('getAdminList ---error:', $e);
            return [];
        }
    }

    public function getShopList()
    {
        try {
            $urlGet =$this ->JavaRpcService ->getStorageUrl() . "/api/storage/base/getShopList";
            $methodGet ="GET";
            $token =$this ->session ->get('token');
            $result_json =$this ->JavaRpcService ->api_execute($methodGet, $urlGet,false,$token);
            $shopList = $result_json['result'];
            $shops = [
                'shopList' => $shopList
            ];

            return $shops;
        } catch (\Exception $e) {
            logger()->info('getShopList ---error:', $e);
            return [];
        }
    }


}
