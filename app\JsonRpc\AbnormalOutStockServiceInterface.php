<?php
declare(strict_types=1);

namespace App\JsonRpc;

Interface  AbnormalOutStockServiceInterface
{
    /**
     * 列表
     * @param int $page
     * @param int $limit
     * @param array $where
     */
    public function list(array $where = [], int $perPage = 1, int $currentPage = 10 );

    /**
     * 错下架单据
     * @param array $where
     * @return array
     */
    public function info(array $where = []);

    /**
     * 获取错下架详情
     * @param array $where
     * @param array|string[] $field
     * @return mixed
     */
    public function detail(array $where, array $field = ['*']);

    /**
     * 错下架详情列表
     * @param array $where
     * @param int $perPage
     * @param int $currentPage
     * @return mixed
     */
    public function AbnormalOutStockDetailList(array $where = [], int $perPage = 1, int $currentPage = 10 );

    /**
     * 获取错下架详情 一条记录
     * @param array $where
     * @param array|string[] $field
     * @return mixed
     */
    public function detailOne(array $where, array $field = ['*']);

    /**
     * 无需理货操作
     * @param array $params
     * @return bool
     */
    public function modifyAbnormalOutStockDetail(array $params);

    /**
     * 无需理货记录
     * @param array $where
     * @param array|string[] $field
     * @return array
     */
    public function noTallyDetailList(array $where, array $field = ['*']);

    /**
     * 获取无需理货记录 一条记录
     * @param array $where
     * @param array|string[] $field
     * @return mixed
     */
    public function noTallyDetailOne(array $where, array $field = ['*']);

    /**
     * 作废无需理货记录
     * @param array $data
     * @return bool
     */
    public function cancelNoTallyDetail(array $params);
}