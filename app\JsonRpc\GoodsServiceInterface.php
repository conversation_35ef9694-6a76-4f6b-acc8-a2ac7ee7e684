<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * Interface GoodsServiceInterface
 * @package App\JsonRpc
 */
interface GoodsServiceInterface
{
    public function channelSpuIsExist(int $spu_id, int $channel_id);
    public function createGoods(array $data);
    public function getGoodsInfoById(int $goods_id);
    public function updateGoods(int $goods_id, array $data);
    public function shopChannelGoodsList(int $export, int $page, int $pageLimit, array $search = []);
    public function goodsShopList(int $goods_id, int $status, int $page, int $pageLimit);
    public function getGoodsShopByIds(array $goods_ids, array $shop_ids = []);
    public function batchUpdateGoodsShop(array $update = [], array $insert = []);
    public function getAllGoodsTag(int $goods_id);
    // 查单个
    public function getGoods(array $where, array $filed = ['*']);
    // 查多个
    public function getGoodsS(array $where = [], array $filed = ['*']);

    public function getGoodsBySpuId(int $spu_id);

    /**
     * goods关联的线上平台-所有
     * @param array $goods_ids
     * @param array $shop_ids
     * @return array
     */
    public function getGoodsOnlineByIds(array $goods_ids, array $terminal_ids = []);

    /**
     * 批量修改goods_online
     * @param array $update
     * @param array $insert
     * @return bool
     */
    public function batchUpdateGoodsOnline(array $update = [], array $insert = []);

    /**
     * goods关联的线上渠道
     * @param int $goods_id
     * @param int $status
     * @param int $page
     * @param int $pageLimit
     * @return array
     */
    public function goodsOnlineList(int $goods_id, int $status);

    /**
     * goods关联的门店销售价
     * @param int $goods_id
     * @return array
     */
    public function goodsShopPrice(array $goods_ids);

    /**
     * goods线上销售价
     * @param int $goods_id
     * @return array
     */
    public function goodsOnlinePrice(array $goods_ids);

    /**
     * 获取商品门店价格
     * @param array $goods_ids
     * @param array $shop_ids
     * @return array
     */
    public function goodsShopSalePrice(array $goods_ids, array $shop_ids);

    public function goodsShopInfo(array $where = [], array $fileds = ['*']);
}
