<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Constants;

use Hyperf\Constants\AbstractConstants;
use Hyperf\Constants\Annotation\Constants;

/**
 * @Constants
 */
class ErrorCode extends AbstractConstants
{
    /**
     * @Message("success")
     */
    const SUCCESS = 200;

    /**
     * @Message("Server Error！")
     */
    const SERVER_ERROR = 32603;

    /**
     * @Message("Request Error！")
     */
    const REQUEST_ERROR = 32000;

    /**
     * @Message("DB Error！")
     */
    const DB_ERROR = 42000;

    /**
     * @Message("请选择上传文件！")
     */
    const REQUEST_FILE_ERROR = 42001;
}
