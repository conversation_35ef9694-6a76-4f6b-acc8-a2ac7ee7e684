<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * 统一导出任务
 */
interface TaskServiceInterface
{
    /**
     * 任务类型
     * @return mixed
     */
    public function type();

    /**
     * 创建任务
     * @param array $data
     * @return mixed
     */
    public function addTask($data = []);

    /**
     * 修改任务
     * @param array $where
     * @param array $data
     * @return bool
     */
    public function etitTask(array $where, array $data);

    /**
     * 获取任务列表
     * @param array $where
     */
    public function getTask(array $where, int $perPage = 10, int $currentPage = 1);

    /**
     * 获取详情
     * @param array $where
     * @return mixed
     */
    public function getDetail(array $where);

    /**
     * 获取数量
     * @param array $where
     * @return mixed
     */
    public function getCountNum(array $where);

    /**
     * 修改任务状态
     * @param array $where
     * @param array $data
     * @return bool
     */
    public function updateTask(string $serial_no, array $data);
}