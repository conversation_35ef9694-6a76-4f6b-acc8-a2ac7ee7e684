<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Controller;

use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Exception\BusinessException;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\UniqueCodeLogServiceInterface;
use App\Library\Facades\AdminService;
use App\Library\Facades\AllotService;
use App\Library\Facades\GoodsPackingService;
use App\Library\Facades\InStoreService;
use App\Library\Facades\LogisticsInfoService;
use App\Library\Facades\LogisticsService;
use App\Library\Facades\OutStoreService;
use App\Library\Facades\UniqueCodeService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Validation\Rule;

/**
 * @Controller(prefix="barcode")
 */
class BarcodeController extends AbstractController
{
    /**
     * @Inject ()
     * @var AdminServiceInterface
     */
    private $AdminService;

    /**
     * @Inject ()
     * @var UniqueCodeLogServiceInterface
     */
    private $UniqueCodeLogService;

    /**
     * @RequestMapping(path="create", methods="get,post")
     * @return mixed
     */
    public function create(RequestInterface $request){
        $data['title'] = "生成店内码";
        $prefix_list = UniqueCodeService::prefixList();

        $data = [
            'prefix_list' => collect($prefix_list)->keyBy('id')
        ];
        return $this->show('barcode/create', $data);
    }

    /**
     * @RequestMapping(path="add_prefix", methods="post")
     * @return mixed
     */
    public function addPrefix(RequestInterface $request){
        $params = $this->validate($request->all(),'add_prefix');
        $userInfo = $this->session->get('userInfo');
        $params['prefix'] = strtoupper($params['prefix']);
        $params['admin_id'] = $userInfo['uid'];
        $params['admin_name'] = $userInfo['nickname'];
        UniqueCodeService::addPrefix($params);
        return $this->returnApi(ResponseCode::SUCCESS, '创建成功');
    }


    /**
     * @RequestMapping(path="add_batch", methods="post")
     * @return mixed
     */
    public function addBatch(RequestInterface $request){
        $params = $this->validate($request->all(),'add_batch');
        $userInfo = $this->session->get('userInfo');
        $params['admin_id'] = $userInfo['uid'];
        $params['admin_name'] = $userInfo['nickname'];
        UniqueCodeService::addBatch($params);
        return $this->returnApi(ResponseCode::SUCCESS, '创建成功');
    }



    /**
     * 详情页列表
     * @RequestMapping(path="down_code", methods="get,post")
     * @return mixed
     */
    public function downCode(RequestInterface $request){
        $params = $this->validate($request->all(),'down_code');
        $id = intval($params['id']);
        $codeList = UniqueCodeService::downloadUniqueCode($id);
        if(empty($codeList)){
            throw new BusinessException('无数据，导出失败！');
        }else{
            $newCodeList = [];
            foreach ($codeList as $code) {
                $newCodeList[] = ['code' => $code];
            }
            return exportCsv(['code'=>'店内码'], $newCodeList, '店内码列表.txt');
        }
    }


    /**
     * @RequestMapping(path="list", methods="get,post")
     * @return mixed
     */
    public function list(RequestInterface $request)
    {
        $data['title'] = '店内码管理';

        if ($this->isAjax()) {
            $where = $this->validate($request->all(),'search');
            logger()->debug('list_where',$where);
            $page = intval($request->input('page',1));
            $limit = intval($request->input('limit',30));
            $list = UniqueCodeService::list($page, $limit, $where);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $limit]);
        }

        $data = [
            'prefix_list' => UniqueCodeService::prefixList()
        ];

        return $this->show('barcode/list', $data);
    }

    /**
     * @RequestMapping(path="export", methods="post")
     * @return mixed
     */
    public function export(RequestInterface $request)
    {
        $where = $this->validate($request->all(),'search');
        $list = UniqueCodeService::list(0, 0, $where);
        if(empty($list['data'])){
            throw new BusinessException('无数据，导出失败！');
        }else{
            $url = exportToExcel(config('file_header.barcode_prefix_export'),$list['data'],'店内码列表');
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
    }

    /**
     * 数据验证
     * @param array $data
     * @return array
     */
    protected function validate(array $data,$secne='default'){

        $message = [
            'prefix_id.numeric' => '前缀格式有误!',
            'id.numeric' => '批次id格式有误!',
            'number.numeric' => '生成数量必须为数值!',
            'number.min' => '生成数量必须大于1!',
            'number.max' => '生成数量必须小于等于50000!',
            'id.required' => '批次id不能为空!',
            'prefix.required' => '前缀不能为空!',
            'prefix.regex' => '前缀必须为2-3位字母组合!',
            'length.required' => '数字位数不能为空!',
            'length.numeric' => '数字位数必须为数值!',
            'length.min' => '数字位数必须大于等于8!',
            'length.max' => '数字位数必须小于等于10!',
        ];
        $rules = [
            'prefix' => 'required|regex:/^[A-Za-z]{2,3}$/',
            'length' => 'required|numeric|min:8|max:10',
            'remark' => 'string',
            'prefix_id' => 'numeric',
            'id' => 'required|numeric',
            'number' => 'numeric|min:1|max:50000',
        ];

        $secnes = [
            'search' => ['prefix_id'],
            'down_code' => ['id'],
            'add_prefix' => ['prefix','length','remark'],
            'add_batch' => ['prefix_id','number'],
            'detail_search' => ['id','search_type','search_value'],
        ];
        $useRule = [];
        if(isset($secnes[$secne])){
            foreach ($secnes[$secne] as $item){
                $useRule[$item] = $rules[$item];
            }
        }else{
            throw new BusinessException('验证场景值有误');
        }

        $validator = validate()->make(
            $data,
            $useRule,
            $message
        );

        return $validator->validate(); //验证数据有效性
    }
}