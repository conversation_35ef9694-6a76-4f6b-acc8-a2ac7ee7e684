<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * 品牌服务
 */
interface BrandServiceInterface
{
    /**
     * 修改商品信息
     * @param int $goods_id
     * @param array $data
     * @return bool
     */
    public function editGoods (int $goods_id, array $data);

    /**
     * @param array $data 品牌数据 【字段名称和表对应】
     * @return mixed
     */
    public function addGoodsBrand (array $data);

    /**
     * @param int $brandId 品牌ID
     * @param array $data 要修改的字段 【字段名称和表相对应】
     * @return mixed
     */
    public function updateGoodsBrand (int $brandId, array $data);

    /**
     * @param array $where 查询条件 【特殊字段 brand_names<list> => name】其余参考表字段
     * @param int $page 当前页
     * @param int $limit 每页条数
     * @param string $orderByField 排序字段
     * @param string $orderByType 排序类型
     * @return mixed
     */
    public function getGoodsBrands (array $where = [], int $page = 1, int $limit = 10, string $orderByField = 'id', string $orderByType = 'asc');

    /**
     * @param int $brandId 商品ID
     * @return mixed
     */
    public function getGoodsBrandDetail (int $brandId);

    /**
     * @param int $brandId
     * @return mixed
     */
    public function deleteGoodsBrand (int $brandId);


    public function checkGoodsBrands (array $data);

    public function getBrandRowById (int $brand_id, string $row);

    /**
     * 查单个
     * @param array $where
     * @param array|string[] $field
     * @return mixed
     */
    public function getBrand (array $where, array $field = ['*']);

    /**
     * 查多个
     * @param array $where
     * @param array|string[] $field
     * @return mixed
     */
    public function getBrands (array $where = [], array $field = ['id', 'name']);

    /**
     * 根据品牌名称获取品牌
     * @param string $name
     * @param array|string[] $field
     * @return mixed
     */
    public function getBrandByName (string $name, array $field = ['*']);
}