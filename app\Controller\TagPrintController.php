<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Controller;

use App\Constants\ErrorCode;
use App\Constants\CachePre;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Constants\SerialType;
use App\Exception\BusinessException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Validation\Rule;
use App\Library\Facades\AdminService;
use App\Library\Facades\HashService;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;

/**
 * @Controller
 */
class TagPrintController extends AbstractController
{

    /**
     * @Inject()
     * @var ValidatorFactoryInterface
     */
    private $validator;
    /**
     * @Inject()
     * @var \App\JsonRpc\WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject()
     * @var \App\JsonRpc\TagPrintServiceInterface
     */
    private $TagPrintService;

    /**
     * @Inject()
     * @var \App\JsonRpc\EpcServiceInterface
     */
    private $EpcService;

    /**
     * @Inject()
     * @var \App\JsonRpc\UniqueCodeServiceInterface
     */
    private $UniqueCodeService;

    /**
     * @Inject()
     * @var \App\JsonRpc\SkuBarcodeServiceInterface
     */
    private $SkuBarcodeService;

    /**
     * @Inject()
     * @var \App\JsonRpc\PriceServiceInterface
     */
    private $PriceService;

    /**
     * @Inject()
     * @var \App\JsonRpc\SkuServiceInterface
     */
    private $SkuService;

    /**
     * @Inject()
     * @var \App\JsonRpc\SpuServiceInterface
     */
    private $SpuService;

    /**
     * @Inject()
     * @var \App\JsonRpc\UnitServiceInterface
     */
    private $UnitService;

    /**
     * @Inject()
     * @var \App\JsonRpc\ShopServiceInterface
     */
    private $ShopService;

    /**
     * @Inject()
     * @var \App\JsonRpc\GoodsServiceInterface
     */
    private $GoodsService;

    /**
     * @Inject()
     * @var \App\JsonRpc\PromotionServiceInterface
     */
    private $PromotionService;

    /**
     * @Inject
     * @var \App\JsonRpc\ShelfServiceInterface
     */
    private $ShelfService;

    /**
     * @Inject
     * @var \App\JsonRpc\SkuEpcCodeServiceInterface
     */
    private $SkuEpcCodeService;

    /**
     * @Inject
     * @var \App\JsonRpc\ShelfGoodsCodeMapServiceInterface
     */
    private $ShelfGoodsCodeMapService;

    /**
     * @Inject
     * @var \App\JsonRpc\BoxQualityServiceInterface
     */
    private $BoxQualityService;

    /**
     * @Inject
     * @var \App\JsonRpc\SerialNoServiceInterface
     */
    private $SerialNoService;

    /**
     * @RequestMapping(path="/tagPrint/log", methods="get,post")
     * @return mixed
     */
    public function log(){
        $data['title'] = "调拨生产差异";

        //获取仓库信息
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $warehouse_list = $this->WarehouseService->getWarehouses(['ids' => $wIds, 'type' => 1], ['id', 'name']);
        $data['warehouse_list'] = $warehouse_list;

        //是否装箱
        $data['is_goods_packing'] = [
            ['id' => PublicCode::GOODS_PACK , 'name' => '是'],
            ['id' => PublicCode::NOT_GOODS_PACK, 'name' =>'否']
        ];

        //装箱人和打印人
        $data['admin_list'] = AdminService::users(['id','real_name']);

        //时间
        $data['date'] = date('Y-m-d 00:00:00').' - '.date('Y-m-d 23:59:59') ;

        // 数据 明细分页展示
        $params = $this->request->all();
        if ($this->isAjax()) {
            if (isset($params['date']) && !empty($params['date'])) {
                $date_time = explode(' - ', $params['date']);
                $params['start_created_at'] = $date_time[0];
                $params['end_created_at'] = $date_time[1];
            }
            if ($params['export'] == 0) {
                $printLogList = $this->TagPrintService->getPrintLog($params);
                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $printLogList['list'], ['count' => $printLogList['total'], 'limit' => $params['limit']]);
            }else{
                $page_id = 1;
                $page_size = 1000;
                $data = [];
                while (true){
                    $params['page'] = $page_id;
                    $params['limit'] = $page_size;
                    $printLogList = $this->TagPrintService->getPrintLog($params);
                    if (empty($printLogList['list'])){break;}
                    $data = array_merge($printLogList['list'], $data);
                    $page_id++;
                }

                try {
                    $url = exportToExcel(config('file_header.print_log'), $data ,'调拨生产差异');
                }catch (\Exception $e){
                    throw new BusinessException('导出失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
                }
                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
            }
        }

        return $this->show('tagPrint/log', $data);
    }

    /**
     * @RequestMapping(path="/tagPrint/tag", methods="get,post")
     * @return mixed
     */
    public function tag(){
        $data['template_type'] = PublicCode::TEMPlATE_TYPE;
        $data['production_type'] = PublicCode::PRODUCTION_TYPE;
        $data['in_store_type'] = PublicCode::IN_STORE_TYPE;

        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $warehouse_list = $this->WarehouseService->getWarehouses(['ids' => $wIds, 'type' => 1, 'status' => 1], ['id', 'name']);
        if (!empty($warehouse_list)) {
            $data['warehouse_list'] = array_column($warehouse_list, 'name', 'id');
        }

        return $this->show('tagPrint/tag', $data);
    }
    /**
     * @RequestMapping(path="/tagPrint/printTag", methods="get,post")
     * @return mixed
     */
    public function printTag(){
        $params = $this->request->all();
//         {"temp_type":"1","produce_type":"1","allot_no":"DB-230705-9122","in_store_type":"","print_type":"1","epc_code":"","unique_code":"JM00131671","barcode":"","arrival_order_no":"","in_store_barode":"","epc_switch":"1"}
        if ($params['produce_type'] == PublicCode::PRODUCTION_TYPE_INSTORE){
            $params['barcode'] = trim($params['in_store_barode']);
            unset($params['in_store_barode']);
        }

        //检测仓库参数是否存在
        if ($params['produce_type'] == PublicCode::PRODUCTION_TYPE_ALLO && $params['print_type'] == 2 && (!isset($params['w_id']) || empty($params['w_id']))){
            return $this->returnApi(ResponseCode::SERVICE_ERROR, '缺少仓库名称参数');
        }

        if (isset($params['allot_no'])){
            $params['allot_no'] = trim($params['allot_no']);
        }
        if (isset($params['arrival_order_no'])){
            $params['arrival_order_no'] = trim($params['arrival_order_no']);
        }


        if (in_array($params['temp_type'], [7,8,9,10])){//模版类型为7 时需要生成epc
                $erpRule = PublicCode::EPC_RULE[$params['temp_type']];
                $epcCodes = $this->SerialNoService->generateEPC(SerialType::BB, $erpRule['check'], $erpRule['company_sign'], $erpRule['epc_type'], 1);
                $params['epc_code'] = $epcCodes[0];
        }else{
            if(isset($params['check_rfid']) && $params['check_rfid'] == "on"){
                $checkRes = $this->EpcService->checkEpc($params['epc_code']);
                if (!$checkRes){
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '此磁扣未加入到磁扣池，请先加入磁扣池！', ['is_set_ecp' => false]);
                }
            }

            if (isset($params['epc_code'])){
                $params['epc_code'] = trim($params['epc_code']);
            }
        }

        if (isset($params['unique_code'])){
            $params['unique_code'] = trim($params['unique_code']);
        }
        if (empty($params['epc_code'])){
            unset($params['epc_code']);
        }else{
            $params['epc_code'] = [$params['epc_code']];
        }
        $userInfo = $this->getUserInfo();
        $params['login_admin_id'] = $userInfo['uid'];
        $params['login_realname'] = $userInfo['nickname'];
        $params['snow_id'] = newSnowId();

        try {
            $data = $this->TagPrintService->print($params);
            $data['tag_info']['epc_code'] = $params['epc_code'][0] ?? '';
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $data);
        }catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
    }

    /**
     * @RequestMapping(path="/tagPrint/checkEpcPool", methods="get,post")
     */
    public function checkEpcPool(){
        $params = $this->request->all();
        $rule = [
            'epc_code' => ['required', 'string']
        ];
        $errors = $this->validator->make($params, $rule);
        if ($errors->fails()) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
        }
        $checkRes = $this->EpcService->checkEpc($params['epc_code']);
        if (!$checkRes){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '此磁扣未加入到磁扣池，请先加入磁扣池！', ['is_set_ecp' => false]);
        }else{
            return $this->returnApi(ResponseCode::SUCCESS, '');
        }
    }

    /**
     * @RequestMapping(path="/tagPrint/isAgainPrint", methods="get,post")
     */
    public function isAgainPrint(){
        $params = $this->request->all();
        $rule = [
            'epc_code' => ['required', 'string'],
            'is_again_print' => ['required', 'integer', 'between:0,1'], // 是否补打吊牌
        ];
        $errors = $this->validator->make($params, $rule);
        if ($errors->fails()) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
        }
        $params['snow_id'] = newSnowId();

        $hasEpcCode = $this->EpcService->getEpcCode(['epc_code' => $params['epc_code'], 'status_list' => [0, 1]]);

        if ($hasEpcCode) {
            if ($params['is_again_print'] == 0) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '此RFID已被绑定，是否补打吊牌？', ['is_again_print' => 1]);
            } else {
                $barcode = '';
                $getSkuId = $hasEpcCode['sku_id'];
                $wId = $hasEpcCode['w_id'];
                // 补打吊牌，则不需要绑定epc
                if ($hasEpcCode['unique_code']) {
                    $code = $hasEpcCode['unique_code'];
                    $code_type = 1;
                    // 查店内码表里
                    $shelfUq = $this->UniqueCodeService->getUniqueCode(['unique_code' => $code]);
                    if ($shelfUq) {
                        $getSkuId = $shelfUq['sku_id'];
                        $wId = $shelfUq['w_id'];
                        $barcode = $shelfUq['barcode'];
                    }
                } else {
                    $code = $hasEpcCode['bar_code'];
                    $code_type = 2;
                    // 查条码对应的sku
                    $getSkuId = $this->SkuBarcodeService->getSkuByBarcode($code);
                }
                try {
                    $tag_info = $this->TagPrintService->getTagData($getSkuId, ['code' => $code, 'code_type' => $code_type, 'snow_id' => $params['snow_id']]);
                } catch (\Exception $e) {
                    return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
                }
                // 存打印日志
                $userInfo = $this->getUserInfo();
                $print_log = [
                    'w_id' => $wId,
                    'code_type' => $code_type,
                    'unique_code' => $code_type == 1 ? $code : '',
                    'barcode' => $code_type == 2 ? $code : $barcode,
                    'is_again_print' => 1,
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                    'snow_id' => $params['snow_id'],
                    'sku_id' => $getSkuId,
                    'spu_no' => $tag_info['spu_no'],
                    'sale_price' => yuan2fen($tag_info['sale_price']),
                    'epc_code' => $params['epc_code'] ?? ''
                ];
                $this->TagPrintService->createPrintLog($print_log);

                //获取店内码信息
                $uniqueData = [];
                if (isset($hasEpcCode['unique_code']) && !empty($hasEpcCode['unique_code'])){
                    $uniqueData = $this->ShelfGoodsCodeMapService->getUniqueCode(['unique_code' => $hasEpcCode['unique_code']]);
                }

                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', [
                    'tag_info' => $tag_info,
                    'log' => [
                        'code' => $code,
                        'num' => 1
                    ],
                    'unique_code_status' => $uniqueData['status'] ?? 0
                ]);
            }
        }else{
            return $this->returnApi(ResponseCode::SUCCESS, '');
        }
    }

    /**
     * @RequestMapping(path="/tagPrint/price", methods="get,post")
     * @return mixed
     */
    public function price(){
        //获取仓库信息
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $warehouse_list = $this->WarehouseService->getWarehouses(['ids' => $wIds, 'type' => 1], ['id', 'name']);
        if (!empty($warehouse_list)) {
            $data['warehouse_list'] = array_column($warehouse_list, 'name', 'id');
        }

        $data['price_temp_type'] = PublicCode::PRICE_TEMP_TYPE;

        return $this->show('tagPrint/price', $data);
    }

    /**
     * @RequestMapping(path="/tagPrint/printPrice", methods="get,post")
     * @return mixed
     */
    public function printPrice(){
        $params = $this->request->all();

        $validator = $this->validator->make($params, [
            'w_id' => ['required', 'integer'],
            'code_type' => ['required', 'integer', 'between:1,2'],// 价签类型 1条码 2店内码
            'code' => ['required', 'array'],
            'print_sign' => ['required', 'integer'],//是否打印标记
        ]);
        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        $error = [];
        if ($params['code_type'] == 1) {
            // 获取sku
            $barcodeList = $this->SkuService->getSkuBarcodeMap(['barcodes' => $params['code'], 'is_temp' => 0]);
            if (!$barcodeList) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '全都不存在');
            }
            $skuMap = array_column($barcodeList, 'sku_id', 'barcode');// barcode=>sku
            // 获取仓库对应的货架号
            $shelfList = $this->ShelfService->getShelfStockList(['w_id' => $params['w_id'], 'sku_ids' => array_unique($skuMap)], ['sku_id', 'shelf_code']);
            if ($shelfList) {
                foreach ($shelfList as $item) {
                    if (!isset($shelfMap[$item['sku_id']])) { // 之前展示多个货架，目前只展示一个
                        $shelfMap[$item['sku_id']][] = $item['shelf_code'];
                    }
                }
            }
        } else {
            // 获取sku
            $uniqueList = $this->UniqueCodeService->getUniqueCodes($params['code'], ['w_id', 'sku_id', 'unique_code', 'shelf_code', 'status']);
            if (!$uniqueList) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '全都不存在');
            }
            foreach ($uniqueList as $item) {
                if ($item['w_id'] != $params['w_id'] || $item['status'] == 2) {
                    $error[] = "{$item['unique_code']}不在此库";
                }
            }
            $skuMap = array_column($uniqueList, 'sku_id', 'unique_code');// unique=>sku
            // 获取仓库对应的货架号
            foreach ($uniqueList as $item) {
                if ($params['w_id'] == $item['w_id']) {
                    $shelfMap[$item['unique_code']][] = $item['shelf_code'];
                }
            }
            // 是否存在店内码调价
            $uniquePriceList = $this->PriceService->uniqueCodePrice($params['code']);
            if ($uniquePriceList) {
                if (count($uniquePriceList) == count($uniquePriceList, 1)) {
                    $uniquePrice[$uniquePriceList['unique_code']] = $uniquePriceList['new_sale_price'];
                } else {
                    $uniquePrice = array_column($uniquePriceList, 'new_sale_price', 'unique_code');
                }
            }
        }

        // 校验是否存在
        foreach ($params['code'] as $code) {
            if (!array_key_exists($code, $skuMap)) {
                $error[] = "{$code}不存在系统中";
            }
        }
        if ($error) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, implode('；', $error));
        }

        $skuIds = array_unique($skuMap);
        $skuList = $this->SkuService->checkSkuInfo(['sku_ids' => $skuIds]);
        // 规格（取销售规格）
        $skuSpec = array_column($skuList, 'sales_unit', 'id');
        // spu
        $skuSpu = array_column($skuList, 'spu_id', 'id');// sku=>spu_id
        $spuIds = array_unique($skuSpu);
        // 单位 spu名称 spu吊牌价 spu货号 spu产地
        $spuList = $this->SpuService->getSpus(['ids' => $spuIds], ['id', 'name', 'unit_id', 'tag_price', 'spu_no', 'place_of_production']);
        $spuNo = array_column($spuList, 'spu_no', 'id');// spu_id=>spu_no
        $spuPlace = array_column($spuList, 'place_of_production', 'id');// spu_id=>place_of_production
        $spuUnit = array_column($spuList, 'unit_id', 'id');// spu_id=>unit_id
        $unitList = $this->UnitService->getUnitAll(['id', 'name'], ['in' => ['id', array_unique($spuUnit)]]);
        $unitName = array_column($unitList, 'name', 'id'); // unit_id=>name

        // 此仓库是否有对应的门店
        $wareMap = $this->ShopService->getShopWarehouse(['w_id' => $params['w_id']]);
        if (!$wareMap) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '此仓库没有对应的门店');
        }
        $shop = $this->ShopService->getShop(['id' => $wareMap['shop_id']]);
        if (!$shop) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '此仓库对应的门店不存在');
        }
        $goodsList = $this->GoodsService->getGoodsS(['spu_ids' => $spuIds, 'channel_id' => 1]);
        if (!$goodsList) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '全部没有发布到门店');
        }
        $spuGoods = array_column($goodsList, 'id', 'spu_id'); // spu_id=>goods_id
        $goodsName = array_column($goodsList, 'name', 'spu_id');// spu_id=>goods_name
        // 获取goods销售价
        $goodsShopPrice = $this->GoodsService->goodsShopSalePrice($spuGoods, [$wareMap['shop_id']]);
        foreach ($spuIds as $spuId) {
            if (!isset($goodsShopPrice[$spuGoods[$spuId]][$wareMap['shop_id']]['sale_price'])) {
                $error[] = "spuId:{$spuId}没有此仓库对应的门店销售价";
            }
        }

        if ($error) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, implode('；', array_unique($error)));
        }

        // 获取限时特价活动价格
        $promotionPrice = $this->PromotionService->getXstjPromotionPrice($spuIds, $wareMap['shop_id']);
        $promotionEndTime = $promotionPrice ? array_column($promotionPrice, 'end_time', 'value') : [];
        $promotionPrice = $promotionPrice ? array_column($promotionPrice, 'product_price', 'value') : [];

        // 组装
        $list = [];
        foreach ($params['code'] as $code) {

            // 门店价
            $sale_price = $goodsShopPrice[$spuGoods[$skuSpu[$skuMap[$code]]]][$wareMap['shop_id']]['sale_price'];
            $sale_price_type = 1;
            // 店内码类型 有店内码调价则先取店内码调价
            if ($params['code_type'] == 2 && isset($uniquePrice[$code])) {
                $sale_price = $uniquePrice[$code];
                $sale_price_type = 2;
            }
            // 有限时特价活动时，与销售价格比，取低
            if (isset($promotionPrice[$skuSpu[$skuMap[$code]]])) {
                if ($promotionPrice[$skuSpu[$skuMap[$code]]] < $sale_price) {
                    $xstj_price = $promotionPrice[$skuSpu[$skuMap[$code]]];
                    $sale_price_type = 3;
                }
            }
            // 系统暂无会员价，先跟销售价一致
            $sale_price_vip = $sale_price;
            $sale_price_info = explode('.', fen2yuan($sale_price));

            $info = [
                'code' => $code,
                'name' => $goodsName[$skuSpu[$skuMap[$code]]],
                'spec' => isset($skuSpec[$skuMap[$code]]) && $skuSpec[$skuMap[$code]] ? $skuSpec[$skuMap[$code]] : "",
                'shelf_code' => $params['code_type'] == 1 ? ($shelfMap[$skuMap[$code]] ?? []) : ($shelfMap[$code] ?? []),

                'sale_price' => fen2yuan($sale_price),
                'sale_price_yuan' => $sale_price_info[0],
                'sale_price_fen' => $sale_price_info[1],
                'sale_price_type' => $sale_price_type,
                'sale_price_vip' => $sale_price_vip,

                'unit_name' => $unitName[$spuUnit[$skuSpu[$skuMap[$code]]]],
                'spu_no' => $spuNo[$skuSpu[$skuMap[$code]]],
                'place_of_production' => $spuPlace[$skuSpu[$skuMap[$code]]],
                'price_bureau_tel' => $shop['price_bureau_tel']
            ];
            if (isset($xstj_price)) {
                $info['xstj_price'] = $xstj_price;
                $xstj_price_info = explode('.', fen2yuan($xstj_price));
                $info['xstj_price_yuan'] = $xstj_price_info[0];
                $info['xstj_price_fen'] = $xstj_price_info[1];
                $info['xstj_end_time'] = date('Y.m.d H:i:s', strtotime($promotionEndTime[$skuSpu[$skuMap[$code]]]));
            }
            unset($xstj_price);
            $list[] = $info;
        }
        logger()->info('==================', $list);
        if ($params['print_sign'] == 1){
            if ($params['temp_type'] == 1){
                return $this->show('tagPrint/one', ['list' => $list]);
            }elseif($params['temp_type'] == 2){
                return $this->show('tagPrint/four', ['list' => $list]);
            }elseif($params['temp_type'] == 3){
                return $this->show('tagPrint/twelve', ['list' => $list]);
            }elseif($params['temp_type'] == 4){
                return $this->show('tagPrint/eighteen', ['list' => $list]);
            }else{
                return $this->returnApi(ResponseCode::SUCCESS, '模版类型不存在', []);
            }
        }else{
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list);
        }
    }


    /**
     * @RequestMapping(path="/tagPrint/printTagRfid", methods="get,post")
     * @return mixed
     */
    public function printTagRfid(){
        $params = $this->request->all();
        if ($params['produce_type'] == PublicCode::PRODUCTION_TYPE_INSTORE){
            $params['barcode'] = trim($params['in_store_barode']);
            unset($params['in_store_barode']);
        }

        if (isset($params['allot_no'])){
            $params['allot_no'] = trim($params['allot_no']);
        }
        if (isset($params['arrival_order_no'])){
            $params['arrival_order_no'] = trim($params['arrival_order_no']);
        }
        if (isset($params['epc_code'])){
            $params['epc_code'] = trim($params['epc_code']);
        }
        if (isset($params['unique_code'])){
            $params['unique_code'] = trim($params['unique_code']);
        }
        if (empty($params['epc_code'])){
            unset($params['epc_code']);
        }else{
            $params['epc_code'] = [$params['epc_code']];
        }
        $userInfo = $this->getUserInfo();
        $params['login_admin_id'] = $userInfo['uid'];
        $params['login_realname'] = $userInfo['nickname'];
        $params['snow_id'] = newSnowId();

        try {
            $data = $this->TagPrintService->printTag($params);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $data);
        }catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
    }

    /**
     * @RequestMapping(path="/tagPrint/bindEpc", methods="get,post")
     * @return mixed
     */
    public function bindEpc(){
        $params = $this->request->all();

        $validator = $this->validator->make($params, [
            'unique_code' => ['string'],
            'epc_code' => ['required', 'string'],
            'barcode' => ['string'],
        ]);
        if (isset($params['in_store_barode']) && !empty($params['in_store_barode'])){
            $params['barcode'] = $params['in_store_barode'];
        }
        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }
        if ($params['print_type'] == 1 && $params['produce_type'] == 1 && empty($params['unique_code'])){
            return $this->returnApi(ErrorCode::REQUEST_ERROR, '店内码不能为空！');
        }
        if ($params['produce_type'] == 2 && $params['in_store_type'] == 1 && empty($params['unique_code'])){
            return $this->returnApi(ErrorCode::REQUEST_ERROR, '条形码不能为空！');
        }
        if ($params['produce_type'] == 2 && $params['in_store_type'] == 2 && empty($params['in_store_barode'])){
            return $this->returnApi(ErrorCode::REQUEST_ERROR, '条形码不能为空！');
        }
        if ($params['print_type'] == 2 && empty($params['barcode'])){
            return $this->returnApi(ErrorCode::REQUEST_ERROR, '条形码不能为空！');
        }
        if ($params['produce_type'] == 2 && isset($params['in_store_unique_code']) && !empty($params['in_store_unique_code'])){
            $params['unique_code'] = $params['in_store_unique_code'];
        }

        $userInfo = $this->getUserInfo();
        $params['login_admin_id'] = $userInfo['uid'];
        $params['login_realname'] = $userInfo['nickname'];
        $params['snow_id'] = newSnowId();
        if ($params['produce_type'] == 2 && isset($params['in_store_barode'])){
            $params['barcode'] = $params['in_store_barode'];
        }

        if ($params['print_type'] == 1 && !empty($params['unique_code'])){
            $hasEpcCode = $this->EpcService->getEpcCode(['unique_code' => $params['unique_code'], 'status_list' => [0, 1]]);
            if (!empty($hasEpcCode)) {
                if ($params['is_again_print'] == 0) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '此店内码已被绑定RFID，是否重新绑定？', ['is_again_print' => 1]);
                } else {
                    return $this->bind($params);
                }
            }else{
                return $this->bind($params);
            }
        }else{
            return $this->bind($params);
        }
    }

    private function bind($params){
        try {
            $data = $this->TagPrintService->bindEpc($params);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $data);
        }catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
    }

    /**
     * @RequestMapping(path="/tagPrint/barcodeBind", methods="get,post")
     * @return mixed
     */
    public function barcodeBind(){
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $warehouse_list = $this->WarehouseService->getWarehouses(['ids' => $wIds, 'type' => 1, 'status' => 1], ['id', 'name']);
        if (!empty($warehouse_list)) {
            $data['warehouse_list'] = array_column($warehouse_list, 'name', 'id');
        }
        return $this->show('tagPrint/barcodeBind', $data);
    }

    /**
     * 检测条形码
     * @RequestMapping(path="/tagPrint/checkBracode", methods="get,post")
     * @return mixed
     */
    public function checkBracode(){
        $params = $this->request->all();

        $validator = $this->validator->make($params, [
            'barcode' => ['string'],
        ]);
        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        try {
            $sku_id = $this->SkuBarcodeService->getSkuByBarcode($params['barcode']);
            if (empty($sku_id)){
                return $this->returnApi(ResponseCode::SERVICE_ERROR, '条形码不存在，请重新扫描！', false);
            }else{
                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', true);
            }
        }catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
    }

    /**
     * @RequestMapping(path="/tagPrint/batchBarcodeBind", methods="get,post")
     * @return mixed
     */
    public function batchBarcodeBind(){
        $params = $this->request->all();

        $validator = $this->validator->make($params, [
            'barcode' => ['string'],
            'epc_code' => ['required', 'string'],
            'w_id' => ['required', 'integer']
        ]);
        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }
        if (empty($params['barcode'])){
            return $this->returnApi(ErrorCode::REQUEST_ERROR, '条形码不能为空！');
        }
        if (empty($params['epc_code'])){
            return $this->returnApi(ErrorCode::REQUEST_ERROR, 'RFID不能为空！');
        }

        //检测rfid是否已被绑定
        $epc_codes = explode("\n", $params['epc_code']);
        $checkEpcCodes = $this->TagPrintService->checkEpcBind(["epc_codes" => $epc_codes]);
        if (!empty($checkEpcCodes)){
            $bindedEpcCodes = array_column($checkEpcCodes, 'epc_code');
            return $this->returnApi(ResponseCode::SUCCESS, "其中包含已被绑定的EPC\n".implode("\n",$bindedEpcCodes), $bindedEpcCodes);
        }else{
            $userInfo = $this->getUserInfo();
//            //获取仓库信息
//            $wIds = AdminService::organizeWareHouseData($userInfo['uid']);
//            $warehouse_list = $this->WarehouseService->getWarehouses(['ids' => $wIds, 'type' => 1], ['id', 'name']);
//            if (!empty($warehouse_list)) {
//                $params['w_id'] = $warehouse_list[0]['id'];
//            }
            $params['login_admin_id'] = $userInfo['uid'];
            $params['login_realname'] = $userInfo['nickname'];
            $params['snow_id'] = newSnowId();
            $params['epc_codes'] = $epc_codes;
            $params['type'] = 2;

            try {
                $this->SkuEpcCodeService->batchBarcodeBind($params);
                return $this->returnApi(ResponseCode::SUCCESS, '绑定成功', ['res' => true, 'bind_epc_num' => count($epc_codes)]);
            }catch (\Exception $e){
                throw new BusinessException('绑定失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
            }
        }
    }

    /**
     * @RequestMapping(path="/tagPrint/checkUniqueCode", methods="get,post")
     * @return mixed
     */
    public function checkUniqueCode(){
        $params = $this->request->all();

        $validator = $this->validator->make($params, [
            'unique_code' => ['required','string'],
        ]);
        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }

        $skuEpcCodes = $this->SkuEpcCodeService->checkEpcInfo(['unique_code' => $params['unique_code']]);
        if (!empty($skuEpcCodes)){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, "店内码(".$params['unique_code'].")已绑定其他EPC");
        }

        try {
            //获取店内码的仓库
            $unique_info = $this->UniqueCodeService->getUniqueCode(['unique_code' => $params['unique_code']]);
            if (empty($unique_info)){
                return $this->returnApi(ResponseCode::SERVER_ERROR, "店内码不存在");
            }else{
                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', true);
            }
        }catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
    }

    /**
     * @RequestMapping(path="/tagPrint/uniqueCodeBind", methods="get,post")
     * @return mixed
     */
    public function uniqueCodeBind(){
        $params = $this->request->all();

        $validator = $this->validator->make($params, [
            'unique_code' => ['string'],
            'epc_code' => ['required', 'string']
        ]);
        if ($validator->fails()) {
            return $this->returnApi(ErrorCode::REQUEST_ERROR, $validator->errors()->first());
        }
        if (empty($params['unique_code'])){
            return $this->returnApi(ErrorCode::REQUEST_ERROR, '店内码不能为空！');
        }
        if (empty($params['epc_code'])){
            return $this->returnApi(ErrorCode::REQUEST_ERROR, 'RFID不能为空！');
        }

        //检测店内码是否已绑定其他rfid
        $skuEpcCodes = $this->SkuEpcCodeService->checkEpcInfo(['unique_code' => $params['unique_code']]);
        if (!empty($skuEpcCodes)){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, "店内码(".$params['unique_code'].")已绑定其他EPC");
        }
        //检测rfid是否已被绑定
        $checkEpcCodes = $this->TagPrintService->checkEpcBind(["epc_codes" => [$params['epc_code']]]);
        if (!empty($checkEpcCodes)){
            $bindedEpcCodes = array_column($checkEpcCodes, 'epc_code');
            return $this->returnApi(ResponseCode::SERVER_ERROR, "其中包含已被绑定的EPC\n".implode("\n",$bindedEpcCodes), $bindedEpcCodes);
        }else{
            $userInfo = $this->getUserInfo();
            //获取店内码的仓库
            $unique_info = $this->UniqueCodeService->getUniqueCode(['unique_code' => $params['unique_code']]);
            if (empty($unique_info)){
                return $this->returnApi(ResponseCode::SERVER_ERROR, "店内码不存在");
            }

            $params['shelf_code'] = $unique_info['shelf_code'];
            $params['w_id'] = $unique_info['w_id'];
            $params['barcode'] = $unique_info['barcode'];
            $params['unique_code'] = $params['unique_code'];
            $params['login_admin_id'] = $userInfo['uid'];
            $params['login_realname'] = $userInfo['nickname'];
            $params['snow_id'] = newSnowId();
            $params['epc_codes'] = [$params['epc_code']];
            $params['type'] = 1;
            try {
                $res = $this->SkuEpcCodeService->batchBarcodeBind($params);
                if ($res['code'] == 200){
                    return $this->returnApi(ResponseCode::SUCCESS, '绑定成功', ['res' => true, 'bind_epc_num' => 1]);
                }else{
                    return $this->returnApi(ResponseCode::SERVER_ERROR, $res['msg'], ['res' => false, 'bind_epc_num' => 0]);
                }
            }catch (\Exception $e){
                throw new BusinessException('绑定失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
            }
        }
    }


    /**
     * @RequestMapping(path="/tagPrint/checkRfidBound", methods="get,post")
     */
    public function checkRfidBound(){
        $params = $this->request->all();
        $rule = [
            'epc_code' => ['required', 'string']
        ];
        $errors = $this->validator->make($params, $rule);
        if ($errors->fails()) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
        }
        $params['snow_id'] = newSnowId();

        $hasEpcCode = $this->EpcService->getEpcCode(['epc_code' => $params['epc_code'], 'status_list' => [0, 1]]);

        if ($hasEpcCode) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '此RFID已被绑定，请先解绑!');
        }else{
            return $this->returnApi(ResponseCode::SUCCESS, '');
        }
    }

    /**
     * @RequestMapping(path="/tagPrint/frontTag", methods="get,post")
     */
    public function frontTag(){
        $data['template_type'] = PublicCode::TEMPlATE_FRONT_TYPE;
        $data['production_type'] = PublicCode::PRODUCTION_TYPE;
        $data['in_store_type'] = PublicCode::IN_STORE_TYPE;

        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $warehouse_list = $this->WarehouseService->getWarehouses(['ids' => $wIds, 'type' => 1, 'status' => 1], ['id', 'name']);
        if (!empty($warehouse_list)) {
            $data['warehouse_list'] = array_column($warehouse_list, 'name', 'id');
        }

        return $this->show('tagPrint/front', $data);
    }

    /**
     * 根据箱号获取预约单信息
     * @RequestMapping(path="/tagPrint/getBoxInfo", methods="get,post")
     */
    public function getBoxInfo(){
        $params = $this->request->all();
        $rule = [
            'box_no' => ['required', 'string'],
            'temp_type' => ['required', 'integer'],
            'sign' => ['required', 'integer', 'in:1,2'],//1根据箱号获取质检信息 2根据箱号获取入库数量
        ];
        $errors = $this->validator->make($params, $rule);
        if ($errors->fails()) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
        }
        //根据质检箱号获取预约单信息
        $boxQuality = $this->BoxQualityService->getInfo(['sub_box_no' => $params['box_no'], 'order_by_field' => 'created_at', 'order_by_sort' => 'desc']);

        if (empty($boxQuality)){
            return $this->returnApi(ResponseCode::SERVICE_ERROR, "质检箱号不存在");
        }

        //检测质检箱号里条码是否都存在店内码且已入库
        $qualityNum = collect($boxQuality['details'])->sum('num');
        if ($params['sign'] == 1 && $qualityNum == $boxQuality['in_num']){
            return $this->returnApi(ResponseCode::SERVICE_ERROR, "质检箱号里条形码重复生产");
        }

        $result = [
            'quality_id' => $boxQuality['id'],
            'arrival_no' => $boxQuality['arrival_no'],
            'barcode' => "",
            'num' => 0,
            'in_num' => 0,
            'is_clear' => ($boxQuality['num'] == $boxQuality['in_num']) ? true : false
        ];
        //是否同一SKU装箱 & 不使用TSC打印机打印，获取条形码
        if ($boxQuality['is_same_sku'] == 1 && !in_array($params['temp_type'], [PublicCode::TEMPLATE_FOUR_NINE_TSC, PublicCode::TEMPLATE_FOUR_SIX_TSC])){
            $result['barcode'] = $boxQuality['details'][0]['barcode'];
            if (!$params['imperfect']){//获取正品
                $result['num'] = $boxQuality['details'][0]['normal_num'];
                $result['in_num'] = $boxQuality['details'][0]['in_normal_num'];
            }else{//获取残次
                $result['num'] = $boxQuality['details'][0]['imperfect_num'];
                $result['in_num'] = $boxQuality['details'][0]['in_imperfect_num'];
            }
        }else{
            if (!$params['imperfect']){//获取正品
                $result['num'] = collect($boxQuality['details'])->sum('normal_num');
                $result['in_num'] = collect($boxQuality['details'])->sum('in_normal_num');
            }else{//获取残次
                $result['num'] = collect($boxQuality['details'])->sum('imperfect_num');
                $result['in_num'] = collect($boxQuality['details'])->sum('in_imperfect_num');
            }
        }

        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $result);
    }

    /**
     * 打印店内码
     * @RequestMapping(path="/tagPrint/printFrontTagRfid", methods="get,post")
     * @return mixed
     */
    public function printFrontTagRfid(){
        $params = $this->request->all();
        //校验条形码是否存在于质检箱中
        if (!isset($params['quality_id']) || empty($params['quality_id'])){
            return $this->returnApi(ResponseCode::SERVICE_ERROR, "请指定质检箱数据");
        }
        $qualityDetail = $this->BoxQualityService->getDetailS(['quality_id' => $params['quality_id'], 'barcode' => trim($params['barcode'])]);
        if (empty($qualityDetail)){
            return $this->returnApi(ResponseCode::SERVICE_ERROR, "条形码 ".$params['barcode']." 不在当前质检箱中");
        }
        //获取商品数量
        $detailNum = $this->BoxQualityService->getDetailsNum(['quality_id' => $params['quality_id']]);

        //补打标记
        if (!isset($params['again_print'])){//非补打
            $params['again_print'] = false;
        }else{
            $params['again_print'] = true;
        }

        //是否原残打印
        if (!isset($params['imperfect'])){//非残次打印时
            if ($qualityDetail[0]['normal_num'] == 0 ){
                return $this->returnApi(ResponseCode::SERVICE_ERROR, "条形码 ".$params['barcode']." 无正品商品可打印");
            }
            if ($qualityDetail[0]['normal_num'] == $qualityDetail[0]['in_normal_num']){
                return $this->returnApi(ResponseCode::SERVICE_ERROR, "条形码 ".$params['barcode']." 已全部入库，无需再次打印");
            }
            if (!isset($params['barcode_num']) || empty($params['barcode_num'])){
                if ($detailNum > 1){
                    $params['barcode_num'] = 1;
                }else{
                    $params['barcode_num'] = $qualityDetail[0]['normal_num'] - $qualityDetail[0]['in_num'];
                }
            }
            $params['imperfect'] = false;
        }else{//残次打印
            //检测残次数量
            if ($qualityDetail[0]['imperfect_num'] == 0 ){
                return $this->returnApi(ResponseCode::SERVICE_ERROR, "条形码 ".$params['barcode']." 无残次商品可打印");
            }
            if ($qualityDetail[0]['imperfect_num'] == $qualityDetail[0]['in_imperfect_num']){
                return $this->returnApi(ResponseCode::SERVICE_ERROR, "条形码 ".$params['barcode']." 残次已全部入库，无需再次打印");
            }
            if (!isset($params['barcode_num']) || empty($params['barcode_num'])){
                if ($detailNum > 1){
                    $params['barcode_num'] = 1;
                }else{
                    $params['barcode_num'] = $qualityDetail[0]['imperfect_num'] - $qualityDetail[0]['in_imperfect_num'];
                }
            }
            $params['imperfect'] = true;
        }

        if (isset($params['arrival_order_no'])){
            $params['arrival_order_no'] = trim($params['arrival_order_no']);
        }
        if (isset($params['epc_code'])){
            $params['epc_code'] = trim($params['epc_code']);
        }

        if (empty($params['epc_code'])){
            unset($params['epc_code']);
        }else{
            $params['epc_code'] = [$params['epc_code']];
        }
        $userInfo = $this->getUserInfo();
        $params['login_admin_id'] = $userInfo['uid'];
        $params['login_realname'] = $userInfo['nickname'];
        $params['snow_id'] = newSnowId();

        try {
            //模版类型为 1， 2 时需要生成epc
            if (in_array($params['temp_type'], [1,2,9,10])){
                $erpRule = PublicCode::EPC_RULE[$params['temp_type']];
                $epcCodes = $this->SerialNoService->generateEPC(SerialType::BB, $erpRule['check'], $erpRule['company_sign'], $erpRule['epc_type'], intval($params['barcode_num']));
                $params['epc_codes'] = $epcCodes;
            }

            $data = $this->TagPrintService->printFrontTag($params);
            //货品数量
            if (!$params['imperfect']){//获取正品
                $data['num'] = $qualityDetail[0]['normal_num'];
                $data['in_num'] = $qualityDetail[0]['in_normal_num'];
            }else{//获取残次
                $data['num'] = $qualityDetail[0]['imperfect_num'];
                $data['in_num'] = $qualityDetail[0]['in_imperfect_num'];
            }

            //将打印的店内码加入缓存
            $key = 'FRONT_TAG_PRINT'.$userInfo['uid'].'_'.$params['quality_id'];
            HashService::del($key);
            logger()->info('data=================', [$data['log']['code']]);
            HashService::saveDataToHash($key, $data['log']['code'], 3600, false);

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $data);
        }catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
    }

    /**
     * 将店内码和rfid绑定,并入库
     * @RequestMapping(path="/tagPrint/frontBindEpcInStore", methods="get,post")
     */
    public function frontBindEpcInStore(){
        $params = $this->request->all();

        $rule = [
            'epc_code' => ['required', 'string'],
            'unique_code' => ['required', 'string'],
            'tag_print_log_id' => ['required', 'integer'],
            'barcode' => ['required', 'string'],
            'arrival_order_no' => ['required', 'string'],
        ];
        $msg = [
            'unique_code.required' => '店内码不能为空，请先打印吊牌',
            'epc_code.required' => 'RFID磁扣不能为空',
            'tag_print_log_id.required' => '店内码打印日志参数不能为空',
            'barcode.required' => '条形码不能为空',
            'arrival_order_no.required' => '预约单号不能为空',
        ];
        $errors = $this->validator->make($params, $rule, $msg);
        if ($errors->fails()) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
        }

        $userInfo = $this->getUserInfo();
        $params['login_admin_id'] = $userInfo['uid'];
        $params['login_realname'] = $userInfo['nickname'];
        $params['snow_id'] = newSnowId();

        try {
            $this->TagPrintService->frontBindEpcInStore($params);
            return $this->returnApi(ResponseCode::SUCCESS, '绑定入库成功', ['res' => true, 'bind_epc_num' => 1]);
        }catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
    }

    /**
     * 识别rfid，将店内码入库
     * @RequestMapping(path="/tagPrint/frontInStore", methods="get,post")
     */
    public function frontInStore(){
        $params = $this->request->all();

        $rule = [
            'epc_codes' => ['required', 'array'],
            'arrival_order_no' => ['required', 'string'],
            'quality_id' => ['required', 'integer'],
            'imperfect' => ['required'],
            'barcode' => ['required', 'string'],
        ];
        $msg = [
            'epc_codes.required' => 'RFID码不能为空',
            'epc_codes.array' => 'RFID码数据类型不符',
            'arrival_order_no.required' => '预约单号不能为空',
            'arrival_order_no.array' => '预约单号数据类型不符',
            'quality_id.required' => 'quality_id不能为空',
            'quality_id.array' => 'quality_id数据类型不符',
            'barcode.required' => '条形码不能为空',
            'barcode.array' => '条形码数据类型不符',
        ];
        $errors = $this->validator->make($params, $rule, $msg);
        if ($errors->fails()) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
        }

        $userInfo = $this->getUserInfo();
        $params['login_admin_id'] = $userInfo['uid'];
        $params['login_realname'] = $userInfo['nickname'];
        $params['snow_id'] = newSnowId();

        try {
            $inNum = $this->TagPrintService->batchRrontInStore($params);
            return $this->returnApi(ResponseCode::SUCCESS, '入库成功', ['res' => true, 'bind_epc_num' => $inNum]);
        }catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
    }

    /**
     * 校验rfid是否已绑定店内码
     * @RequestMapping(path="/tagPrint/checkFrontRfid", methods="get,post")
     * @return mixed
     */
    public function checkFrontRfid(){
        $params = $this->request->all();
        //根据epc获取绑定记录
        $skuEpcList = $this->SkuEpcCodeService->checkEpcInfo(['epc_codes' => $params['epc_codes'], 'bind_unique_code' => true]);
        if (empty($skuEpcList)){
            return $this->returnApi(ResponseCode::SERVICE_ERROR, "RFID均未绑定店内码，请确认");
        }

        $errorMsg = [];
        //检测epc是否已绑定店内码
        $skuEpcCodes = array_column($skuEpcList, 'epc_code');
        foreach ($params['epc_codes'] as $item){
            if (!in_array($item, $skuEpcCodes)){
                $errorMsg[] = "RFID:".$item."未绑定店内码";
            }
        }

        //检测epc绑定的店内码是否在此质检箱中
        $uniqueCodes = array_column($skuEpcList, 'unique_code');
        $tagList = $this->TagPrintService->getPrintList(['unique_codes' => $uniqueCodes, 'quality_id' => $params['quality_id'], 'is_imperfect' => !$params['imperfect'] ? 0 : 1]);
        if (empty($tagList)){
            $msg = "RFID绑定的店内码均不属于此质检箱，请确认";
            if (!$params['imperfect']){
                $msg = "RFID绑定的店内码均不属于此质检箱正品数据，请确认";
            }else{
                $msg = "RFID绑定的店内码均不属于此质检箱原残数据，请确认";
            }
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $msg);
        }
        $tagUniqueCodes = array_column($tagList, 'unique_code');
        foreach ($uniqueCodes as $uItem){
            if (!in_array($uItem, $tagUniqueCodes)){
                $errorMsg[] = "店内码:".$uItem."不属于此质检箱";
            }
        }

        //检测epc绑定的店内码是否已入库
        $checkUniqueCodes = $this->ShelfGoodsCodeMapService->checkUniqueCodes($tagUniqueCodes);
        if (!empty($checkUniqueCodes['exists'])){
            foreach ($checkUniqueCodes['exists'] as $cItem){
                $errorMsg[] = "店内码:".$cItem."已入库";
            }
        }

        //获取未识别到的店内码
        $no_read_num = 0;
        if (count($params['epc_codes']) > 0){//批量绑定时，获取质检箱中的店内码
            //从缓存中获取打印的店内码
            $userInfo = $this->getUserInfo();
            $key = 'FRONT_TAG_PRINT'.$userInfo['uid'].'_'.$params['quality_id'];
            $printUniqueCodes = HashService::getDataAllFromHash($key);
            //获取质检箱中店内码
            $qualityTagList = $this->TagPrintService->getPrintList(['quality_id' => $params['quality_id'], 'unique_codes' => $printUniqueCodes, 'is_imperfect' => !$params['imperfect'] ? 0 : 1]);
            $qualityUniqueCodes = array_column($qualityTagList, 'unique_code');
            foreach ($qualityUniqueCodes as $qItem){
                if (!in_array($qItem, $tagUniqueCodes)){
                    $errorMsg[] = "店内码:".$qItem."绑定的RFID未识别";
                    $no_read_num++;
                }
            }
        }

        $result = [
            'error_msg' => $errorMsg,
            'error_num' => count($errorMsg),
            'read_num' => count($params['epc_codes']),
            'no_read_num' => $no_read_num
        ];
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $result);
    }

    /**
     * @RequestMapping(path="/tagPrint/checkUniqueOut", methods="get,post")
     * @return mixed
     */
    public function checkUniqueOut(){
        $params = $this->request->all();

        if (!isset($params['unique_code']) || empty(trim($params['unique_code'])) ){
            return $this->returnApi(ResponseCode::SERVICE_ERROR, '缺少店内码参数');
        }

        $params['unique_code'] = trim($params['unique_code']);
        try {
            $data = $this->ShelfGoodsCodeMapService->getUniqueCode($params);
            if (!empty($data)){
                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['status' => $data['status']]);
            }else{
                return $this->returnApi(ResponseCode::SERVICE_ERROR, "店内码不存在");
            }
        }catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
    }

    /**
     * @RequestMapping(path="/tagPrint/kongList", methods="get,post")
     * @return mixed
     */
    public function kongList(){
        $data['title'] = "空吊牌查询";
        //获取仓库信息
        $where = [
            'code_type' => 1,
            'empty_spu_no' => true,
            'get_w_id' => true,
            'start_created_at' => '2025-03-01 00:00:00'
        ];
        $printList = $this->TagPrintService->getPrintWIds($where);
        logger()->info('print list===========', [$printList]);
        $wIds = array_column($printList, 'w_id');
        $warehouse_list = [];
        if (!empty($wIds)){
            $warehouse_list = $this->WarehouseService->getWarehouses(['ids' => $wIds, 'type' => 1], ['id', 'name']);
        }
        $data['warehouse_list'] = $warehouse_list;

        //时间
        $data['date'] = date("Y-m-d 00:00:00",strtotime("-30 day")).' - '.date('Y-m-d 23:59:59') ;

        // 数据 明细分页展示
        $params = $this->request->all();
        if ($this->isAjax()) {
            $search = $params['search'];
            if (isset($search['print_time']) && !empty($search['print_time'])) {
                $date_time = explode(' - ', $search['print_time']);
                $search['start_created_at'] = $date_time[0];
                $search['end_created_at'] = $date_time[1];

                if ($search['start_created_at'] < '2025-03-01 00:00:00'){//只针对2025-03-01之后的吊牌打印里
//                    throw new BusinessException('只针对2025-03-01之后的数据可查询！',ResponseCode::SERVER_ERROR);
                    return $this->returnApi(ResponseCode::SERVER_ERROR, '只针对2025-03-01之后的数据可查询！');
                }
            }
            if ($params['export'] == 0) {
                $search['page'] = $params['page'];
                $search['limit'] = $params['limit'];
                $list = $this->TagPrintService->kongList($search);
                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $params['limit']]);
            }else{
                $page_id = 1;
                $page_size = 1000;
                $data = [];
                while (true){
                    $search['page'] = $page_id;
                    $search['limit'] = $page_size;
                    $list = $this->TagPrintService->kongList($search);
                    if (empty($list['data'])){break;}
                    $data = array_merge($list['data'], $data);
                    $page_id++;
                }
                if (empty($data)){
                    return $this->returnApi(ResponseCode::SERVER_ERROR, '可导出数据为空');
                }

                try {
                    $url = exportToExcel(config('file_header.empty_spu_log'), $data ,'空吊牌导出');
                }catch (\Exception $e){
                    throw new BusinessException('导出失败！'.$e->getMessage(),ResponseCode::SERVER_ERROR,$e);
                }
                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => $url]);
            }
        }

        return $this->show('tagPrint/kong', $data);
    }
}