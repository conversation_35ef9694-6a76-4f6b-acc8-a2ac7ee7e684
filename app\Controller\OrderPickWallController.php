<?php
declare(strict_types=1);

namespace App\Controller;

use App\Constants\ErrorCode;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Exception\ValidateException;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\OrderPickWallServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use Hyperf\Contract\SessionInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use App\Library\Facades\AdminService;

/**
 * Class OrderPickWallController
 * @Controller()
 * @package App\Controller
 */
class OrderPickWallController extends AbstractController
{
    private static string $PICK_WALL_CACHE_KEY = 'wms.order.pick.wall.%s';//拼接 仓库id_分拣墙编号
    private static int $PICK_WALL_CACHE_EXPIRE = 7200;//过期时间(两小时)
    private static int $PICK_WALL_CACHE_MAX_NUM = 35;//每面墙最大数量

    /**
     * @Inject()
     * @var OrderPickWallServiceInterface
     */
    private $OrderPickWallService;

    /**
     * @Inject
     * @var WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject()
     * @var SessionInterface
     */
    protected $session;
    /**
     * @RequestMapping(path="/orderPickWall/wall", methods="get,post")
     * @return mixed
     */
    public function wall ()
    {
        $data['title'] = '订单分拣';
        $data['walls'] = PublicCode::order_pick_wall;
        $where = [];
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        if (!empty( $wIds )) {
            $where = ['ids' => $wIds];
        }
        $data['warehouse_info'] = collect( $this->WarehouseService->getWarehouses( $where ) )->pluck( 'name', 'id' )->toArray();
        return $this->show( 'orderPickWall/wall', $data );
    }

    /**
     * 初始化分拣墙
     * @RequestMapping(path="/orderPickWall/initWall", methods="post")
     */
    public function initWall ()
    {
        $result = array();
        //获取当前分拣墙的所有格子数据 在redis
        $wall_id = $this->request->post( 'wall_id', 0 );//分拣墙ID
        $wId = $this->request->post( 'w_id', 0 );//分拣墙ID
        $walls = PublicCode::order_pick_wall;
        if (isset( $walls[$wall_id] )) {
            //$wId = AdminService::organizeWareHouseData($this->getUserId());
            try {
                if (empty($wId)) {
                    throw new ValidateException( '请选择仓库！' );
                }
                $wall = $walls[$wall_id];
                $key = sprintf( self::$PICK_WALL_CACHE_KEY,  $wId  . '_' . $wall );
                $wall_info = redis()->get( $key );
                if (empty( $wall_info )) {
                    throw new ValidateException( '此分拣墙暂无数据!' );
                }
                $wall_info = json_decode( $wall_info, true );
                foreach ($wall_info['location'] as $key => $val) {
                    //循环每个格子，判断该显示什么颜色
                    $scan_num = count( $val['scan_incodes'] );
                    $no_scan_num = count( $val['no_scan_incodes'] );
                    $result['wall_info'][$key]['html'] = $scan_num . '/' . ($scan_num + $no_scan_num);
                    if ($no_scan_num == 0) {
                        $result['wall_info'][$key]['css'] = 'bg-ok';
                    } else {
                        $result['wall_info'][$key]['css'] = 'bg-no';
                    }
                }
            } catch (\Exception $exception) {
                return $this->returnApi( ErrorCode::SERVER_ERROR, $exception->getMessage() );
            }
            return $this->returnApi( ResponseCode::SUCCESS, '请求成功', $result );
        }
    }

    /**
     * 清空分拣墙
     * @RequestMapping(path="/orderPickWall/cleanWall", methods="post")
     */
    public function cleanWall ()
    {
        //获取当前分拣墙的所有格子数据 在redis里清空
        $wall_id = $this->request->post( 'wall_id', 0 );//分拣墙ID
        $wId = $this->request->post( 'w_id', 0 );//分拣墙ID
        $walls = PublicCode::order_pick_wall;
        if (isset( $walls[$wall_id] )) {
            //$wId = AdminService::organizeWareHouseData($this->getUserId());
            try {
                if (empty($wId)) {
                    throw new ValidateException( '请选择仓库！' );
                }
                $wall = $walls[$wall_id];
                $key = sprintf( self::$PICK_WALL_CACHE_KEY,  $wId . '_' . $wall );
                redis()->del( $key );
            } catch (\Exception $exception) {
                return $this->returnApi( ErrorCode::SERVER_ERROR, $exception->getMessage() );
            }
            return $this->returnApi( ResponseCode::SUCCESS, '清除成功' );
        }
    }


    /**
     * @RequestMapping(path="/orderPickWall/scanUniqueCode", methods="post")
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function scanUniqueCode ()
    {
        $unique_code = $this->request->post( 'unique_code', 0 );//店内码
        $wall_id = $this->request->post( 'wall_id', 0 );//分拣墙ID
        $wId = $this->request->post( 'w_id', 0 );//分拣墙ID
        //$wId = AdminService::organizeWareHouseData($this->getUserId());
        try {
            $orderInfo = $this->OrderPickWallService->checkOrderUniqueCode(['unique_code' => $unique_code, 'current_generation' => 6]);//查询已质检商品

            if (empty( $orderInfo )) {
                throw new ValidateException( '不存在该店内码,或仓库信息不对' );
            }

            if (isset( $orderInfo['o_exception_status'] ) && $orderInfo['o_exception_status'] != 1) {
                throw new ValidateException( '订单有异常，请处理完异常再分拣' );
            }

            if (isset( $orderInfo['order_pick_type'] ) && $orderInfo['order_pick_type'] == 1) {
                throw new ValidateException( '是单品订单请直接交给生产线' );
            }

            if (isset( $orderInfo['exception_status'] ) && $orderInfo['exception_status'] != 1) {
                #$return = array('status' => 0, 'msg' => '该店内码有异常--' . $this->_exception_code[$res[0]['exception_id']]);
                throw new ValidateException( '该店内码有异常' );
            }

            if (empty($wId)) {
                throw new ValidateException( '请选择仓库！' );
            }

            if($wId != $orderInfo['w_id']){
                throw new ValidateException( '店内码不属于当前选择仓库！' );
            }

            $status = $orderInfo['current_generation'] ?? 0;
            switch ($status) {
                case -1:
                    $msg = '该店内码所属订单停止发货，请勿继续操作';
                    break;
                case 1:
                    $msg = '该店内码还未分配快递公司，请稍后操作';
                    break;
                case 2:
                    $msg = '该店内码还未匹配店内码，请稍后操作';
                    break;
                case 3:
                    $msg = '该店内码还未导出拣货单，请稍后操作';
                    break;
                case 4:
                    $msg = '该店内码还未出库，请稍后操作';
                    break;
                case 5:
                    $msg = '该店内码还未进行质检';
                    break;
                case 6:
                    //校验成功继续调用查询该店内码在当前分拣墙的哪个格子里，并且查询相关订单信息，
                    $walls = PublicCode::order_pick_wall;
                    if (isset( $walls[$wall_id] )) {
                        $wall = $walls[$wall_id];
                        $order_branch_code = $orderInfo['branch_serial_no'];
                        $return = $this->getWallUniqueCode( $order_branch_code, $unique_code, $wall,$wId );
                        $return['wall'] = $wall;
                        $return['order_error_msg'] = '';
                        return $this->returnApi( ResponseCode::SUCCESS, '请求成功', $return );
                    } else {
                        $msg = '分拣墙信息错误';
                    }
                default:
                    $msg = '该店内码已制单完毕,请勿操作';
                    break;
            }

            //抛出异常信息
            if (!empty( $msg )) {
                throw new ValidateException( $msg );
            }
        } catch (\Exception $exception) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, $exception->getMessage() );
        }
    }


    /**
     * 查询当前店内码在哪个分拣墙内且相关订单信息
     * @param $order_branch_code //子订单号
     * @param $unique_code //店内码
     * @param $wall //墙
     * @return array
     * @throws ValidateException
     */
    private function getWallUniqueCode ($order_branch_code, $unique_code, $wall, $wId)
    {
        if (empty( $order_branch_code ) || empty( $unique_code ) || empty( $wall )) {
            throw new ValidateException( '参数错误' );
        }
        //$wId = AdminService::organizeWareHouseData($this->getUserId());

        /**
         *redis存储分拣墙的信息格式
         *1.如果当前用户的当前分拣墙有缓存信息，则判断该店内码和订单号是否存在，
         *如果店内码存在，则把当前店内码更新到格子里已扫店内码数组里，unset未扫店内码的该数据，不存在走2，
         *2.无缓存信息，重新查询当前订单号下所有店内码信息,按照以下格式存储
         *array(
         *     'order_codes'=>array(  //存储已放入分拣墙的订单号：格式为"订单号=>格子编号"
         *         '1706190000'=>1,
         *         '1706190001'=>2,
         *          ....
         *     );
         *     'incodes'=>array(   //存储已放入分拣墙的店内码：格式为"店内码=>格子编号"
         *         'ST00000'=>1,
         *         'ST00002'=>2,
         *          ....
         *     );
         *     'location'=>array(  //60个格子，不超过60个，从1开始,每个格子存储的数据格式
         *         01=>array(
         *             'order_code'=>'1706190000', //订单号
         *             'scan_incodes'=>array(     //已扫描店内码数组,第一次加载订单数据时把相关数据存储进去
         *                 'ST00000'=>1,
         *                 'ST00002'=>2,
         *                 ....
         *             ),
         *             'no_scan_incodes'=>array(  //未扫描店内码数据
         *                 'ST00000'=>1,
         *                 'ST00002'=>2,
         *                 ....
         *             ),
         *         ),
         *         02=>array(
         *             ....
         *         ),
         *         ....
         *         60=>array()
         *     )
         *)
         */
        $key = sprintf(self::$PICK_WALL_CACHE_KEY, $wId . '_' . $wall);
        $expire = self::$PICK_WALL_CACHE_EXPIRE;
        $max_num = self::$PICK_WALL_CACHE_MAX_NUM;
        $result = redis()->get( $key );
        //获取当前订单号的所有unique_code
        $order_branchs = $this->OrderPickWallService->getOrderUniqueCodes( ['branch_serial_no' => $order_branch_code, 'not_unique_code' => $unique_code,'current_generation'=>[3,4,5,6]] );
        $no_incodes = array();

        //判断订单有无异常，订单商品有无异常，商品状态是否有不是6的
        if (!empty( $order_branchs )) {
            foreach ($order_branchs as $val) {
                if ($val['delivery_type'] == 2) {
                    if ($val['ob_exception_status'] == 1) {
                        $no_incodes[$val['unique_code']] = $val['unique_code'];
                    }
                } else {
                    $no_incodes[$val['unique_code']] = $val['unique_code'];
                }
            }
            $no_incodes = array_flip( $no_incodes );
        }

        if ($result) {
            $result = json_decode( $result, true );
            //获取key剩余生存时间
            $ttl = redis()->ttl( $key );
            //存在分拣墙缓存，则查询该订单号是否存在
            if (isset( $result['order_codes'][$order_branch_code] ) && !empty( $result['order_codes'][$order_branch_code] )) {
                $location_id = $result['order_codes'][$order_branch_code];
                //查询店内码是否存在,存在直接返回店内码所在格子数
                if (!isset( $result['incodes'][$unique_code] )) {
                    //不存在店内码，把店内码写入和订单号所在的格子数一样更新到incodes,然后push到location的scan_incodes里，unset掉no_scan_incodes里的数据
                    $result['incodes'][$unique_code] = $location_id;
                    $result['location'][$location_id]['scan_incodes'][] = $unique_code;
                    unset( $result['location'][$location_id]['no_scan_incodes'][$unique_code] );
                    //更新缓存
                    redis()->set( $key, json_encode( $result ), $ttl );
                }
                $scan_num = count( $result['location'][$location_id]['scan_incodes'] );
                $no_scan_num = count( $result['location'][$location_id]['no_scan_incodes'] );
                $html = $scan_num . '/' . ($scan_num + $no_scan_num);
                $css = $no_scan_num == 0 ? 'bg-ok' : 'bg-no';
                $return = array('status' => 1, 'id' => $location_id, 'html' => $html, 'css' => $css, 'order_code' => $order_branch_code, 'scan_incodes' => $result['location'][$location_id]['scan_incodes'], 'no_scan_incodes' => $result['location'][$location_id]['no_scan_incodes']);
            } else {
                //不存在该订单号缓存,所有数据需全新写入,先计算格子放到哪一个了,超过最大数量，报错
                $count = count( $result['location'] );
                if ($count < $max_num) {
                    $location_id = $count + 1;
                    if ($location_id < 10) $location_id = '0' . $location_id;
                    $result['order_codes'][$order_branch_code] = $location_id;
                    $result['incodes'][$unique_code] = $location_id;
                    $result['location'][$location_id]['order_code'] = $order_branch_code;
                    $result['location'][$location_id]['scan_incodes'][] = $unique_code;
                    $result['location'][$location_id]['no_scan_incodes'] = $no_incodes;
                    $scan_num = count( $result['location'][$location_id]['scan_incodes'] );
                    $no_scan_num = count( $result['location'][$location_id]['no_scan_incodes'] );
                    $html = $scan_num . '/' . ($scan_num + $no_scan_num);
                    $css = $no_scan_num == 0 ? 'bg-ok' : 'bg-no';
                    $return = array('status' => 1, 'id' => $location_id, 'html' => $html, 'css' => $css, 'order_code' => $order_branch_code, 'scan_incodes' => $result['location'][$location_id]['scan_incodes'], 'no_scan_incodes' => $result['location'][$location_id]['no_scan_incodes']);
                    //更新缓存
                    redis()->set( $key, json_encode( $result ), $ttl );
                } else {
                    throw new ValidateException( '该墙已满，请换一个' );
                }
            }
        } else {
            $result = array();
            $location_id = '01';
            $result['order_codes'][$order_branch_code] = $location_id;
            $result['incodes'][$unique_code] = $location_id;
            $result['location'][$location_id]['order_code'] = $order_branch_code;
            $result['location'][$location_id]['scan_incodes'][] = $unique_code;
            $result['location'][$location_id]['no_scan_incodes'] = $no_incodes;
            redis()->set( $key, json_encode( $result ), $expire );
            $scan_num = count( $result['location'][$location_id]['scan_incodes'] );
            $no_scan_num = count( $result['location'][$location_id]['no_scan_incodes'] );
            $html = $scan_num . '/' . ($scan_num + $no_scan_num);
            $css = $no_scan_num == 0 ? 'bg-ok' : 'bg-no';
            $return = array('status' => 1, 'id' => $location_id, 'html' => $html, 'css' => $css, 'order_code' => $order_branch_code, 'scan_incodes' => $result['location'][$location_id]['scan_incodes'], 'no_scan_incodes' => $result['location'][$location_id]['no_scan_incodes']);
        }
        if ($return['status'] == 1) {
            $location = $result['location'];
            foreach ($location as $key => $val) {
                //循环每个格子，判断该显示什么颜色
                $scan_num = count( $val['scan_incodes'] );
                $no_scan_num = count( $val['no_scan_incodes'] );
                $new_location[$key]['html'] = $scan_num . '/' . ($scan_num + $no_scan_num);
                if ($no_scan_num == 0) {
                    $new_location[$key]['css'] = 'bg-ok';
                } else {
                    $new_location[$key]['css'] = 'bg-no';
                }
            }
            $return['new_location'] = $new_location;
        }

        return $return;
    }


    /**
     * @RequestMapping(path="/orderPickWall/getBlock", methods="post")
     */
    public function getBlock ()
    {
        $wall_id = $this->request->post( 'wall_id', 0 );//分拣墙ID
        $block_id = $this->request->post( 'block_id', 0 );//格子ID
        $wId = $this->request->post( 'w_id', 0 );//仓库id
        $result = [];
        //$wId = AdminService::organizeWareHouseData($this->getUserId());
        try {
            $walls = PublicCode::order_pick_wall;
            if (isset( $walls[$wall_id] )) {
                $wall = $walls[$wall_id];
                if (empty($wId)) {
                    throw new ValidateException( '请选择仓库！' );
                }
                $key = sprintf( self::$PICK_WALL_CACHE_KEY, $wId. '_' . $wall );
                $wall_info = redis()->get( $key );
                if ($wall_info) {
                    $wall_info = json_decode( $wall_info, true );
                    if (isset( $wall_info['location'][$block_id] )) {
                        $return = $wall_info['location'][$block_id];
                        $result = array('wall' => $wall, 'order_code' => $return['order_code'], 'scan_incodes' => $return['scan_incodes'], 'no_scan_incodes' => $return['no_scan_incodes']);
                    } else {
                        throw new ValidateException( '无数据' );
                    }
                } else {
                    throw new ValidateException( '无数据' );
                }
            }
        } catch (\Exception $exception) {
            return $this->returnApi( ErrorCode::SERVER_ERROR, $exception->getMessage() );
        }
        return $this->returnApi( ResponseCode::SUCCESS, '请求成功', $result );
    }
}