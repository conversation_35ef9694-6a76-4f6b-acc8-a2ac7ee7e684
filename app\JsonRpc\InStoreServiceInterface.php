<?php
declare(strict_types=1);

namespace App\JsonRpc;


interface InStoreServiceInterface
{

    /**
     * 创建调拨
     * @param array $data [
     *    "arrival_no" => 预约单号，
     *    "type" => 入库类型， 1=采购入库，3调拨入库
     *    "batch_type" => 批次类型 1= 店内码，2=条行码
     *    "admin_id" => 用户id，
     *    "admin_name" => 用户姓名
     * ]
     * @return mixed
     */
    public function add( array $data);

    /**
     * 详情页
     * @param int $id
     * @param int $page
     * @param int $limit
     */
    public function detailList(array $where = [],int $page = 1, int $limit = 10);

    /**
     * 详情页
     * @param int $id
     * @param int $page
     * @param int $limit
     */
    public function diffList(array $where = [],int $page = 1, int $limit = 10);
    /**
     * 入库单列表
     * @param int $page
     * @param int $limit
     * @param array $where [
     *      "w_id":"仓库id",
     *      "in_stock_no":"批次号",
     *      "purchase_code":"采购单号",
     *      "sku_id":"sku",
     *      "spu_id":"spu",
     *      "unique_code":"spu",
     *      "supplier_ids":"供应商",
     *      "spu_no":"货号",
     *      "status":"状态",
     *      "start_time":"开始时间",
     *      "end_time":"结束时间"
     * ]
     * @param array|string[] $field
     * @return array
     */
    public function list(int $page = 1, int $limit = 10, array $where = [], array $field = ['*']);

    /**
     * 反向调拨列表
     * @param int $page
     * @param int $limit
     * @param array $where [
     *      "in_w_id":"调入仓库id",
     *      "out_w_id":"调出仓库id",
     *      "order_no":"调拨责任单号",
     *      "allot_no":"调拨单号",
     *      "allot_diff_no":"调拨差异单号",
     *      "sku_id":"sku",
     *      "spu_id":"spu",
     *      "unique_code":"spu",
     *      "spu_no":"货号",
     *      "status":"状态",
     *      "start_check_at":"开始时间",
     *      "end_check_at":"结束时间"
     * ]
     * @param array|string[] $field
     * @return array
     */
    public function revAllotList(int $page = 1, int $limit = 10, array $where = [], array $field = ['*']);

    /**
     * 详请
     * @param int $id
     */
    public function info(int $id);

    /**
     * 根据id导出调拨详情
     * @param $id
     */
    public function exportInfo($id);

    /**
     * 根据查询条件导出调拨详情
     * @param $where [
     *      "serial_no":"调拨单号",
     *      "sku_id":"sku",
     *      "spu_id":"spu",
     *      "out_w_id":"调出方",
     *      "in_w_id":"凋入方",
     *      "admin_id":"制单人",
     *      "spu_name":"商品名",
     *      "status":"状态",
     *      "start_time":"开始时间",
     *      "end_time":"结束时间"
     * ]
     */
    public function exportSearch(array $where = []);

    /**
     * 详请
     * @param int $id
     */
    public function cancel(int $id);

    /**
     * 货品入库文件导入
     * @param $data[
     *   "serial_no":单号,
     *   "import_data": [
     *      "sku_id": SKU
     *      "barcode": 条码
     *      "stock_nums" 数量
     *   ]
     * ]
     */
    public function import(array $data);

    /**
     * 确认入库
     * @param $data[
     *   "serial_no":单号,
     *   "task_id":任务id,
     *   "import_type":入库方式 1=店内码,2=条码 ,
     *   "admin_id":入库人id,
     *   "admin_name":入库人,
     *   "list_cache_key": 数据缓存key
     * ]
     */
    public function confirm(array $data);

    /**
     * 完成入库
     * @param $taskId 任务单id
     */
    public function finish($taskId);

    /**
     * 获取入库单数据
     * @param array $where
     */
    public function getInStoreInfo(array $where);
    public function getInStoreInfoByAds(array $where);

    public function getInStockInfoByKeys($keys,$values);

    /**
     * 修改条码
     * @param array $params [
     *      in_store_id => 任务id,
     *      changeList => [
     *          [
     *              old_barcode => 旧条码,
     *              new_barcode => 变更条码
     *          ]
     *      ],
     * ]
     */
    public function changeBarcode(array $params );

    /**
     * 修改店内码条码
     * @param array $params [
     *      in_store_id => 任务id,
     *      changeList => [
     *          [
     *              unique_code => 店内码,
     *              new_barcode => 变更条码
     *          ]
     *      ],
     * ]
     */
    public function changeUniqueCodeBarcode(array $params );

    /**
     * 根据入库明细id获取列表
     * @param array $ids
     */
    public function detailByIds(array $ids );

    /**
     * 批量下载差异列表
     * @param int $id
     * @param int $page
     * @param int $limit
     */
    public function batDiffList(array $where = [],int $page = 1, int $limit = 10);

    /**
     * @param $params [
     *   batch_type => 批次类型,
     *   changeList => 变更数据列表,
     * ]
     * @return string
     */
    public function batChangeCode($params);

    /**
     * 获取箱号质检的入库详情
     * @param int $qualityId
     * @return array
     */
    public function getInDataByBoxQualityId(int $qualityId);
}

