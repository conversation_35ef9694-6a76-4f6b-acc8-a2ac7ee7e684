<?php
declare(strict_types=1);

namespace App\JsonRpc;

interface UniqueCodeServiceInterface
{
    /**
     * 创建店内码前缀
     * @param array $params
     * @return mixed
     */
    public function prefixList();

    /**
     * 创建店内码前缀
     * @param array $params
     * @return mixed
     */
    public function addPrefix(array $params);


    /**
     * 调拨单列表
     * @param int $page
     * @param int $limit
     * @param array $where [
     *      "prefix_id":"前缀id"
     * ]
     * @param array|string[] $field
     * @return array
     */
    public function list(int $page = 1, int $limit = 10, array $where = []);

    /**
     * 获取 店内码
     * @param array $unique_codes
     * @param array|string[] $filed
     * @return array
     */
    public function getUniqueCodes(array $unique_codes, array $filed = ['*']);

    /**
     * 释放店内码和恢复对应货架库存
     * @param array $params
     */
    public function releaseStock(array $params);

    /**
     * 查单个
     * @param array $where
     * @param array|string[] $filed
     * @return \Hyperf\Database\Model\Builder|\Hyperf\Database\Model\Model|object|null
     */
    public function getUniqueCode(array $where, array $filed = ['*']);

    /**
     * 创建 店内码 批次
     * @param int $prefix_id
     * @param int $num
     * @param array $extend [admin_id=>'',admin_name=>'']
     * @return int
     */
    public function generateBatch(int $prefix_id, int $num, array $extend);

    /**
     * 根据批次 生成店内码
     * @param int $batchId
     * @return array
     */
    public function downloadUniqueCode(int $batchId);

    /**
     * 根据批次 生成店内码
     * @param int $batchId
     * @return array
     */
    public function generateUniqueCode(int $batchId);

    /**
     * 前缀 最后的 截止序号
     * @param int $prefix_id
     * @return int|mixed
     */
    public function getEndNumber(int $prefix_id);

    /**
     * 获取 前缀id
     * @param string $prefix
     * @return \Hyperf\Utils\HigherOrderTapProxy|int|mixed|void
     */
    public function getPrefixIdByName(string $prefix);

    /**
     * 创建批次
     * @param array $params
     * @return mixed
     */
    public function addBatch(array $params);

    public function getStocks(array $where);
}
