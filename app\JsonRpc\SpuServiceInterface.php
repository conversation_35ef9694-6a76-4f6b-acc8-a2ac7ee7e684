<?php

namespace App\JsonRpc;


interface SpuServiceInterface
{
    public function getSpuByIds (array $ids);

    public function getSpuInfoById (int $spu_id);

    public function getSpuList (int $page = 1, int $limit = 10, array $where = [], array $field = []);

    /**
     * 获取品牌列表
     * @param array $where
     * @param int $page
     * @param int $limit
     * @param string $orderByField
     * @param string $orderByType
     * @return mixed
     */
    public function getGoodsBrands(array $where = [], int $page = 1, int $limit = 10, string $orderByField = 'id', string $orderByType = 'asc');

    public function importSpuOneKey (array $data);

    /**
     * 查找sku列表详情
     * @param int $spu_id
     * @return array
     */
    public function getSkuListBySpuId(int $spu_id);
    /**
     * 获取spu的总销售库存
     * @param int $spu_id
     * @return int|mixed
     */
    public function getSpuSpecNums(int $spu_id);

    /**
     * 查单个
     * @param array $where
     * @param array|string[] $filed
     * @return mixed
     */
    public function getSpu(array $where, array $filed = ['*']);

    /**
     * 查多个
     * @param array $where
     * @param array|string[] $filed
     * @return mixed
     */
    public function getSpus(array $where = [], array $filed = ['*']);
}
