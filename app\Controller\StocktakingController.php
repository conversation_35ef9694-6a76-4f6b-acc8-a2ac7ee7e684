<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use App\Amqp\Producer\CommonProducer;
use App\Constants\ErrorCode;
use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\Constants\SerialType;
use App\Exception\BusinessException;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\CrontabService;
use App\JsonRpc\CrontabServiceInterface;
use App\JsonRpc\InStoreService;
use App\JsonRpc\InStoreServiceInterface;
use App\JsonRpc\SerialNoServiceInterface;
use App\JsonRpc\StockAdjustServiceInterface;
use App\JsonRpc\StocktakingServiceInterface;
use App\JsonRpc\TemplateSetServiceInterface;
use App\JsonRpc\WmsStocktakingServiceInterface;
use App\Service\HashService;
use Hyperf\Amqp\Producer;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Utils\Collection;

/**
 * @Controller()
 */
class StocktakingController extends AbstractController
{
    /**
     * @Inject ()
     * @var StockAdjustServiceInterface
     */
    private $StockAdjustService;

    /**
     * @Inject ()
     * @var Producer
     */
    private $Producer;
    /**
     * @Inject ()
     * @var InStoreServiceInterface
     */
    private $InStoreService;

    /**
     * @Inject ()
     * @var CrontabServiceInterface
     */
    private $CrontabService;

    /**
     * @Inject ()
     * @var HashService
     */
    private $hashService;

    /**
     * @Inject ()
     * @var AdminServiceInterface
     */
    private $AdminService;

    /**
     * @Inject ()
     * @var WmsStocktakingServiceInterface
     */
    private $WmsStocktakingService;

    /**
     * @Inject ()
     * @var SerialNoServiceInterface
     */
    private $SerialNoService;

    /**
     * 新建盘点任务
     * @RequestMapping(path="/stocktaking/addTask", methods="get,post")
     *
     */
    public function addTask(RequestInterface $request)
    {
        if ($request -> isMethod("post")) {
            $params = $request -> all();
            var_dump('提交5555===',$params);
            $validator = validate()->make($params, [
                'name' => 'required|string',
                'w_id' => 'required|numeric|gt:0',
                'st_type' => 'required|numeric|in:4,5,6,7',
            ],[
                'name.required'        => '盘点名称必填',
                'name.string'         => '盘点名称必须字符串',
                'w_id.required'        => '仓库ID必填',
                'w_id.integer'         => '仓库ID必须为正整数',
                'st_type.required'        => '盘点类型必填',
                'st_type.integer'         => '盘点类型必须为正整数',
            ]);
            if ($validator->fails()) {
                var_dump( $validator->errors()->first() );
                return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
            }
            var_dump('333');
            if (!isset($params['start_time']) || empty($params['start_time'])) {
                var_dump( $params['start_time']);
                return $this->returnApi( ErrorCode::REQUEST_ERROR, 'start_time必须' );
            }

            // 登录用户
            $userInfo = $this->session->get('userInfo');

            try {
                $params['admin_id'] = $userInfo['uid'];
                $params['admin_name'] = $userInfo['nickname'];
                $params['st_no'] = $this -> SerialNoService -> generate(SerialType::I,(int)$params['st_type']);

                var_dump('params==',$params);

                //$data['st_status'] = empty($data['st_status']) || !isset($data['st_status']) ? 1 : $data['st_status'] ;
                $ret = $this -> WmsStocktakingService -> addStocktakingTask($params);

                /********************************************记录日志****************************************************/
                // 当前登录账户数据
                $userInfo = $this->session->get('userInfo');
                // 记录日志数据
                $logData = [
                    'snow_id' => $this->request->getAttribute('snow_id'),
                    'op_id' => $ret['st_id'],
                    'op_type' => '创建盘点任务', // 操作类型
                    'op_content' => '盘点ID：'.$ret['st_id'], // 操作内容
                    'op_time' => date('Y-m-d H:i:s'), // 操作时间
                    'model_name' => 'stocktaking', // 操作模块
                    'st_status' => 1,  // 盘点状态: 1=初盘中，2=复盘中，3=待审核，4=已完成，5=已作废，6=驳回
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                    'remark' => "创建盘点任务，盘点ID：".$ret['st_id']
                ];
                wlog((string)$logData['snow_id'], $logData);
                /********************************************记录日志****************************************************/
                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
            } catch (\Exception $exception) {
                $ret = $exception -> getMessage();
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
            }

        }
        return $this -> show("/stocktaking/addTask");
    }
//    public function addTask(RequestInterface $request)
//    {
//        if ($request -> isMethod("post")) {
//            $params = $request -> all();
//            var_dump('提交===',$params);
//            $validator = validate()->make($params, [
//                'name' => 'required|string',
//                'w_id' => 'required|numeric|gt:0',
//                //'start_time' => 'required|date',
//                'info_ids' => 'required|string',
//                'st_type' => 'required|numeric|in:4,5,6,7',
//            ],[
//                'name.required'        => '盘点名称必填',
//                'name.string'         => '盘点名称必须字符串',
//                'w_id.required'        => '仓库ID必填',
//                'w_id.integer'         => '仓库ID必须为正整数',
//                'st_type.required'        => '盘点类型必填',
//                'st_type.integer'         => '盘点类型必须为正整数',
//                'info_ids.required'        => '盘点内容必填',
//                'info_ids.string'         => '盘点内容必须为字符串',
//            ]);
//            if ($validator->fails()) {
//                var_dump( $validator->errors()->first() );
//                return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
//            }
//
//            if (!isset($params['start_time']) || empty($params['start_time'])) {
//                var_dump( $params['start_time']);
//                return $this->returnApi( ErrorCode::REQUEST_ERROR, 'start_time必须' );
//            }
//
//            // 处理info_ids成数组
//            if (isset($params['info_ids']) && !empty($params['info_ids'])) {
//                // 将文本转成数组
//                $params['info_ids'] = array_filter(explode(PHP_EOL,$params['info_ids']));//str_replace(PHP_EOL,',',$params['info_ids']);
//            }
//
//            // 登录用户
//            $userInfo = $this->session->get('userInfo');
//
//            try {
//                $params['admin_id'] = $userInfo['uid'];
//                $params['admin_name'] = $userInfo['nickname'];
//                $params['st_no'] = $this -> SerialNoService -> generate(SerialType::I,(int)$params['st_type']);
//
//                var_dump('params==',$params);
//
//                //$data['st_status'] = empty($data['st_status']) || !isset($data['st_status']) ? 1 : $data['st_status'] ;
//                $ret = $this -> WmsStocktakingService -> addStocktakingTask($params);
//                /********************************************如果是定时任务，则创建倒计时任务****************************************************/
//                if (isset($ret['snap_now']) && !$ret['snap_now']) { // 非及时快照（定时快照）
//                    var_dump('非即时快照，开始创建快照定时任务。。。');
//                    $snapParams = [
//                        "st_id" => (string)$ret['st_id'],
//                        'st_type' => (string)$ret['st_type'],
//                        'info_ids' => $ret['info_ids'],
//                        'w_id' => (string)$ret['w_id'],
//                    ];
//                    // 添加定时任务
//                    // 任务格式：{"task_expr":"echo test","params":"","type":"0","exec_time":"2021-01-25 19:15:32"}
//                    $taskData = [
//                        'name' => $ret['name'],
//                        'task_expr' => 'wmsStocktaking',
//                        'params' => json_encode($snapParams),
//                        'type' => 0,
//                        'exec_time' => $ret['start_time'],
//                    ];
//                    var_dump("快照参数==",$snapParams);
//                    var_dump("任务参数==",$taskData);
//                    $this -> CrontabService -> addTimeOutCrontab($taskData);
//                    var_dump('AAAAA');
//
//                }
//
//
//                /********************************************记录日志****************************************************/
//                // 当前登录账户数据
//                $userInfo = $this->session->get('userInfo');
//                // 记录日志数据
//                $logData = [
//                    'snow_id' => $this->request->getAttribute('snow_id'),
//                    'op_id' => $ret['st_id'],
//                    'op_type' => '创建盘点任务', // 操作类型
//                    'op_content' => '盘点ID：'.$ret['st_id'], // 操作内容
//                    'op_time' => date('Y-m-d H:i:s'), // 操作时间
//                    'model_name' => 'stocktaking', // 操作模块
//                    'st_status' => 2,  // 盘点状态: 1=待盘点，2=盘点中，3=待审核，4=已完成，5=已作废，6=驳回
//                    'admin_id' => $userInfo['uid'],
//                    'admin_name' => $userInfo['nickname'],
//                    'remark' => "创建盘点任务，盘点ID：".$ret['st_id']
//                ];
//                wlog((string)$logData['snow_id'], $logData);
//                /********************************************记录日志****************************************************/
//                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
//            } catch (\Exception $exception) {
//                $ret = $exception -> getMessage();
//                return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
//            }
//
//        }
//        return $this -> show("/stocktaking/addTask");
//    }
    /**
     * 盘点详情
     * @RequestMapping(path="/stocktaking/detail", methods="get,post")
     */
    public function detail(RequestInterface $request)
    {
        $params = $request -> all();
        $id = $params['id'] ?? 0;
        $detail = $this -> WmsStocktakingService -> getStocktakingTaskOne((int)$id);

        return $this->show('stocktaking/detail',['detail' => $detail]);

    }

    /**
     * 任务详情 - 货架分布
     * @RequestMapping(path="/stocktaking/shelfDistributionList", methods="get,post")
     */
    public function getShelfDistributionList(RequestInterface $request)
    {
        $params = $request -> all();
        var_dump($params);
        $page = $params['page'] ?? 1;
        $limit= $params['limit'] ?? 10;
        $stId = $params['st_id'] ?? 0;
        try {
            $result = $this -> WmsStocktakingService -> getShelfDistributionList((int)$page, (int)$limit, (int)$stId);
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 任务详情 - 货架分布 详情
     * @RequestMapping(path="/stocktaking/shelfDistributionDetail", methods="get,post")
     */
    public function getShelfDistributionDetail(RequestInterface $request)
    {
        $params = $request -> all();
        var_dump($params);
        $page = $params['page'] ?? 1;
        $limit= $params['limit'] ?? 10;
        $stId = $params['st_id'] ?? 0;
        $shelfLine = $params['shelf_line'] ?? '';
        try {
            $result = $this -> WmsStocktakingService -> getShelfDistributionDetail((int)$page, (int)$limit, (int)$stId, $shelfLine);
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 上传盘点数据
     * @RequestMapping(path="/stocktaking/uploadStData", methods="get,post")
     */
    public function uploadStData(RequestInterface $request)
    {
        // 将上传的盘点数据存入redis
        $params = $request -> all();
        $userInfo = $this -> session -> get('userInfo');
        $uniqueKey = $userInfo['uid'].'_st_real_data_unique_code'; // 店内码盘点
        $barcodeKey = $userInfo['uid'].'_st_real_data_barcode'; // 条形码盘点
        $key = 1 == $params['st_way'] ? $uniqueKey : $barcodeKey;

        // 先删除
        $redis = redis();
        $redis -> del($key);

        if (!isset($params['st_way']) || empty($params['st_way'])) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,"请先选择盘点方式",'');
        }

        $validator = validate()->make($params, [
            'st_type' => 'required|integer',
            'st_way' => 'required|integer',
        ],[
            'st_type.required'=>'盘点类型（st_type）必须',
            'st_way.required'=>'盘点方式（st_way）必须',
            'st_type.integer'=>'盘点类型（st_type）必须是整数',
            'st_way.integer'=>'盘点方式（st_way）必须是整数',
        ]);
        try {
            if ($validator->fails()) {
                return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
            }

            if (isset($params['st_type']) && 5 == $params['st_type'] && 1 != $params['st_way']) {
                return $this -> returnApi(ErrorCode::REQUEST_ERROR,"【按店内码】盘点的录入数据类型必须为【店内码】",'');
            }

            if (isset($params['st_type']) && 6 == $params['st_type']) {
                return $this -> returnApi(ErrorCode::REQUEST_ERROR,"【按RFID】盘点的数据请通过PDA系统录入",'');
            }

            if (isset($params['st_type']) && 7 == $params['st_type'] && 2 != $params['st_way']) {
                return $this -> returnApi(ErrorCode::REQUEST_ERROR,"【按条形码】盘点的录入数据类型必须为【条形码】",'');
            }

            $stWay = $params['st_way'];

            if (!$request->hasFile('file')) {
                return $this->returnApi(ErrorCode::REQUEST_FILE_ERROR, ErrorCode::getMessage(ErrorCode::REQUEST_FILE_ERROR));
            }
            $file = $request->file('file');
            $file -> getPathInfo();
            $tplKey = $stWay == 1 ? 'stocktaking_unique_code_txt' : 'stocktaking_barcode_txt';
            $metaData = getTemplateInfo($tplKey);
            var_dump('metaData==',$metaData);

            $metaData = explode(',',$metaData['key']);
            $txtData = readTxt($file,$metaData);

            // 将上传数据以货架排为单位汇总统计
            $handledData = $this -> handleStData($txtData,$stWay);

            $saveResult = $redis -> set($key,json_encode($handledData),60 * 30);
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$saveResult);
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }

    }

    /**
     * 将上传数据以货架排为单位汇总统计
     * @param $data
     */
    private function handleStData($data,$stWay)
    {
        var_dump("data==handle_data=",$data);
        $adminInfo = $this -> AdminService -> idsToNameList(array_column($data,'admin_id'));
        var_dump("adminInfo=",$adminInfo);
        $summaryData = [];
        //if ( 1 == $stWay ) { // 店内码
        $arr = [];
        foreach ($data as $k => $v) {
            $strArr = explode('-',$v['shelf_code']);
            $shelfLine = $strArr[0].'-'.$strArr[1];
            $searchRes = array_search2($summaryData,'shelf_line',$shelfLine); // 如果找到，则返回的结果是改元素的所在下标key
            $arr['shelf_line'] = $shelfLine;
            $arr['gather_admin_name'] = $adminInfo[$v['admin_id']];

            if( 1 == $stWay ) {
                if ($searchRes !== false) { // 存在，原来基础上数量+1
                    $summaryData[$searchRes]['real_stock'] = $summaryData[$searchRes]['real_stock'] + 1;
                } else { // 不存在
                    $arr['real_stock'] = 1;
                    array_push($summaryData,$arr);
                }
            } else if( 2 == $stWay ) {
                if ($searchRes !== false) { // 存在，原来基础上数量+1
                    $summaryData[$searchRes]['real_stock'] = $summaryData[$searchRes]['real_stock'] + $v['num'];
                } else { // 不存在
                    $arr['real_stock'] = $v['num'];
                    array_push($summaryData,$arr);
                }
            }

            // 详情数据追加采集人姓名
            $data[$k]['admin_name'] = $adminInfo[$v['admin_id']];
        }
        $result['summary_data'] = $summaryData;
        $result['detail_data'] = $data;
        return $result;
    }

    /**
     * 任务详情 - 从redis获取上传的盘点数据列表
     * @RequestMapping(path="/stocktaking/getUploadStData", methods="get,post")
     */
    public function getUploadStData(RequestInterface $request)
    {
        // 将上传的盘点数据存入redis
        $params = $request -> all();
        $userInfo = $this -> session -> get('userInfo');
        $uniqueKey = $userInfo['uid'].'_st_real_data_unique_code'; // 店内码盘点
        $barcodeKey = $userInfo['uid'].'_st_real_data_barcode'; // 条形码盘点
        $key = 1 == $params['st_way'] ? $uniqueKey : $barcodeKey;
        try {
            $result = redis() -> get($key);
            var_dump('upload_data==',json_decode($result,true));
            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",json_decode($result,true));
        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

    /**
     * 确定录入 - 保存上传的盘点数据
     * @RequestMapping(path="/stocktaking/saveSbData", methods="get,post")
     */
    public function saveSbData(RequestInterface $request)
    {
        $params = $request -> all();
        if (!isset($params['st_way']) || empty($params['st_way'])) {
            return $this -> returnApi(ErrorCode::REQUEST_ERROR,"盘点方式st_way必须",'');
        }

        $validator = validate()->make($params,[
            'st_id' => 'required|integer',
            'st_way' => 'required|integer',
        ],[
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_id.integer'=>'盘点ID（st_id）必须是整数',
            'st_way.required'=>'盘点方式（st_way）必须',
            'st_way.integer'=>'盘点方式（st_way）必须是整数',
        ]);

        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        $stWay = $params['st_way'];
        $stId = $params['st_id'];

        $userInfo = $this -> session -> get('userInfo');
        $uniqueKey = $userInfo['uid'].'_st_real_data_unique_code'; // 店内码盘点
        $barcodeKey = $userInfo['uid'].'_st_real_data_barcode'; // 条形码盘点
        $key = 1 == $stWay ? $uniqueKey : $barcodeKey;

        // 从redis取出数据
        $stData = redis() -> get($key);
        var_dump('st_data==',$stData);
        $stData = json_decode($stData,true)['detail_data'];
        var_dump('cur_detail_data==',$stData);

        try {
            $result = $this -> WmsStocktakingService -> stDynamic($params['w_id'],$stId,$stWay,$userInfo['uid'],$stData);

            /********************************************记录日志****************************************************/
            // 当前登录账户数据
            $userInfo = $this->session->get('userInfo');
            // 记录日志数据
            $logData = [
                'snow_id' => $this->request->getAttribute('snow_id'),
                'op_id' => $params['st_id'],
                'op_type' => $this -> getOpType(7), // 操作类型
                'op_content' => '数据录入：'. count($stData),//'盘点ID：'.$ret['st_id'], // 操作内容
                'st_status' => 0,  //录入，默认无状态设置 // 盘点状态: 1=初盘中，2=复盘中，3=待审核，4=已完成，5=已作废，6=驳回
                'admin_id' => $userInfo['uid'],
                'admin_name' => $userInfo['nickname'],
                'op_time' => date('Y-m-d H:i:s'), // 操作时间
                'model_name' => 'stocktaking', // 操作模块
                'remark' => '数据录入：'. count($stData) // $ret ? '成功' : '失败' // PublicCode::ST_STATUS[$params['st_status']]
            ];
            wlog((string)$logData['snow_id'], $logData);
            /********************************************记录日志****************************************************/
            var_dump('result==',$result);
            if ($result['result']) {
                return $this -> returnApi(ErrorCode::SUCCESS,$result['msg'],$result['result']);
            } else {
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$result['msg']);
            }

        } catch (\Exception $exception) {
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
        }
    }

//    public function saveSbData(RequestInterface $request)
//    {
//        $params = $request -> all();
//        $validator = validate()->make($params,[
//            'st_id' => 'required|integer',
//            'st_way' => 'required|integer',
//            'w_id' => 'required|integer',
//        ],[
//            'st_id.required'=>'盘点ID（st_id）必须',
//            'st_id.integer'=>'盘点ID（st_id）必须是整数',
//            'w_id.required'=>'仓库ID（w_id）必须',
//            'w_id.integer'=>'仓库ID（w_id）必须是整数',
//            'st_way.required'=>'盘点方式（st_way）必须',
//            'st_way.integer'=>'盘点方式（st_way）必须是整数',
//        ]);
//
//        if ($validator->fails()) {
//            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
//        }
//
//        $stWay = $params['st_way'];
//        $stId = $params['st_id'];
//        $wId = $params['w_id'];
//
//        $userInfo = $this -> session -> get('userInfo');
//        $uniqueKey = $userInfo['uid'].'_st_real_data_unique_code'; // 店内码盘点
//        $barcodeKey = $userInfo['uid'].'_st_real_data_barcode'; // 条形码盘点
//        $key = 1 == $stWay ? $uniqueKey : $barcodeKey;
//
//        // 从redis取出数据
//        $stData = redis() -> get($key);
//        $stData = json_decode($stData,true)['detail_data'];
//
//        try {
//            $prdData = [
//                'st_id' => $stId,
//                'st_way' => $stWay,
//                'w_id' => $wId,
//                'st_data' => $stData,
//                'admin_id' => $userInfo['uid'],
//            ];
//            // 异步投递任务
//            $this -> Producer ->produce(new CommonProducer($prdData,"bigoffs_stocktaking_add"));
//            var_dump("消息投递完成。。。");
//
//            /********************************************记录日志****************************************************/
//            // 当前登录账户数据
//            $userInfo = $this->session->get('userInfo');
//            // 记录日志数据
//            $logData = [
//                'snow_id' => $this->request->getAttribute('snow_id'),
//                'op_id' => $params['st_id'],
//                'op_type' => $this -> getOpType(7), // 操作类型
//                'op_content' => '数据录入：'. count($stData),//'盘点ID：'.$ret['st_id'], // 操作内容
//                'st_status' => 0,  //录入，默认无状态设置 // 盘点状态: 1=初盘中，2=复盘中，3=待审核，4=已完成，5=已作废，6=驳回
//                'admin_id' => $userInfo['uid'],
//                'admin_name' => $userInfo['nickname'],
//                'op_time' => date('Y-m-d H:i:s'), // 操作时间
//                'model_name' => 'stocktaking', // 操作模块
//                'remark' => '数据录入：'. count($stData) // $ret ? '成功' : '失败' // PublicCode::ST_STATUS[$params['st_status']]
//            ];
//            wlog((string)$logData['snow_id'], $logData);
//            /********************************************记录日志****************************************************/
//            return $this -> returnApi(ErrorCode::SUCCESS,"正在录入，请勿刷新");
////            if ($result['result']) {
////                return $this -> returnApi(ErrorCode::SUCCESS,$result['msg']);
////            } else {
////                return $this -> returnApi(ErrorCode::SERVER_ERROR,$result['msg'],$result["data"]);
////            }
//
//        } catch (\Exception $exception) {
//            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
//        }
//    }
//    public function saveSbData(RequestInterface $request)
//    {
//        $params = $request -> all();
//        if (!isset($params['st_way']) || empty($params['st_way'])) {
//            return $this -> returnApi(ErrorCode::REQUEST_ERROR,"盘点方式st_way必须",'');
//        }
//
//        $validator = validate()->make($params,[
//            'st_id' => 'required|integer',
//            'st_way' => 'required|integer',
//            ],[
//                'st_id.required'=>'盘点ID（st_id）必须',
//                'st_id.integer'=>'盘点ID（st_id）必须是整数',
//                'st_way.required'=>'盘点方式（st_way）必须',
//                'st_way.integer'=>'盘点方式（st_way）必须是整数',
//            ]);
//
//        if ($validator->fails()) {
//            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
//        }
//
//        $stWay = $params['st_way'];
//        $stId = $params['st_id'];
//
//        $userInfo = $this -> session -> get('userInfo');
//        $uniqueKey = $userInfo['uid'].'_st_real_data_unique_code'; // 店内码盘点
//        $barcodeKey = $userInfo['uid'].'_st_real_data_barcode'; // 条形码盘点
//        $key = 1 == $stWay ? $uniqueKey : $barcodeKey;
//
//        // 从redis取出数据
//        $stData = redis() -> get($key);
//        var_dump('st_data==',$stData);
//        $stData = json_decode($stData,true)['detail_data'];
//        var_dump('cur_detail_data==',$stData);
//
//        try {
//            $result = $this -> WmsStocktakingService -> saveSbData($stId,$stWay,$stData);
//
//            /********************************************记录日志****************************************************/
//            // 当前登录账户数据
//            $userInfo = $this->session->get('userInfo');
//            // 记录日志数据
//            $logData = [
//                'snow_id' => $this->request->getAttribute('snow_id'),
//                'op_id' => $params['st_id'],
//                'op_type' => $this -> getOpType(7), // 操作类型
//                'op_content' => '数据录入：'. count($stData),//'盘点ID：'.$ret['st_id'], // 操作内容
//                'st_status' => 0,  //录入，默认无状态设置 // 盘点状态: 1=待盘点，2=盘点中，3=待审核，4=已完成，5=已作废，6=驳回
//                'admin_id' => $userInfo['uid'],
//                'admin_name' => $userInfo['nickname'],
//                'op_time' => date('Y-m-d H:i:s'), // 操作时间
//                'model_name' => 'stocktaking', // 操作模块
//                'remark' => '数据录入：'. count($stData) // $ret ? '成功' : '失败' // PublicCode::ST_STATUS[$params['st_status']]
//            ];
//            wlog((string)$logData['snow_id'], $logData);
//            /********************************************记录日志****************************************************/
//
//            return $this -> returnApi(ErrorCode::SUCCESS,"操作成功",$result);
//        } catch (\Exception $exception) {
//            return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
//        }
//    }

    /**
     * 盘点任务列表
     * @RequestMapping(path="/stocktaking/taskList", methods="get,post")
     */
    public function getTaskList(RequestInterface $request)
    {
        if ($request -> isMethod('post')) {
            $params = $request -> all();

            try {
                $where = $params['where'] ?? [];
                $page = $params['page'] ?? 1;
                $limit = $params['limit'] ?? 10;
                //$where = $this -> getTempData();
                $authWIds = getAuthWIds();
                $ret = $this -> WmsStocktakingService -> getStocktakingTaskList($authWIds,(int)$page, (int)$limit, $where);

                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
            } catch (\Exception $exception) {
                $ret = $exception -> getMessage();
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
            }
        }

        return $this -> show("/stocktaking/taskList");

    }

    // 临时，用完即删
    public function getTempData()
    {
        // 条码	系统库存数量	复盘数量
        return [
            "4713507007057,0,0",
            "4897042171869,24,23",
            "4897042170480,0,0",
            "4897042171227,0,0",
            "8934760212927,50,49",
            "4897047803475,0,0",
            "4897047803468,1,0",
            "6958260001012,3,3",
            "6958260000435,29,27",
            "8801068395976,2,0",
            "8801068395969,5,0",
            "6973752430019,1,0",
            "6973752430002,0,0",
            "6972240151894,128,126",
            "6971072900052,2,1",
            "6971072900045,0,0",
            "6971072900038,0,0",
            "6923567880226,1,0",
            "6924790600711,0,0",
            "6956367187486,1,1",
            "6956367187479,9,9",
            "6956367182436,0,0",
            "6954128052868,32,33",
            "6954128052844,2,0",
            "6954128052820,0,0",
            "6954128052813,3,0",
            "6970948760318,63,63",
            "6974563650023,157,156",
            "6920856701309,0,0",
            "4897107710125,1,1",
            "6937821529813,303,300",
            "6937821529806,272,269",
            "6937821529929,0,0",
            "6937821520919,8,0",
            "6937821520902,0,0",
            "6959977222691,1,1",
            "6945705876304,0,0",
            "6945705876298,0,0",
            "6946050105927,0,0",
            "6946050105903,0,0",
            "6946050104746,1,0",
            "6946050104548,1,0",
            "6946050104524,0,0",
            "6946050105750,0,0",
            "6946050105743,0,0",
            "6946050105583,0,0",
            "6937451883040,2,0",
            "6937451883033,0,0",
            "6937451883019,0,0",
            "6973210830030,362,360",
            "6972087951800,0,0",
            "6972087951602,33,32",
            "6972087951817,2,0",
            "9588871328238,55,55",
            "4895235508102,0,0",
            "6956706889538,26,22",
            "6956706889156,65,73",
            "6956706888593,2,0",
            "6956706888319,15,18",
            "6911988014825,69,69",
            "6911988007209,82,82",
            "6911988006783,125,125",
            "6911988000682,258,258",
            "6924911901628,2,0",
            "6924911901611,0,0",
            "6914973604254,84,95",
            "6914973604261,24,24",
            "6914973604230,45,45",
            "6914973604223,38,35",
            "6914973603653,0,0",
            "6924762323006,0,0",
            "6974297990181,0,0",
            "6974297990143,82,81",
            "6950242809562,64,64",
            "8809489790806,40,0",
            "8809489790790,0,0",
            "6928497834189,10,10",
            "6928497834172,5,4",
            "6928497833915,0,0",
            "89686727074,0,0",
            "6971148950899,1,0",
            "6971148950868,0,0",
            "6971148950677,0,0",
            "6947929617206,92,91",
            "6973738930335,1,0",
            "6951981825370,0,0",
            "6951981824458,0,0",
            "6951981824441,1,0",
            "6951981824434,0,0",
            "8991102381222,1,0",
            "8991102381024,0,0",
            "8991102380805,0,0",
            "6971846930148,240,240",
            "6940188809023,18,19",
            "6940188808965,5,4",
            "6940188805964,37,37",
            "6940188805957,27,26",
            "6940188805940,29,27",
            "6940188805827,2,7",
            "6940188805810,4,0",
            "6940188804479,2,0",
            "6940188804066,0,0",
            "6940188804035,0,0",
            "6940188803618,0,0",
            "6940188803595,1,0",
            "6940188803588,50,0",
            "6923589200453,0,0",
            "6923589200415,0,0",
            "6923589200347,0,0",
            "6923589200279,0,0",
            "6971931360058,0,0",
            "6957747548736,2,1",
            "6957747506514,153,153",
            "6957747501113,126,124",
            "6925271100171,113,114",
            "6925271100133,69,69",
            "6925271100720,4,4",
            "6925271100713,150,150",
            "6925271100478,39,39",
            "6925271100300,0,0",
            "6925271100270,96,35",
            "6925271100263,28,26",
            "6925271100249,17,15",
            "6925271100126,28,27",
            "6974031910017,41,39",
            "8803556850035,1,0",
            "6921678105092,0,0",
            "6921678105085,0,0",
            "6931144401023,84,119",
            "6920907800968,4,3",
            "6920907803044,100,100",
            "6920907800302,24,24",
            "6920907810363,0,0",
            "6920907810028,110,110",
            "6920907810004,16,15",
            "6920907803020,171,170",
            "6920907800944,140,141",
            "6920907808254,205,201",
            "6972032970153,25,25",
            "6972032970146,15,14",
            "6931630080732,174,171",
            "6973306430151,105,104",
            "6973306430144,131,131",
            "6936605800834,0,0",
            "6971426300101,1,0",
            "6971426300095,1,0",
            "6971426300903,1,0",
            "6971426300880,6,6",
            "6973949830042,6,0",
            "6922133651048,3,0",
            "6922133606086,164,160",
            "5060600921278,0,0",
            "5060600927843,0,0",
            "5060600927768,0,0",
            "5060600927454,1,1",
            "5060600926792,0,0",
            "9588552330376,40,41",
            "9588552330345,72,72",
            "9588552330031,144,143",
            "9588552329097,135,136",
            "9588552321602,88,88",
            "9588552302809,46,36",
            "6972318510080,0,0",
            "6947435731069,0,0",
            "6947435731052,18,18",
            "6947435731045,0,0",
            "6947435732110,12,10",
            "6947435732103,39,38",
            "6947435732097,118,116",
            "6947435732035,91,92",
            "6947435732028,136,135",
            "6947435731014,113,114",
            "6947435731007,40,37",
            "6947435732059,143,142",
            "6952675341411,0,0",
            "6952675340216,54,54",
            "6901668250056,304,300",
            "6901668200235,53,53",
            "6901668200082,130,132",
            "6901668200013,102,101",
            "6901668001115,141,141",
            "6901668005755,97,98",
            "6901668005731,372,372",
            "4897082059196,0,0",
            "4897082059189,1,0",
            "4897082059172,0,0",
            "6926355202651,123,120",
            "4897110242354,115,115",
            "6971529860939,72,68",
            "6971529860786,107,106",
            "6971529860779,5,5",
            "6955773916123,190,189",
            "6920731700205,1,0",
            "8809420331631,41,36",
            "8809420331624,121,117",
            "8809420332409,81,81",
            "6941608106494,45,44",
            "6930755601150,162,162",
            "6924743924147,42,41",
            "6924743915848,0,0",
            "6924743915817,2,0",
            "6924743915763,0,0",
            "4897085300424,0,0",
            "6959011901827,135,134",
            "6959011901759,102,99",
            "6946431800199,44,44",
            "6946431800168,0,0",
            "6972588782927,114,116",
            "6972588782736,152,149",
            "6972588782729,104,104",
            "6972588782538,99,99",
            "6972588782033,79,76",
            "6956706886162,8,7",
            "6933661500524,0,0",
            "6933661500517,45,45",
            "6970545530611,82,80",
            "6970545530604,82,82",
            "6932965116615,101,101",
            "6932965116622,40,39",
            "6973643230339,59,59",
            "6973643230209,1,0",
            "6973643230193,0,0",
            "6958652300068,0,0",
            "6958652300037,0,0",
            "6958652300020,12,14",
            "6958652300013,3,0",
            "6958652300006,0,0",
            "6973547490716,1,0",
            "6973547490693,3,2",
            "6973547490303,230,234",
            "6973547490013,104,100",
            "6949187391430,0,0",
            "6949187391423,2,2",
            "6949187391416,140,119",
            "6949187399009,77,77",
            "6949187398958,102,103",
            "6949187398828,102,103",
            "6971657870251,169,168",
            "6926265313515,178,175",
            "6926265313386,3,2",
            "6926265313010,0,0",
            "6971867951610,172,172",
            "6914973206106,128,113",
            "6924161380822,142,140",
            "6970673889940,31,29",
            "6970673889933,41,40",
            "6970673887502,0,0",
            "6974228160201,62,60",
            "6971675971053,76,77",
            "6941499304641,208,207",
            "6909995110326,86,85",
            "6909995101676,35,34",
            "6935041525103,38,38",
            "6959479300439,23,22",
            "6927127600675,45,45",
            "6926475202470,19,18",
            "6911988014887,154,154",
            "6926475204320,120,119",
            "6902934990164,80,79",
            "6926475204412,142,139",
            "6926475204054,155,154",
            "6926475204047,135,132",
            "6970000311373,15,14",
            "6970000311212,0,1",
            "6970000310994,0,0",
            "6970000310574,48,47",
            "6970000310185,2,0",
            "6970113721052,15,13",
            "6972319285536,1,1",
            "6972319280081,9,9",
            "6972319280012,26,68",
            "6942506201014,123,123",
            "6942506200550,0,0",
            "6942506201366,128,68",
            "6956074802474,147,87",
            "6956074802467,2,0",
            "6972178230241,43,43",
            "6971198149816,4,0",
            "6971198148956,1,0",
            "6971198148864,93,89",
            "6932107008907,187,234",
            "6932107000246,3,0",
            "4897050056677,132,136",
            "4897050056653,143,143",
            "6952166801684,302,300",
            "6939621400750,44,44",
            "6939621400613,12,11",
            "6973376370135,0,68",
            "6973376370128,0,0",
            "6973376370111,0,0",
            "6973376370012,5,10",
            "6973376370005,2,0",
            "6956984100363,64,64",
            "6970512358293,81,81",
            "6970512358286,8,8",
            "6974438430088,127,127",
            "6974438430064,79,79",
            "6974438430040,337,337",
            "8935024149669,0,0",
            "6906791583637,0,0",
            "6925303730574,497,497",
            "6933266600049,122,123",
            "6906907101014,440,439",
            "6933266600025,1036,1035",
            "6933266606553,39,39",
            "6922255451427,40,38",
            "6922255447833,364,361",
            "6948960112750,72,72",
            "6948960102980,129,126",
            "6948960102188,147,147",
            "6948960100924,140,142",
            "6948960100221,179,180",
            "6948960100009,354,354",
            "6948960113542,2,0",
            "6915878219093,763,768",
            "6948960107671,697,697",
            "6974315350157,164,164",
            "6971425850263,2,0",
            "6932529211107,476,475",
            "6937451883101,1,0",
            "6937451883095,2,0",
            "6937451883088,2,0",
            "6923450603666,340,339",
            "6914171780132,153,153",
            "6954767442778,259,259",
            "6954767441481,794,792",
            "6954767442075,1045,1044",
            "6933833902798,3,0",
            "6933833902781,0,0",
            "6951726600019,4,3",
            "6924097907704,0,0",
            "6924097907681,0,0",
            "6920202888883,1478,1471",
            "6957923601200,0,0",
            "6957923600517,48,0",
            "6957923600210,1,0",
            "6957923600111,3,0",
            "6921681119093,0,0",
            "5060600922190,18,18",
            "6921317998849,46,45",
            "6921317998825,144,144",
            "6921317996500,512,511",
            "6921317996364,226,226",
            "6921317940312,3,0",
            "6921317905038,199,178",
            "6921317993790,572,568",
            "6921317989465,564,564",
            "6921317957631,485,483",
            "6921317906370,120,120",
            "6921317905168,595,594",
            "6921317905014,539,545",
            "6954767413877,219,219",
            "6954767412573,918,918",
            "6954767410388,144,132",
            "6954767423579,630,628",
            "6902538007169,299,297",
            "6902538005141,561,559",
            "6902538004052,328,326",
            "6902538004045,535,533",
            "8690792017755,193,193",
            "8681049021076,203,203",
            "6906907103018,586,582",
            "6906907403088,601,596",
            "6972884000183,912,911",
            "6938309700533,337,335",
            "6938309700526,391,390",
            "6933266600117,176,476",
            "6920180209724,749,764",
            "6920180209601,312,413",
            "6918976450115,122,122",
            "6918976550082,23,12",
            "6973903120202,164,152",
            "6973903120141,311,293",
            "6973903120110,228,232",
            "6973903120103,210,203",
            "6973903120011,333,350",
            "6935718000148,2695,2616",
            "6956367338697,109,109",
            "6956367338666,318,318",
            "6956367338680,836,1030",
            "4891028164456,1,0",
            "4891028720232,209,205",
            "4891028705949,458,457",
            "4891028706656,126,124",
            "6926475203507,233,232",
            "6954767433073,249,250",
            "6954767430386,886,886",
            "6954767432076,1110,1108",
            "6925303733704,568,567",
            "6970399927759,22,29",
            "6970399923157,7,6",
            "6970399923096,8,5",
            "6970399921559,276,280",
            "6970399920439,275,270",
            "6970399920415,5,0",
            "6970399920132,492,493",
            "6970399920118,595,593",
            "6971964860822,0,0",
            "6971964860815,19,0",
            "6971964860808,0,0",
            "6970410588020,25,25",
            "6971931360010,0,0",
            "6930802600044,21,21",
            "6970987733809,31,30",
            "6970987733793,36,35",
            "6958652301737,19,0",
            "6958652300792,44,0",
            "8801111610636,0,0",
            "6971095493357,49,49",
            "6971095493142,48,48",
            "6973547490211,0,0",
            "6973547490105,75,76",
            "6907992514338,4,1",
            "6907992512761,31,31",
            "6907992507385,31,31",
            "6907992504070,0,0",
            "6971964861379,5,0",
            "6971964861362,0,0",
            "6970565870520,0,0",
            "6970565870629,1,0",
            "6970565870346,4,1",
            "6970565871121,12,9",
            "6970565871114,1,0",
            "8076809572217,18,18",
            "8076809572200,50,50",
            "8076800195057,20,20",
            "4897878140305,0,0",
            "4897878140039,0,0",
            "4897878140015,0,0",
            "6915993303714,32,32",
            "6915993303653,57,57",
            "6915993304902,52,50",
            "6915993303509,44,44",
            "6915993300966,45,33",
            "6915993300072,48,0",
            "6915993304452,22,22",
            "6915993303103,35,35",
            "6915993300720,48,48",
            "6915993200051,72,72",
            "6915993304872,133,133",
            "6915993304322,130,130",
            "6915993304292,150,150",
            "6915993303561,122,123",
            "6915993303363,37,33",
            "6915993303028,90,90",
            "6915993302236,96,99",
            "6915993302229,61,60",
            "6915993302205,37,38",
            "6971356940026,883,888",
            "6971356940019,266,264",
            "6941499122627,36,36",
            "6941499114691,281,297",
            "6941499102919,338,338",
            "6941499100335,289,288",
            "6944910356250,38,38",
            "6944910336153,351,358",
            "6944910318210,34,34",
            "6944910318012,45,45",
            "6949909050133,6,6",
            "6941499113908,100,100",
            "6949909050041,682,679",
            "6949909050034,728,727",
            "6907476352609,73,73",
            "6907476222605,22,22",
            "6907476024209,39,39",
            "6907476014200,30,30",
            "6907476012107,29,29",
            "6972802401566,39,35",
            "6917935002945,0,0",
            "6917935002907,0,0",
            "6917935002297,0,0",
            "6917935002280,0,0",
            "6917935002242,0,0",
            "6917935002181,1,0",
            "6917935002167,1,0",
            "6917935002150,0,0",
            "6951895604092,70,68",
            "6951895604061,59,55",
            "6951895604054,60,60",
            "6951895604023,46,46",
            "6951895604016,63,68",
            "6951895604009,50,54",
            "6951895603705,17,17",
            "6951895603651,58,58",
            "6951895603217,30,30",
            "6951895602685,47,47",
            "6951895603378,79,79",
            "6951895603354,58,54",
            "6905566316180,53,53",
            "6905566272233,42,42",
            "6905566018886,62,61",
            "6971313330594,2,0",
            "6971313330525,0,0",
            "6971313330471,0,0",
            "6971313330464,1,0",
            "6945640200141,68,68",
            "6945640200110,69,69",
            "6945640200073,70,70",
            "6952675337735,0,0",
            "6903252715613,49,48",
            "6903252017663,43,43",
            "6903252000634,15,13",
            "6903252000771,191,191",
            "6903252714630,265,261",
            "6903252000979,12,16",
            "6921804700269,34,34",
            "6972371311624,2,0",
            "6926392500789,532,531",
            "6924968166780,88,90",
            "6924968116662,89,119",
            "6924968101156,86,86",
            "6924968101149,84,84",
            "6924968101132,86,86",
            "6924968101125,85,85",
            "6924968101491,110,110",
            "6924968100777,68,70",
            "6924968100135,139,142",
            "6927551200052,185,185",
            "6940154300189,10,0",
            "6932438200674,1272,1270",
            "6901894122097,5,5",
            "6901894121168,34,34",
            "6901894122271,24,24",
            "6935816601902,1038,1029",
            "6910019023324,8,8",
            "6910019015299,9,7",
            "6910019022259,7,7",
            "6910019011604,24,24",
            "6910019021276,22,22",
            "6910019022563,19,21",
            "6910019020774,32,31",
            "6910019020767,16,17",
            "6910019023416,14,18",
            "6910019015503,9,9",
            "6910019017323,70,70",
            "6910019021078,14,14",
            "6910019010928,21,21",
            "6910019006402,37,37",
            "6910019024697,1,0",
            "6910019017484,3,3",
            "6910019011154,40,39",
            "4901301361035,46,47",
            "6935816602459,691,691",
            "6973387141397,125,124",
            "6973387141298,1968,1952",
            "6914068032368,24,24",
            "6914068026329,22,22",
            "6914068013138,27,27",
            "6914068016498,28,29",
            "6914068016153,28,30",
            "6914068032436,54,52",
            "6914068020532,137,137",
            "6914068016092,46,46",
            "6914068024400,44,44",
            "6914068015736,111,111",
            "6914068012957,60,60",
            "6914068025667,346,342",
            "6914068024332,11,11",
            "6902088320763,37,37",
            "6902088320749,39,39",
            "6920174752090,6,5",
            "6920174742381,1,1",
            "6920174768794,4,4",
            "6920174745443,11,11",
            "6920174736748,27,27",
            "6920174757156,31,30",
            "6920174749328,0,0",
            "6920174749700,39,39",
            "6920174749731,51,27",
            "6923083028287,1876,1867",
            "6923083024210,911,912",
            "6970032250558,29,30",
            "6970032250541,26,25",
            "6970032250138,34,42",
            "6970032250152,28,28",
            "6922266463235,64,61",
            "6922266467639,26,26",
            "6922266443190,77,77",
            "6922266462283,338,337",
            "6922266461842,25,25",
            "6922266457500,32,32",
            "6922266448164,41,41",
            "6922266443787,38,45",
            "6922266439575,41,41",
            "6922266451058,102,102",
            "6922266448393,111,111",
            "6922266467035,270,267",
            "6922266446153,97,97",
            "6922266462870,151,123",
            "6922266444333,338,338",
            "6922266439148,82,83",
            "6903148091944,50,50",
            "8885010233554,10,8",
            "8885010233547,11,11",
            "6901586106121,37,37",
            "6901586110456,18,18",
            "6901586105094,45,45",
            "6901236385845,39,39",
            "6901236383292,329,317",
            "6901236382882,24,24",
            "6901236382974,55,53",
            "6901236388372,677,677",
            "6901236300374,52,51",
            "6901236300312,92,92",
            "6901236382400,79,79",
            "6901236380505,152,152",
            "6901236380017,167,166",
            "6901236300138,5171,5164",
            "6901236300091,53,52",
            "6921418073070,472,468",
            "6922868291281,0,0",
            "6903244958103,41,41",
            "6922868285266,57,56",
            "6903244950619,93,91",
            "6903244984157,84,84",
            "6922868286621,114,111",
            "6922868285716,330,330",
            "6922868284283,907,842",
            "6923027200151,719,717",
            "6973009161017,3241,3144",
            "6917751430274,271,271",
            "6917751430267,284,285",
            "6917751430281,246,245",
            "6925704489675,31,31",
            "6925704485523,26,26",
            "6925704485516,26,26",
            "6925704485240,25,25",
            "6925704483642,35,35",
            "6925704459678,30,28",
            "6925704459067,24,24",
            "6925704456080,33,31",
            "6925704444223,15,15",
            "6925704444049,12,12",
            "6925704443103,17,17",
            "6925704439984,18,18",
            "6925704438208,23,23",
            "6925704436778,9,9",
            "6925704435832,0,0",
            "6925704435559,11,11",
            "6925704433227,11,11",
            "6925704432909,12,12",
            "6925704431797,16,16",
            "6925704426427,31,31",
            "6925704424744,17,17",
            "6925704424607,34,33",
            "6925704421941,4,4",
            "6925704420821,3,2",
            "6925704420746,6,6",
            "6925704418330,32,33",
            "6925704417999,32,32",
            "6925704417227,20,18",
            "6925704416763,29,29",
            "6925704416718,16,14",
            "6925704413571,26,27",
            "6925704407655,14,14",
            "6925704407570,29,29",
            "6925704407464,5,3",
            "6925704405941,37,35",
            "6925704403534,36,34",
            "6925704402100,22,20",
            "6925704402070,11,9",
            "6925704401806,8,8",
            "6943819117191,4,4",
            "6943819117184,2,2",
            "6943819117177,1,1",
            "6943819117160,0,0",
            "6943819117153,2,2",
            "6943819117146,1,1",
            "6943819117139,3,3",
            "6943819117122,4,4",
            "6943819117115,3,3",
            "6944662597192,4,3",
            "6944662597185,4,4",
            "6944662597178,6,6",
            "6943819102289,4,4",
            "6943819102272,4,4",
            "6943819119027,4,4",
            "6943819119010,4,4",
            "6943819119003,2,2",
            "6943819118990,4,4",
            "6943819118983,4,4",
            "6943819118976,3,3",
            "6943819118969,4,4",
            "6943819118952,4,4",
            "6943819118945,4,4",
            "6943819118938,4,4",
            "6943819118921,4,4",
            "6943819118914,3,3",
            "6943819118907,4,4",
            "6943819118891,4,4",
            "6943819118884,3,3",
            "6943819118877,3,3",
            "6943819116712,4,4",
            "6943819116705,3,0",
            "6943819116699,4,4",
            "6943819116682,4,4",
            "6943819116675,2,2",
            "6943819116668,4,4",
            "6943819116149,3,3",
            "6943819116132,0,0",
            "6943819116125,2,2",
            "6943819116118,0,0",
            "6943819112196,2,2",
            "6943819112189,1,2",
            "6943819112172,4,3",
            "6943819112165,3,3",
            "6943819112158,3,3",
            "6943819112141,4,4",
            "6943819107512,2,2",
            "6943819107505,4,4",
            "6943819107499,4,4",
            "6943819107482,4,4",
            "6943819104573,3,3",
            "6943819104566,1,1",
            "6943819104559,3,3",
            "6943819104542,3,3",
            "6943819104535,4,4",
            "6943819104528,4,4",
            "6943819104511,3,3",
            "6943819104504,2,2",
            "6943819104498,4,4",
            "6943819104481,1,1",
            "6943819104474,4,4",
            "6943819104467,4,4",
            "6943819104450,4,4",
            "6943819104443,4,4",
            "6943819104436,4,4",
            "6943819104429,4,4",
            "6943819102203,2,2",
            "6943819102197,4,4",
            "6943819102180,4,4",
            "6943819102173,3,3",
            "6970032250060,9,9",
            "6917751430458,96,96",
            "6917751461575,105,105",
            "6917751410108,126,126",
            "6917751420190,49,52",
            "6917751420183,27,24",
            "6917751430038,53,54",
            "6917751430021,92,91",
            "6917751430014,87,87",
            "6928787501173,420,420",
            "6928787501111,469,469",
            "6901586111590,0,0",
            "6901586104547,16,15",
            "6901586104950,64,64",
            "6903244122542,115,114",
            "6903244120678,132,132",
            "6923567601296,314,315",
            "6923567601227,305,305",
            "6923567600435,257,257",
            "6922731881809,409,416",
            "6922731881786,379,378",
            "6922731800817,210,209",
            "6922731800770,347,348",
            "6922731800695,320,319",
            "6923589466262,135,135",
            "6923589466231,77,77",
            "6923589452821,95,106",
            "6923589452425,111,92",
            "6923589462455,303,303",
            "6923589462400,161,160",
            "6923589460772,143,143",
            "6923589468044,358,361",
            "6923589461199,581,581",
            "4589772640059,24,25",
            "6903148231562,10,10",
            "6903148230961,10,10",
            "4589772640028,5,5",
            "6903148236420,176,171",
            "6903148236413,106,108",
            "6903148222539,101,102",
            "6903148222508,92,91",
            "6903148254530,268,269",
            "6903148254523,201,203",
            "6903148138250,320,319",
            "6903148137406,303,297",
            "6903148091104,340,225",
            "6903148091074,374,373",
            "6945162268681,13,13",
            "6902088113938,16,16",
            "6902088113907,14,14",
            "6902088323924,26,26",
            "6902088323894,29,29",
            "8806390593984,8,7",
            "9345521001428,17,24",
            "9345521001411,14,11",
            "9345521000933,32,30",
            "9345521000926,3,3",
            "6903148091487,23,23",
            "6903148091449,31,31",
            "6903148108314,39,39",
            "6903148079614,17,17",
            "6903244375221,4,4",
            "6903244372510,11,11",
            "6903244370974,316,316",
            "6903244370950,308,312",
            "6903244370431,275,275",
            "6903244370424,274,269",
            "6903244370998,233,229",
            "6903244370776,235,237",
            "6903244372367,82,82",
            "6903244370899,448,448",
            "6902088134681,4,4",
            "6902088122213,19,19",
            "6903148080085,42,45",
            "6903148080023,41,41",
            "6903148190043,25,23",
            "6903148166468,15,15",
            "6928471360154,40,40",
            "6928471360123,99,100",
            "6934660580227,153,153",
            "6934660580128,167,167",
            "6934660556123,310,304",
            "6934660555126,200,200",
            "6934660552224,222,224",
            "6934660552125,271,271",
            "6934660528649,227,227",
            "6934660528557,187,187",
            "6934660522289,187,187",
            "6934660516745,263,262",
            "6934660551395,224,226",
            "6934660539157,331,329",
            "6934660534169,244,246",
            "6934660521213,486,482",
            "6934660511948,463,463",
            "6934660511658,481,481",
            "6934660529141,431,437",
            "6925911514962,14,14",
            "6925911514924,8,6",
            "6925779697883,2332,2329",
            "6925779697852,441,436",
            "6902088605259,35,34",
            "6902088605228,134,133",
            "6971558610017,459,458",
            "6972603870011,49,49",
        ];
    }

    /**
     * 获取某一任务下的实盘结果
     * @param RequestInterface $request
     * @RequestMapping(path="/stocktaking/getStResultListByStId", methods="get,post")
     */
    public function getStResultListByStId(RequestInterface $request)
    {
        $params = $request -> all();

        try {
            // 导出
            $export = $params['export'] ?? 0;

            $where = $params['where'] ?? [];
            $stId = $params['st_id'];

            if (2 == $export) { // 导出全部
                $page = 1;
                $limit = 100000;
                $ret = $this -> WmsStocktakingService -> getStResultListByStId((int)$stId, (int)$page, (int)$limit, $where);
                $exportUrl= $this -> exportStResult($ret['data']);
                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),['url'=>$exportUrl]);
            }

            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 10;
            $ret = $this -> WmsStocktakingService -> getStResultListByStId((int)$stId, (int)$page, (int)$limit, $where);

            if (1 == $export) {

                $exportUrl= $this -> exportStResult($ret['data']);
                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),['url'=>$exportUrl]);
            }



            // 盘点结果审批单，追加入库批次号
            if (1 == $params['is_audit']) {
                $stResultList = isset($ret['data']) && $ret['data'] ? $ret['data'] : [];
                if ($stResultList) {
                    $vals = [];
                    $keys = []; // TODO: 查看调试返回结果。。。
                    foreach ($stResultList as $k => $v) {
                        $item = [
                            'sku_id' => $v['sku_id'],
                            'shelf_code' => $v['shelf_grid'] ? $v['shelf_grid'] : $v['shelf_seat'],
                            'goods_code_type' => $v['goods_code_type'],
                            'goods_code' => $v['goods_code'],
                        ];
                        if (1 == $v['goods_code_type']) { // 商品码类型：1=店内码，2=条形码，3=epc码
                            $keys = ['sku_id','shelf_code','unique_code'];
                        }
                        if (2 == $v['goods_code_type']) { // 商品码类型：1=店内码，2=条形码，3=epc码
                            $keys = ['sku_id','shelf_code','barcode'];
                        }
                        array_push($vals,$item);
                    }
                    $inStoreNoResult = $this -> InStoreService -> getInStockInfoByKeys($keys,$vals);

                    // 追加入库批次号
                    foreach ($stResultList as &$v2) {
                        if (1 == $v2['goods_code_type']) { // 商品码类型：1=店内码，2=条形码，3=epc码
                            $kvs = [
                                'sku_id' => $v2['sku_id'],
                                'shelf_code'=> $v2['shelf_grid'] ? $v2['shelf_grid'] : $v2['shelf_seat'],
                                'unique_code'=> $v2['goods_code']
                            ];
                        }

                        if (2 == $v2['goods_code_type']) { // 商品码类型：1=店内码，2=条形码，3=epc码
                            $kvs = [
                                'sku_id' => $v2['sku_id'],
                                'shelf_code'=> $v2['shelf_grid'] ? $v2['shelf_grid'] : $v2['shelf_seat'],
                                'barcode'=> $v2['goods_code']
                            ];
                        }

                        $searchItem = $this -> _searchItemByMultiFields($inStoreNoResult,$kvs);  // 系统数据
                        $searchItem = $searchItem ? array_values($searchItem)[0] : [];
                        $v2['in_stock_no'] = $searchItem['in_stock_no'] ?? '';
                    }

                    $ret['data'] = $stResultList;

                }
            }
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    /**
     * 获取某一任务下的盘点卡列表
     * @param RequestInterface $request
     * @RequestMapping(path="/stocktaking/getStCardListByStId", methods="get,post")
     */
    public function getStCardListByStId(RequestInterface $request)
    {
        $params = $request -> all();

        try {
            $where = $params['where'] ?? [];
            $page = $params['page'] ?? 1;
            $stId = $params['st_id'];
            $limit = $params['limit'] ?? 10;
            $ret = $this -> WmsStocktakingService -> getStCardListByStId((int)$stId, (int)$page, (int)$limit, $where);

            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    /**
     * 多字段搜索
     * @param array $arr 待搜索集合
     * @param array $kvs 多字段kv键值对
     */
    public function _searchItemByMultiFields(array $arr,array $kvs)
    {
        return array_filter($arr,function ($item) use ($kvs) {
            var_dump('item==',$item);
            $conditionResult = [];
            foreach ($kvs as $k => $v) {
            var_dump('$item[$k]==',$item[$k]);
            var_dump('$k==',$k);
            var_dump('$v==',$v);
                if ($item[$k] == $v) {
                    array_push($conditionResult,1);
                }
            }
            if (count($conditionResult) === count($kvs)) {
                return $item;
            }
            return false;
        });
    }

    private function exportStResult(array $data){

        $head = [
            'shelf_code' => '系统货架位',
            'gather_shelf_code' => '采集货架位',
            'goods_code_type' => '类型',
            'goods_code' => '店内码/RFID/条形码',
            'book_stock' => '系统数',
            'real_stock' => '采集数量',
            'p_num' => '盘盈数',
            'l_num' => '盘亏数',
            'st_result_type_name' => '结果',
        ];
        $fileName = '盘点结果 - '.date('Y-m-d-H-i-s');
        $eData = [];
        if (!empty($data)) {
            foreach ($data as $k => $v) {
                $shelfCode = !empty($v['shelf_grid']) ? $v['shelf_grid'] : $v['shelf_seat'];
                $gatherShelfCode = !empty($v['gather_shelf_grid']) ? $v['gather_shelf_grid'] : $v['gather_shelf_seat'];
                // 商品码类型：1=店内码，2=条形码，3=epc码
                $goodsCodeType = '';
                if (1 == $v['goods_code_type']) {
                    $goodsCodeType = '店内码';
                }
                if (2 == $v['goods_code_type']) {
                    $goodsCodeType = '条码';
                }
                if (3 == $v['goods_code_type']) {
                    $goodsCodeType = 'epc码';
                }
                array_push($eData,[
                    'shelf_code' => $shelfCode,
                    'gather_shelf_code' => $gatherShelfCode,
                    'goods_code_type' => $goodsCodeType,
                    'goods_code' => $v['goods_code'] ?? '',
                    'book_stock' => $v['book_stock'] ?? '',
                    'real_stock' => $v['real_stock'] ?? '',
                    'p_num' => $v['p_num'] ?? 0,
                    'l_num' => $v['l_num'] ?? 0,
                    'st_result_type_name' => $v['st_result_type_name'] ?? '',
                ]);
            }
        }
        return exportToExcel($head,$eData,$fileName);
    }



    /**
     * 盘点报表页面
     * @param RequestInterface $request
     * @RequestMapping(path="/stocktaking/reportList", methods="get,post")
     */
    public function reportList(RequestInterface $request)
    {
        $params = $request -> all();
        $st_id = $params['st_id'] ?? 0;
        $detail = $this -> WmsStocktakingService -> getStocktakingTaskOne((int)$st_id);

        $statistics = $this -> WmsStocktakingService -> getStatistics((int)$st_id);
        return $this -> show("/stocktaking/reportList",['detail' => $detail,'statistics' => $statistics]);
    }

    /**
     * 获取盘点报表数据
     * @param RequestInterface $request
     * @RequestMapping(path="/stocktaking/getReportList", methods="get,post")
     */
    public function getReportList(RequestInterface $request)
    {
        $params = $request -> all();

        $validator = validate()->make($params, [
            'st_id' => 'required|gt:0',
        ]);

        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        try {
            $stId = $params['st_id'];
            $ret = $this -> WmsStocktakingService -> getReportList($stId);

            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    /**
     * 完成盘点
     * @param RequestInterface $request
     * @RequestMapping(path="/stocktaking/updateStatus", methods="get,post")
     */
    public function updateStatus(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'st_id' => 'required|gt:0',
            'st_status' => 'required|numeric|in:2,3,4,5',
        ]);
        if ($validator->fails()) {
            var_dump( $validator->errors()->first() );
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        try {
            $stId = $params['st_id'] ?? 0;
            $stStatus = $params['st_status'] ?? 0;
            $detail = $this -> WmsStocktakingService -> getStocktakingTaskOne(intval($stId));
            if(empty($detail)){
                return $this->returnApi( ErrorCode::REQUEST_ERROR, '盘点任务不存在');
            }

            // 查询盘点任务综合结果
            $stResult = $this -> WmsStocktakingService -> getStResultSummaryById($stId);
            var_dump('st_result_summary==',$stResult);

            // 如果完成复盘时，盘点盈亏都是0，则直接将盘点状态改为已完成
            if ($stResult['p_num_total'] == 0 && $stResult['l_num_total'] == 0) {
                $stStatus = 4;
            }

            $ret = $this -> WmsStocktakingService -> updateStatus($stId,['st_status' => $stStatus]);

            /********************************************记录日志****************************************************/
            // TODO 商超日志记录盘点完成之前的库存，盘点完成之后的库存数及操作人
            // 当前登录账户数据
            $userInfo = $this->session->get('userInfo');
            // 记录日志数据
            $optype = '';

            $logData = [
                'snow_id' => $this->request->getAttribute('snow_id'),
                'op_id' => $params['st_id'],
                'op_type' => $this -> getOpType($params['st_status']), // 操作类型
                'op_content' => '', // '盘点ID：'.$ret['st_id'], // 操作内容
                'op_time' => date('Y-m-d H:i:s'), // 操作时间
                'model_name' => 'stocktaking', // 操作模块
                'st_status' => $params['st_status'], // 创建任务，默认盘点中  // 盘点状态: 1=待盘点，2=盘点中，3=待审核，4=已完成，5=已作废，6=驳回
                'admin_id' => $userInfo['uid'],
                'admin_name' => $userInfo['nickname'],
                'remark' => '' // $ret ? '成功' : '失败' // PublicCode::ST_STATUS[$params['st_status']]
            ];
            /********************************************记录日志****************************************************/
            wlog((string)$logData['snow_id'], $logData);
            //创建库存调整单: 完成复盘时（st_status=3），才生成库存调整单
            var_dump('stStatus=',$stStatus);
            var_dump('stResult=',$stResult);
            var_dump('if ==',3==$stStatus && ($stResult['p_num_total'] > 0 || $stResult['l_num_total'] > 0));
            if(3==$stStatus && ($stResult['p_num_total'] > 0 || $stResult['l_num_total'] > 0)){
                var_dump('begin to create st_stock_adjust...');
                $ret2 = $this->container->get(StockAdjustServiceInterface::class)->createStockAdjust(['w_id'=>$detail['w_id'],'type'=>1,'source_serial_no'=>$detail['st_no']]);
                var_dump('begin to create st_stock_adjust, result ==',$ret2);
            }
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),[$ret,$ret2]);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
    }

    private function getOpType($stStatus)
    {
        switch ($stStatus) {
            case 4:
                $msg = '完成盘点';
                break;
            case 5:
                $msg = '作废盘点';
                break;
            case 7:
                $msg = '数据录入';
                break;
            default:
                $msg = '';
                break;
        }

        return $msg;
    }

    /**
     * 获取盘点日志列表
     * @param RequestInterface $request
     * @RequestMapping(path="/stocktaking/getStLog", methods="get,post")
     */
    public function getStLog(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'st_id' => 'required|gt:0',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $stId = $params['st_id'];
        $params = [
            'op_id' => $stId,
            'system' => 'wms',
            'model_name' =>'stocktaking',
        ];
        try{
            $ret = getLog($params);
            var_dump('log=====',$ret);
            $retData = [];
            foreach ($ret['data'] as $k => $v) {
                //array_push($retData,json_decode($v['res_params'],true));
                array_push($retData,$v['res_params']);
            }

            $result = [
                'data' => $retData,
                'total' => $ret['total'],
            ];
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$result);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }
        
    }

    /**
     * 新建盘点结果审批单
     * @RequestMapping(path="/stocktaking/addStResultAudit", methods="get,post")
     *
     */
    public function addStResultAudit(RequestInterface $request)
    {
        $params = $request -> all();
        var_dump('params===',$params);
        $validator = validate()->make($params, [
            'st_id' => 'required|numeric|gt:0',
            'st_no' => 'required|string',
            'w_id' => 'required|numeric|gt:0',
            'st_type' => 'required|numeric|in:4,5,6,7',
        ],[
            'st_id.required'       => '盘点ID必填',
            'st_id.integer'        => '盘点ID必须为正整数',
            'st_no.required'       => '盘点单号必填',
            'st_no.string'         => '盘点单号必须字符串',
            'w_id.required'        => '仓库ID必填',
            'w_id.integer'         => '仓库ID必须为正整数',
            'st_type.required'     => '盘点类型必填',
            'st_type.integer'      => '盘点类型必须为正整数',
        ]);
        if ($validator->fails()) {
            var_dump( $validator->errors()->first() );
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }

        // 登录用户
        $userInfo = $this->session->get('userInfo');

        try {
            $params['admin_id'] = $userInfo['uid'];
            $params['admin_name'] = $userInfo['nickname'];

            var_dump('params==',$params);

            //$data['st_status'] = empty($data['st_status']) || !isset($data['st_status']) ? 1 : $data['st_status'] ;
            $ret = $this -> WmsStocktakingService -> addStResultAudit($params);

            /********************************************记录日志****************************************************/
            // 当前登录账户数据
            // 记录日志数据
            $logData = [
                'snow_id' => $this->request->getAttribute('snow_id'),
                'op_id' => $ret['id'],
                'op_type' => '创建盘点结果审批单', // 操作类型
                'op_content' => '审批单ID：'.$ret['id'], // 操作内容
                'op_time' => date('Y-m-d H:i:s'), // 操作时间
                'model_name' => 'stocktakingResult', // 操作模块
                'admin_id' => $userInfo['uid'],
                'admin_name' => $userInfo['nickname'],
                'remark' => "创建盘点结果审批单，ID：".$ret['st_id']
            ];
            wlog((string)$logData['snow_id'], $logData);
            /********************************************记录日志****************************************************/
            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }

    }

    /**
     * 盘点任务列表
     * @RequestMapping(path="/stocktaking/stResultAuditList", methods="get,post")
     */
    public function getStResultAuditList(RequestInterface $request)
    {
        $params = $request -> all();

        try {
            $where = $params['where'] ?? [];
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 10;

            $authWIds = getAuthWIds();
            $ret = $this -> WmsStocktakingService -> getStResultAuditList($authWIds,(int)$page, (int)$limit, $where);

            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }

    }

    /**
     * 盘点任务列表
     * @RequestMapping(path="/stocktaking/stResultAuditDetail", methods="get,post")
     */
    public function getStResultAuditDetail(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params, [
            'st_id' => 'required|numeric|gt:0',
        ],[
            'st_id.required'       => '盘点ID必填',
            'st_id.integer'        => '盘点ID必须为正整数',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $detail = $this -> WmsStocktakingService -> getStocktakingTaskOne((int)$params['st_id']);
        //$detail = $this -> WmsStocktakingService -> getStResultAuditDetail((int)$params['st_id']);

        return $this->show('stocktaking/stResultAuditDetail',['id' => $params['id'],'detail' => $detail,'is_audit'=>$params['is_audit']]);

    }

    /**
     * 盘点结果审批，状态更改（停用）
     * @RequestMapping(path="/stocktaking/updateStAuditStatus", methods="get,post")
     */
    public function updateStAuditStatus(RequestInterface $request)
    {
        $params = $request -> all();
        var_dump('params==aaa',$params);
        $validator = validate()->make($params, [
            'id' => 'required|numeric|gt:0',
            'audit_status' => 'required|numeric|in:1,2,3',
            'audit_remark' => 'required|string',
        ],[
            'st_id.required'       => '盘点ID必填',
            'st_id.integer'        => '盘点ID必须为正整数',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        try {

            $updataData['audit_remark'] = $params['audit_remark'];
            $updataData['audit_status'] = $params['audit_status'];
            $ret = $this -> WmsStocktakingService -> updateStAuditStatus($params['id'],$updataData);

            return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
        } catch (\Exception $exception) {
            $ret = $exception -> getMessage();
            return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
        }

    }

    /**
     * 前端轮询获取录入盘点数据的结果
     * @RequestMapping(path="/stocktaking/getSaveStDataResult", methods="get,post")
     */
    public function getSaveStDataResult(RequestInterface $request)
    {
        $params = $request -> all();
        $validator = validate()->make($params,[
            'st_id' => 'required|integer',
        ],[
            'st_id.required'=>'盘点ID（st_id）必须',
            'st_id.integer'=>'盘点ID（st_id）必须是整数',
        ]);
        if ($validator->fails()) {
            return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
        }
        $userInfo = $this -> session -> get('userInfo');
        // 将结果写入redis，由前端轮询获取结果
        $ResKey = $userInfo['uid'].'_save_st_data_'.$params['st_id'];
        $ResEDataKey = $userInfo['uid'].'_save_st_e_data_'.$params['st_id'];
        $resultData = redis() -> rPop($ResKey);
        $resultEData = redis() -> rPop($ResEDataKey);
        var_dump('result===',$resultData);
        var_dump('resultEData===',$resultEData);
        if ((string)$resultData == "1") {
            $returnData = [
                'result' => "1",
                'eData' => $resultEData ? $resultEData : [],
            ];
            return $this -> returnApi(ResponseCode::SUCCESS,'录入任务已完成，页面将自动刷新',$returnData);
        }
        if ((string)$resultData === "0") {
            $returnData = [
                'result' => "0",
                'eData' => $resultEData ? $resultEData : [],
            ];
            return $this -> returnApi(ResponseCode::SERVER_ERROR,'录入任务失败',$returnData);
        }
        return $this -> returnApi(0,"无结果",'');
    }

}
