<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use App\Constants\ResponseCode;
use App\Exception\BusinessException;
use App\JsonRpc\WarehouseServiceInterface;
use App\Library\Facades\AdminService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use App\Funcs\Common;
use Picqer\Barcode\BarcodeGeneratorPNG;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;
use App\Constants\SerialType;
use App\Library\Facades\InStoreService;
use App\Library\Facades\SendService;
use App\Library\Facades\TaskService;

/**
 * @Controller()
 */
class LogisticsInfoController extends AbstractController
{
    /**
     * @Inject()
     * @var \App\JsonRpc\ArrivalOrderServiceInterface
     */
    private $arrivalOrderService;

    /**
     * @Inject()
     * @var \App\JsonRpc\LogisticsInfoServiceInterface
     */
    private $logisticsInfoService;

    /**
     * @Inject()
     * @var \App\JsonRpc\PurchaseOrderServiceInterface
     */
    private $purchaseOrderService;

    /**
     * @Inject()
     * @var \App\JsonRpc\AdminServiceInterface
     */
    private $adminService;

    /**
     * @Inject()
     * @var ValidatorFactoryInterface
     */
    private $validator;

    /**
     * @Inject
     * @var WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject()
     * @var \App\JsonRpc\SupplierServiceInterface
     */
    private $supplierService;

    /**
     * @Inject()
     * @var \App\JsonRpc\SerialNoServiceInterface
     */
    private $SerialNoService;

    private $rela_type = [
//        '1' => '调拨',
//        '2' => '退返',
        '3' => '采购'
    ];

    private $batch_rela_type = [
        '1' => '调拨',
        '2' => '退返',
//        '3' => '采购'
    ];

    private $send_type = [
        '1' => '物流承运',
        '2' => '自提',
        '3' => '发货方自送'
    ];

    /**
     * @RequestMapping(path="/LogisticsInfo/logistics", methods="get,post")
     */
    public function logistics(RequestInterface $request){
        //获取仓库信息
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        $warehouseList = $this->WarehouseService->getWarehouses(['ids' => $wIds, 'type' => 1], ['id', 'name']);

        //获取供应商信息
        $supplierList = $this->supplierService->getSuppliers([], ['id', 'name']);

        if ($this->isAjax()) {
            $where = $request->input('search');
            $export = $request->input('export',0);
            $page = intval($request->input('page',1));
            $limit = intval($request->input('limit',30));
            if (isset($where['warehouse']) && !empty($where['warehouse'])){
                $where['w_ids'] = explode(',', $where['warehouse']);
            }
            if (isset($where['supplier']) && !empty($where['supplier'])){
                $where['sup_ids'] = explode(',', $where['supplier']);
            }
            //登记时间
            if (isset($where['date']) && !empty($where['date'])) {
                $date_time = explode(' - ', $where['date']);
                $where['start_created_at'] = $date_time[0] . ' 00:00:00';
                $where['end_created_at'] = $date_time[1] . ' 23:59:59';
            }

            if ($where['lay_id'] == 1){//未预约
                $where['neq_cancel'] = true;//未作废
                $where['not_rela_no'] = true;//获取无预约单号的数据
            }elseif($where['lay_id'] == 2){//已预约
                $where['neq_cancel'] = true;
                $where['not_null_rela_no'] = true;
            }elseif($where['lay_id'] == 3){//已作废
                $where['status'] = -1;
            }
            $supplierMap = collect($supplierList)->pluck('name', 'id')->toArray();
            $warehouseMap = collect($warehouseList)->pluck('name', 'id')->toArray();

            if ($export == 1){//导出
                $where['export'] = $export;
                $list = $this->logisticsInfoService->getNewList($where, $limit, $page);
                if (!empty($list)){
                    foreach ($list as &$item){
                        $item['third_party_no'] = $item['third_party_no'] ?? '-';
                        $item['is_amends'] = ($item['is_amends'] == 0) ? '否' : '是';
                        $item['sup_name'] = $supplierMap[$item['sup_id']] ?? '-';
                        $item['w_name'] = $warehouseMap[$item['w_id']] ?? '-';
                        $item['is_send_email'] = ($item['is_send_email'] == 0) ? '否' : '是';
                        $item['detection_box'] = 0;//预留
                    }
                }
                if ($where['lay_id'] == 1 || $where['lay_id'] == 3){
                    $header = [
                        'serial_no' => '登记号',
                        'created_at' => '登记时间',
                        'admin_name' => '登记人',
                        'send_box' => '送货箱数',
                        'send_num' => '送货件数',
                        'recev_box' => '登记箱数',
                        'cancel_at' => '作废时间',
                        'canceler_name' => '作废人',
                    ];
                    if ($where['lay_id'] == 1){
                        $header['remark'] = '备注';
                    }else{
                        $header['cancel_remark'] = '备注';
                    }
                }elseif($where['lay_id'] == 2){
                    $header = [
                        'serial_no' => '登记号',
                        'created_at' => '登记时间',
                        'admin_name' => '登记人',
                        'third_party_no' => '三方发货单号',
                        'arrival_no' => '预约单号',
                        'is_amends' => '后补预约单',
                        'purchase_admin_name' => '负责人',
                        'sup_name' => '供应商',
                        'w_name' => '仓库',
                        'expected_time' => '预计送达时间',
                        'send_box' => '送货箱数',
                        'send_num' => '送货件数',
                        'recev_box' => '登记箱数',
                        'is_send_email' => '差异邮件是否发送',
                        'detection_box' => '质检箱数',
                        'in_store_num' => '入库件数',
                        'diff_num' => '采购差异件数',
                        'remark' => '备注'
                    ];
                }
                $url = exportToExcel($header, $list, '批次交接列表');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }else{
                $list = $this->logisticsInfoService->getNewList($where, $limit, $page);
                if (!empty($list['data'])){
                    foreach ($list['data'] as &$item){
                        $item['third_party_no'] = $item['third_party_no'] ?? '-';
                        $item['is_amends'] = ($item['is_amends'] == 0) ? '否' : '是';
                        $item['sup_name'] = $supplierMap[$item['sup_id']] ?? '-';
                        $item['w_name'] = $warehouseMap[$item['w_id']] ?? '-';
                        $item['is_send_email'] = ($item['is_send_email'] == 0) ? '否' : '是';
                        $item['detection_box'] = 0;//预留
                    }
                }

                //获取各标签数量
                if ($where['lay_id'] == 1){//未预约
                    unset($where['not_rela_no'], $where['neq_cancel']);
                    //已预约数量
                    $where['not_null_rela_no'] = true;
                    $where['neq_cancel'] = true;
                    $yyList = $this->logisticsInfoService->getNewList($where, $limit, $page);
                    unset($where['not_null_rela_no'], $where['neq_cancel']);
                    //已作废数量
                    $where['status'] = -1;
                    $cancelList = $this->logisticsInfoService->getNewList($where, $limit, $page);
                    $hzNums = [
                        'un_nums' => $list['total_number'],
                        'booked_nums' => $yyList['total_number'],
                        'zf_nums' => $cancelList['total_number']
                    ];
                }elseif($where['lay_id'] == 2){//已预约
                    unset($where['not_null_rela_no'], $where['neq_cancel']);
                    //未预约数量
                    $where['not_rela_no'] = true;//获取无预约单号的数据
                    $where['neq_cancel'] = true;
                    $unList = $this->logisticsInfoService->getNewList($where, $limit, $page);
                    unset($where['not_rela_no'], $where['neq_cancel']);
                    //已作废数量
                    $where['status'] = -1;
                    $cancelList = $this->logisticsInfoService->getNewList($where, $limit, $page);
                    $hzNums = [
                        'un_nums' => $unList['total_number'],
                        'booked_nums' => $list['total_number'],
                        'zf_nums' => $cancelList['total_number']
                    ];
                }elseif($where['lay_id'] == 3){//已作废
                    unset($where['status']);
                    //未预约数量
                    $where['not_rela_no'] = true;//获取无预约单号的数据
                    $where['neq_cancel'] = true;
                    $unList = $this->logisticsInfoService->getNewList($where, $limit, $page);
                    unset($where['not_rela_no'], $where['neq_cancel']);
                    //已预约数量
                    $where['not_null_rela_no'] = true;
                    $where['neq_cancel'] = true;
                    $yyList = $this->logisticsInfoService->getNewList($where, $limit, $page);
                    $hzNums = [
                        'un_nums' => $unList['total_number'],
                        'booked_nums' => $yyList['total_number'],
                        'zf_nums' => $list['total_number']
                    ];
                }

                return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total_number'],  'hzNums' => $hzNums , 'limit' => $limit]);
            }
        }

        //获取所有创建过采购单的用户
        $purchaseOrders = $this->purchaseOrderService->getOrders(['status' => 4], ['admin_id']);
        $adminIds = array_column($purchaseOrders, 'admin_id');
        $adminList = $this->adminService->idsToNameList($adminIds);

        // 当前用户是否有审核权限
        $userInfo = $this->session->get('userInfo');
        $verifyAuth = $this->adminService->can($userInfo['uid'], '/LogisticsInfo/verify', getAppType());

        $date = date("Y-m-d", strtotime("-90 days"))." - ". date('Y-m-d');

        $data = [
            'warehouse_list' => $warehouseList,
            'supplier_list' => $supplierList,
            'admin_list' => $adminList,
            'verify_auth' => $verifyAuth,
            'date' => $date
        ];

        return $this->show('logisticsInfo/logistics', $data);
    }

    /**
     * 登记
     * @RequestMapping(path="/LogisticsInfo/add", methods="get,post")
     */
    public function add()
    {
        $data['third_party_no'] = $this->SerialNoService->generate(SerialType::THIRD);;

        $data['send_type_list'] = $this->send_type;

        return $this->show('logisticsInfo/creat', $data);
    }

    /**
     * 校验三方发货单号，获取预约单信息
     * @RequestMapping(path="/LogisticsInfo/checkThridPartyNo", methods="get,post")
     */
    public function checkThridPartyNo(RequestInterface $request){
        $thirdPartyNo = $request->input('third_party_no','');
        $arrivalNo = $request->input('arrival_no','');
        if(!$thirdPartyNo && !$arrivalNo){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '三方发货单号和预约单号不能同时为空!');
        }

        //获取预约单信息
        $where = [
            'status_list' => [0]
        ];
        if (!empty($thirdPartyNo)){
            $where['third_party_no'] = $thirdPartyNo;
        }
        if (!empty($arrivalNo)){
            $where['serial_no'] = $arrivalNo;
        }
        $arrivalOrderList = $this->arrivalOrderService->getOrders($where, ['id','purchase_no','serial_no','delivery_sign','contact','phone','w_id','arrive_num','arrive_time','third_party_no','box_nos']);
        if (empty($arrivalOrderList)){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '不存在预约单！');
        }

        $arrivalSerialNos = array_column($arrivalOrderList, 'serial_no');
        //获取预约单登记信息
        $logisticsInfoList = $this->logisticsInfoService->getLogisticsInfoS(['rela_nos' => $arrivalSerialNos, 'status' => [1, 2, 3]]);

        //检测是否存在预约单
        if (!empty($logisticsInfoList)){
            if (count($logisticsInfoList) == count($arrivalOrderList)){
                if (!empty($thirdPartyNo)){
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '三方发货单号关联的预约单{'.implode(',', $arrivalSerialNos).'}都已登记，请检查！');
                }else if(!empty($arrivalNo)){
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '预约单{'.implode(',', $arrivalSerialNos).'}都已登记，请检查！');
                }
            }
        }
        //检测三方单号对应的预约单是否唯一
        if (count($arrivalOrderList) == 1){
            $arrivalOrder = $arrivalOrderList[0];

            //获取预约数量
            $detail = $this->arrivalOrderService->getOrderDetail(['order_id' => $arrivalOrder['id'], 'get_arrival_num' => true]);
            $box_nos = [];
            if (!empty($arrivalOrder['box_nos'])){
                $box_nos = explode(",", $arrivalOrder['box_nos']);
            }
            $arrivalOrder['send_box'] = count($box_nos);
            $arrivalOrder['send_box_text'] = implode("<br>", $box_nos);
            $arrivalOrder['send_num'] = !empty($detail) ? $detail[0]['num'] : 0;

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $arrivalOrder);
        }else{
            $arrivalOrder['id'] = 0;
            $arrivalOrder['purchase_no'] = "";
            $arrivalOrder['serial_no'] = "";
            $arrivalOrder['delivery_sign'] = "";
            $arrivalOrder['contact'] = "";
            $arrivalOrder['phone'] = "";
            $arrivalOrder['w_id'] = 0;
            $arrivalOrder['arrive_num'] = 0;
            $arrivalOrder['arrive_time'] = "";
            $arrivalOrder['third_party_no'] = "";
            $arrivalOrder['box_nos'] = "";
            $arrivalOrder['send_box'] = 0;
            $arrivalOrder['send_box_text'] = "";
            $arrivalOrder['send_num'] = 0;
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $arrivalOrder);
        }
    }

    /**
     * 明细
     * @RequestMapping(path="/LogisticsInfo/detail/{serial_no}", methods="get,post")
     */
    public function detail(){
        //获取预约登记明细信息
        $serial_no = $this->request->route('serial_no');
        $where = [
            'serial_no' => $serial_no
        ];
        $logisticsInfo = $this->logisticsInfoService->getLogisticsInfo($where);
        $logisticsInfo['freight_price'] = fen2yuan($logisticsInfo['freight_price']);
        $logisticsInfo['send_type_name'] = $this->send_type[$logisticsInfo['send_type']];
        $logisticsInfo['image_list_array'] = [];
        if ($logisticsInfo['image_list'] && strpos($logisticsInfo['image_list'], '[') === false) {
            $logisticsInfo['image_list_array'] = explode(";", $logisticsInfo['image_list']);
        }
        //箱号
        $logisticsInfo['send_boxs'] = [];
        if (!empty($logisticsInfo['rela_no'])){
            //获取预约单信息
            $arrivalOrderList = $this->arrivalOrderService->getOrders(['serial_nos' => [$logisticsInfo['rela_no']]], ['id','purchase_no','serial_no','delivery_sign','contact','phone','w_id','arrive_num','arrive_time','third_party_no','box_nos']);
            $arrivalOrder = $arrivalOrderList[0];
            if (!empty($arrivalOrder['box_nos'])){
                $box_nos = explode(",", $arrivalOrder['box_nos']);
                $logisticsInfo['send_boxs'] = $box_nos;
            }

        }

        $data['info'] = $logisticsInfo;

        //获取log信息
        $logInfo = getLog(['model_name'=>'LogisticsInfo','op_id'=>$logisticsInfo['id']]);
        $data['log'] = $logInfo['data'] ?? [];

        return $this->show('logisticsInfo/detail', $data);
    }

    /**
     * @RequestMapping(path="/LogisticsInfo/index", methods="get,post")
     */
    public function index()
    {
        $id = (int)$this->request->input('id');
        $rela_type = (int)$this->request->input('rela_type');
        if (!$id || !$rela_type) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
        }

        $data['title'] = "到货登记";
        $data['rela_type'] = $rela_type;
        $data['rela_type_list'] = $this->rela_type;
        $data['send_type_list'] = $this->send_type;

        if ($rela_type == 3) { // 采购到货
            $arrivalOrder = $this->arrivalOrderService->getOrder(['id' => $id]);
            if (!$arrivalOrder) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '任务单不存在');
            }
            if (!in_array($arrivalOrder['status'], [0, 1])) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '任务单状态不符合');
            }

            $data['data'] = [
                'rela_no' => $arrivalOrder['serial_no'],
                'linkman' => $arrivalOrder['contact'],
                'mobile' => $arrivalOrder['mobile']
            ];
        }

        return $this->show('logisticsInfo/add', $data);
    }

    /**
     * @RequestMapping(path="/LogisticsInfo/list", methods="get,post")
     */
    public function list(RequestInterface $request){
        $relaNo = $request->input('rela_no','');
        if(!$relaNo){
            throw new BusinessException('来源单号不能为空');
        }
        $page = intval($request->input('page',1));
        $limit = intval($request->input('limit',30));
        $list = $this->logisticsInfoService->getLogisticsList(['rela_no'=>$relaNo],$limit,$page);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['list'], ['count' => $list['total_number'], 'limit' => $limit]);
    }

    /**
     * 创建
     * @RequestMapping(path="/LogisticsInfo/create", methods="get,post")
     */
    public function create()
    {
        if ($this->isAjax()) {
            $data = $this->request->all();
            $rule = [
                'rela_no' => ['string', 'max:30'],
                'rela_type' => ['required', 'integer', 'in:3'],
                'send_type' => ['required', 'integer', 'in:1,3'],
                'express_code' => ['string', 'max:30'],
                'car_code' => ['string', 'max:30'],
                'send_box' => ['required', 'integer'],
                'send_num' => ['required', 'integer'],
                'linkman' => ['string', 'max:20'],
                'mobile' => ['string', 'max:15'],
                'recev_box' => ['required', 'integer'],
                //'recev_num' => ['required', 'integer'],
                //'receiver' => ['required', 'string', 'max:20'],
                //'refuse_box' => ['integer', 'lte:recev_box'],
                //'refuse_num' => ['integer', 'lte:recev_num'],
                'refuser' => ['string', 'max:30'],
                'freight_price' => ['required', 'numeric'],
                //'recev_time' => ['required']
            ];
            $message = [
                'send_box.integer' => '送货箱数必须为整数',
                'send_num.integer' => '送货件数必须为整数',
                'recev_box.integer' => '收货箱数必须为整数',
                //'recev_num.integer' => '收货件数必须为整数',
                //'refuse_box.integer' => '拒收箱数必须为整数',
                //'refuse_num.integer' => '拒收件数必须为整数',
                'freight_price.numeric' => '运费必须为数字',
                //'refuse_box.lte' => '拒收箱数不能大于收货箱数',
                //'refuse_num.lte' => '拒收件数不能大于收货件数',
            ];
            $errors = $this->validator->make($data, $rule, $message);
            if ($errors->fails()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
            }

            $user_info = $this->session->get('userInfo');
            logger()->info('data===========', [$data, isDate($data['recev_time'])]);
            if ($data['send_num'] < 0){
                $data['send_num'] = 0;
            }

            if ($data['rela_type'] == 3) { // 采购到货登记
                $instore = true;
                if (isset($data['diff_box']) && empty($data['diff_box']) && $data['send_box'] != 0){
                    $data['diff_box'] = $data['send_box'] - $data['recev_box'];
                }elseif(!isset($data['diff_box'])){
                    $data['diff_box'] = 0;
                    $instore = false;
                }

                $arrivalOrder = $this->arrivalOrderService->getOrder(['serial_no' => $data['rela_no']]);
                //if (!$arrivalOrder) {
                //    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '任务单不存在');
                //}
                //if (isset($arrivalOrder)) {
                //  return $this->returnApi(ResponseCode::VALIDATE_ERROR, '任务单状态不符合');
                //}

                $logisticsData = [
                    'third_party_no' => $data['third_party_no'] ?? '',
                    'rela_no' => $data['rela_no'],
                    'is_amends' => empty($data['rela_no']) ? 1 : 0,
                    'rela_type' => $data['rela_type'],
                    'send_type' => $data['send_type'],
                    'express_code' => $data['express_code'] ?? '',
                    'car_code' => $data['car_code'] ?? '',
                    'send_box' => $data['send_box'],
                    'send_num' => $data['send_num'],
                    'linkman' => $data['linkman'],
                    'mobile' => $data['mobile'] ?? '',
                    'recev_num' => $data['recev_num'] ?? 0,
                    'recev_box' => $data['recev_box'],
                    'receiver' => $data['receiver'] ?? '',
                    'refuse_box' => (!isset($data['refuse_box']) || empty($data['refuse_box']) ) ? 0 : $data['refuse_box'],
                    'refuse_num' => (!isset($data['refuse_num']) || empty($data['refuse_num'])) ? 0 : $data['refuse_num'],
                    'refuser' => $data['refuser'] ?? '',
                    'freight_price' => $data['freight_price'] * 100,
                    'recev_time' => (isset($data['recev_time']) && !empty($data['recev_time']) && $data['recev_time'] != "0000-00-00 00:00:00" && isDate($data['recev_time'])) ? $data['recev_time'] : date('Y-m-d H:i:s'),
                    'sign_remark' => $data['sign_remark'],
                    'image_list' => $data['image_list'] ? implode(';', array_filter(explode(';', $data['image_list']))) : '',
                    'admin_id' => $user_info['uid'],
                    'admin_name' => $user_info['nickname'],
                    'diff_box' => $data['diff_box'],
                    'delivery_diff' => $data['diff_box'] != 0 ? 1 : 0,
                    'is_send_email' => 0,
                    'diff_verify' => $data['diff_box'] != 0 ? 0 : 1,
                    'status' => $data['diff_box'] != 0 ? 2 : 3,
                ];
                $logisticsInfoId = $this->logisticsInfoService->addLogisticsInfo($logisticsData);

                // 更新预约单为已到货  无差异时
                if (isset($arrivalOrder) && $logisticsInfoId && $arrivalOrder['status'] == 0 && $data['diff_box'] == 0) {
                    $this->arrivalOrderService->updateOrder(['serial_no' => $data['rela_no'], 'id' => $arrivalOrder['id']], [
                        'real_arrival_time' => (isset($data['recev_time']) && !empty($data['recev_time']) && $data['recev_time'] != "0000-00-00 00:00:00" && isDate($data['recev_time'])) ? $data['recev_time'] : date('Y-m-d H:i:s'),
                        'status' => 1
                    ]);
                    logger()->info('instore=============', [$instore]);
                    //生成入库批次
                    if ($instore){
                        //检测生成的入库批次是否为中转入库批次
                        $isTransfer = 0;
                        //获取入库批次
                        $inStores = InStoreService::getInStoreInfo(['arrival_no' => $data['rela_no'], 'statusS' => [1,0]]);
                        if (isset($arrivalOrder['allot_w_id']) && !empty($arrivalOrder['allot_w_id']) && $arrivalOrder['w_id'] != $arrivalOrder['allot_w_id'] && count($inStores) == 0){//存在调入仓 && 调入仓id不为空 && 预约仓!=调入仓 && 不存在入库批次
                            $isTransfer = 1;
                        }

                        try {
                            $userInfo = $this->getUserInfo();
                            $params['admin_id'] = $userInfo['uid'];
                            $params['admin_name'] = $userInfo['nickname'];
                            $params['serial_no'] = $data['rela_no'];
                            $params['batch_type'] = 1;
                            $params['type'] = 1;
                            $params['is_transfer'] = $isTransfer;
                            $info = InStoreService::add($params);
                        } catch (\Exception $e) {
                            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
                        }
                        $instoreLog = [];
                        $instoreLog['op_id'] = $info['id'];
                        $instoreLog['req_router_name'] = '创建任务单';
                        $instoreLog['model_name'] = 'InStore';
                        $instoreLog['snow_id'] = newSnowId();
                        newWLog(strval($this->request->getAttribute('snow_id')), $instoreLog, true, false);
                    }
                }

                //添加log
                $logisticsLog = [];
                $logisticsLog['op_id'] = $logisticsInfoId;
                $logisticsLog['req_router_name'] = '到货登记';
                $logisticsLog['remark'] = "创建";
                $logisticsLog['snow_id'] = strval($this->request->getAttribute('snow_id'));
                wlog($logisticsLog['snow_id'],$logisticsLog, true);
            }

            return $this->returnApi(ResponseCode::SUCCESS, '登记成功');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * @RequestMapping(path="/LogisticsInfo/uploadImage", methods="get,post")
     * @param RequestInterface $request
     */
    public function uploadImage(RequestInterface $request){
        $file = $request->file('file');
        if (empty($file)) {
            return ['code' => 0, 'msg' => '图片不能为空'];
        }

        $image_name = $file->getClientFilename();
        $image_path = $file->getPathname();
        //校验图片名称和尺寸
        $image_name_info = explode('.', $image_name);
        $upload_path = 'runtime/upload/logisticsInfo/';
        if (!is_dir($upload_path)) {
            @mkdir($upload_path, 0777, true);
        }
        $save_path = $upload_path . $image_name;
        if ($image_name_info) {
            move_uploaded_file($image_path, $save_path);
            $new_img = imgThumb($save_path);
            if (!$new_img) {
                $error_info = $image_name . '图片格式不符合要求';
                return ['code' => 0, 'msg' => $error_info];
            } else {
                $upload_name = md5( $image_name . mt_rand( 9999, time() ) ). '.' . pathinfo( $new_img, PATHINFO_EXTENSION );;
                $up_res = upload($upload_name, $new_img, 5);
                if (!$up_res) {
                    return $this->returnApi('501', '上传失败');
                }
                return $this->returnApi('200', '上传成功', $up_res);
            }
        }

    }

    /**
     * 批次交接
     * @RequestMapping(path="/logisticsInfo/lists", methods="get,post")
     */
    public function lists(RequestInterface $request)
    {
        $page = intval($request->input('page', 0));
        $limit = intval($request->input('limit', 30));
        $data = $request->input('search',[]);
        $export = $request->input('export',0);
        $wIds = AdminService::organizeWareHouseData($this->getUserId());
        if (!empty($page) || !empty($export)) {
            $data['w_ids'] = $wIds;
            if(!empty($data['w_id'])){
                $data['w_ids'] = [$data['w_id']];
            }
            if(!empty($data['join_time'])){
                $joinTime = explode(" - ",$data['join_time']);
                $data['start_time'] = $joinTime[0];
                $data['end_time'] = $joinTime[1];
            }

            if(!empty($data['expected_time'])){
                $expectedTime = explode(" - ",$data['expected_time']);
                $data['start_expected_time'] = $expectedTime[0];
                $data['end_expected_time'] = $expectedTime[1];
            }

            if(empty($data['rela_type'])){
                $data['rela_type'] =  array_keys($this->batch_rela_type);
            }

            $data['export'] = $export;
            $list = $this->logisticsInfoService->getLogisticsLists($data, $limit, $page);
            if (!empty($list['list'])) {
                foreach ($list['list'] as $key => $item) {
                    $list['list'][$key]['linkman'] = ($item['linkman'] ?? '无')."/".($item['mobile'] ?? '无');
                    $list['list'][$key]['rela_type_name'] = $this->rela_type[$item['rela_type']] ?? '';
                    $list['list'][$key]['send_type_name'] = $this->send_type[$item['send_type']] ?? '';
                    if(empty($item['image_list'])){
                        $list['list'][$key]['image_list'] = '';
                    }
                }
            }

            if(!empty($export)){
                $url = exportToExcel([
                    'id' => 'ID',
                    'serial_no' => '交接批次',
                    'transfer_serial_no' => '中转交接批次',
                    'send_type_name' => '承运方式',
                    'express_name' => '国内承运人',
                    'express_code' => '承运单号',
                    'freight_price' => '承运运费',
                    'send_type_text' => '承运方式',
                    'send_box' => '箱数',
                    'shoes_num' => '鞋类双数',
                    'send_num' => '货品总数',
                    'admin_name' => '交接人',
                    'created_at' => '交接时间',
                    'transit_day' => '在途时效(天)',
                    'expected_time' => '预计送达时间',
                    'car_code' => '车牌照',
                    'linkman' => '司机/联系方',
                    'remark' => '备注',
                ], $list['list'], '批次交接列表');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['list'], ['count' => $list['total_number'], 'limit' => $limit]);
        } else {
            if (!empty( $wIds )) {
                $where = ['ids' => $wIds];
            }
            $data['warehouse_list'] = collect( $this->WarehouseService->getWarehouses( $where ) )->pluck( 'name', 'id' )->toArray();
            $data['rela_type_list'] = $this->batch_rela_type;
            return $this->show('logisticsInfo/lists', $data);
        }
    }

    /**
     * 打印
     * @RequestMapping(path="/logisticsInfo/print",methods="get,post")
     */
    public function print ()
    {
        $idStr = $this->request->input('ids');
        if (empty($idStr)) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '请选择要打印的交接批次！');
        }
        $ids = explode(",", $idStr);
        $printDetails = $this->logisticsInfoService->logisticsPrint($ids);
        if(!empty($printDetails)){
            foreach ($printDetails as $key => $detail) {
                $boxDetails = [];
                $printDetails[$key]['no_shoes_box'] = 0;
                $printDetails[$key]['no_shoes_box_num'] = 0;
                $details = collect($detail['box_details']);
                $infos = $details->groupBy(['type']);
                foreach ($infos as $type => $item) {
                    $boxDetails[$type] = [
                        'source_no' => $item->pluck('source_no')->unique()->map(function ($val) use ($type, $details) {
                            if ($type == 1) {
                                $boxNum = $details->where('source_no', $val)->where('type', $type)->pluck('box_no')->unique()->count();
                            } else {
                                $shoes_num = $details->where('source_no', $val)->where('type', $type)->sum('shoes_num');
                                $boxNum = floor($shoes_num / 5) . "*5+" . ($shoes_num % 5);
                            }
                            return $val . "(" . $boxNum . ")";
                        })->implode(","),
                        'type' => $type,
                        'box_num' => $item->pluck('box_no')->unique()->count(),
                    ];

                    if ($type == 1) {
                        $printDetails[$key]['no_shoes_box'] = $item->pluck('box_no')->unique()->count();
                    }

                    $printDetails[$key]['no_shoes_box_num'] += ($item->sum('goods_num') - $item->sum('shoes_num'));
                }
                $printDetails[$key]['box_details'] = array_values($boxDetails);
            }
        }
        $generator = new BarcodeGeneratorPNG();
        foreach ($printDetails as &$item) {
            $item['serial_no_barcode'] = base64_encode($generator->getBarcode($item['serial_no'], $generator::TYPE_CODE_128));
            $item['expected_time'] = (!empty($item['expected_time']))?date("Y-m-d",strtotime($item['expected_time'])):'';
        }
        return $this->show('logisticsInfo/print', ['print_list'=>$printDetails]);
    }

    /**
     * 查看交接图片
     * @RequestMapping(path="/logisticsInfo/viewImg",methods="get,post")
     */
    public function viewImg ()
    {
        $id = $this->request->input('id');
        if (empty($id)) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '请选择要查看的交接批次！');
        }
        $detail = $this->logisticsInfoService->getLogisticsInfo(['id'=>$id]);
        logger()->info('viewImg',[$detail]);
        return $this->show('logisticsInfo/viewImg', ['image_list'=>json_decode($detail['image_list'],true)]);
    }

    /**
     * 列表导出
     * @RequestMapping(path="/LogisticsInfo/exportList", methods="get,post")
     */
    public function exportList()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $search = $params['search'] ?? [];

            $list = [];
            $page = 1;
            $limit = 2000;
            while (true) {
                $data = $this->logisticsInfoService->getLogisticsList($search, $limit, $page);
                if (!$data['list']) {
                    break;
                }
                $list = array_merge($list, $data['list']);
                unset($data);
                $page++;
            }
            if (!$list) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
            }
            foreach ($list as &$item) {
                $item['send_type_text'] = $this->send_type[$item['send_type']];
            }

            $url = exportToExcel([
                'id' => 'ID',
                'express_code' => '物流单号',
                'car_code' => '车辆牌照',
                'send_box' => '送货箱数',
                'send_num' => '送货件数',
                'linkman' => '联系用户',
                'send_type_text' => '承运方式',
                'recev_box' => '收货箱数',
                'recev_num' => '收货件数',
                'receiver' => '收货用户',
                'refuse_box' => '拒收箱数',
                'refuse_num' => '拒收件数',
                'refuser' => '拒收人',
                'sign_remark' => '备注信息',
                'admin_name' => '创建人',
                'created_at' => '创建时间',
            ], $list, '到货登记记录');

            return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }


    /**
     * 编辑
     * @RequestMapping(path="/LogisticsInfo/update", methods="get,post")
     */
    public function update()
    {
        $id = (int)$this->request->input('id');
        if (!$id) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
        }

        $logistic = $this->logisticsInfoService->getLogisticsInfo(['id' => $id]);
        if (!$logistic) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '数据不存在');
        }
        $logistic['image_list_array'] = [];
        if ($logistic['image_list'] && strpos($logistic['image_list'], '[') === false) {
            $logistic['image_list_array'] = explode(";", $logistic['image_list']);
        }
        $logistic['freight_price'] = fen2yuan($logistic['freight_price']);

        $data['title'] = "到货登记";
        $data['rela_type'] = $logistic['rela_type'];
        $data['rela_type_list'] = $this->rela_type;
        $data['send_type_list'] = $this->send_type;
        $data['data'] = $logistic;

        if ($this->isAjax()) {
            $data = $this->request->all();
            $rule = [
                'rela_no' => ['required', 'string', 'max:30'],
                'rela_type' => ['required', 'integer', 'in:3'],
                'send_type' => ['required', 'integer', 'in:1,3'],
                'express_code' => ['required', 'string', 'max:30'],
                'car_code' => ['required', 'string', 'max:30'],
                'send_box' => ['required', 'integer'],
                'send_num' => ['required', 'integer'],
                'linkman' => ['required', 'string', 'max:20'],
                'mobile' => ['required', 'string', 'max:15'],
                'recev_box' => ['required', 'integer'],
                'recev_num' => ['required', 'integer'],
                'receiver' => ['required', 'string', 'max:20'],
                'refuse_box' => ['integer', 'lte:recev_box'],
                'refuse_num' => ['integer', 'lte:recev_num'],
                'refuser' => ['string', 'max:30'],
                'freight_price' => ['required', 'numeric'],
                'recev_time' => ['required', 'date'],
                'image_list' => ['array']
            ];
            $message = [
                'send_box.integer' => '送货箱数必须为整数',
                'send_num.integer' => '送货件数必须为整数',
                'recev_box.integer' => '收货箱数必须为整数',
                'recev_num.integer' => '收货件数必须为整数',
                'refuse_box.integer' => '拒收箱数必须为整数',
                'refuse_num.integer' => '拒收件数必须为整数',
                'freight_price.numeric' => '运费必须为数字',
                'refuse_box.lte' => '拒收箱数不能大于收货箱数',
                'refuse_num.lte' => '拒收件数不能大于收货件数',
                'recev_time.date' => '收货时间不是合法的日期格式'
            ];
            $errors = $this->validator->make($data, $rule, $message);
            if ($errors->fails()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
            }

            $user_info = $this->session->get('userInfo');

            if ($data['rela_type'] == 3) { // 采购到货登记
                $arrivalOrder = $this->arrivalOrderService->getOrder(['serial_no' => $data['rela_no']]);
                if (!$arrivalOrder) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '任务单不存在');
                }
                if (!in_array($arrivalOrder['status'], [0, 1])) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '任务单状态不符合');
                }

                $logisticsData = [
                    'rela_no' => $data['rela_no'],
                    'rela_type' => $data['rela_type'],
                    'send_type' => $data['send_type'],
                    'express_code' => $data['express_code'],
                    'car_code' => $data['car_code'],
                    'send_box' => $data['send_box'],
                    'send_num' => $data['send_num'],
                    'linkman' => $data['linkman'],
                    'mobile' => $data['mobile'],
                    'recev_num' => $data['recev_num'],
                    'recev_box' => $data['recev_box'],
                    'receiver' => $data['receiver'],
                    'refuse_box' => $data['refuse_box'] ? $data['refuse_box'] : 0,
                    'refuse_num' => $data['refuse_num'] ? $data['refuse_num'] : 0,
                    'refuser' => $data['refuser'],
                    'freight_price' => $data['freight_price'] * 100,
                    'recev_time' => $data['recev_time'],
                    'sign_remark' => $data['sign_remark'],
                    'image_list' => $data['image_list'] ? implode(';' , $data['image_list']) : '',
                    'admin_id' => $user_info['uid'],
                    'admin_name' => $user_info['nickname']
                ];
                $this->logisticsInfoService->updateLogisticsInfo(['id' => $data['id']], $logisticsData);
            }

            return $this->returnApi(ResponseCode::SUCCESS, '编辑成功');
        }

        return $this->show('logisticsInfo/edit', $data);
    }

    /**
     * 绑定预约单
     * @RequestMapping(path="/LogisticsInfo/repair", methods="get,post")
     */
    public function repairArrivalCode(){
        $data = $this->request->all();
        $rule = [
            'id' => ['required', 'integer'],
            'repair_arrival_no' => ['required', 'string', 'max:30'],
        ];

        $message = [
            'id.required' => 'id必填',
            'id.integer' => 'id参数类型不符',
            'repair_arrival_no.required' => '预约单号必填',
            'repair_arrival_no.string' => '预约单号参数类型不符',
        ];
        $errors = $this->validator->make($data, $rule, $message);
        if ($errors->fails()) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
        }

        //检测预约单号是否已登记
        $info = $this->logisticsInfoService->getLogisticsInfo(['rela_no' => $data['repair_arrival_no'], 'neq_cancel' => true]);
        if (!empty($info)){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '此预约单已签收，请确认后再绑定！');
        }
        //检测预约单是否存在
        $arrival = $this->arrivalOrderService->getOrder(['serial_no' => $data['repair_arrival_no']]);
        if (empty($arrival)){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '预约单不存在，请确认后再绑定！');
        }
        if ($arrival['status'] != 0) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '预约单状态不符合');
        }

        //送货箱数
        $send_box = 0;
        if (!empty($arrival['box_nos'])){
            $send_box = count(explode(',', $arrival['box_nos']));
        }
        //获取登记信息
        $logistics = $this->logisticsInfoService->getLogisticsInfo(['id' => $data['id']]);

        $res = $this->logisticsInfoService->updateLogisticsInfo(['id'=> $data['id']], ['rela_no' => $data['repair_arrival_no'], 'expected_time' => $arrival['arrive_time'], 'send_num' => $arrival['arrive_num'], 'send_box' => $send_box, 'updated_at' => currentTime()]);
        if ($logistics['delivery_diff'] == 0){//更新预约单为已到货  无差异时
            $this->arrivalOrderService->updateOrder(['serial_no' => $data['repair_arrival_no']], [
                'real_arrival_time' => $logistics['recev_time'],
                'status' => 1
            ]);

            //生成入库批次
            try {
                $userInfo = $this->getUserInfo();
                $params['admin_id'] = $userInfo['uid'];
                $params['admin_name'] = $userInfo['nickname'];
                $params['serial_no'] = $data['repair_arrival_no'];
                $params['batch_type'] = 1;
                $params['type'] = 1;
                $info = InStoreService::add($params);
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }
            $instoreLog = [];
            $instoreLog['op_id'] = $info['id'];
            $instoreLog['req_router_name'] = '创建任务单';
            $instoreLog['model_name'] = 'InStore';
            $instoreLog['snow_id'] = newSnowId();
            wlog($instoreLog['snow_id'],$instoreLog, true, false);
        }
        //添加log
        $logisticsLog = [];
        $logisticsLog['op_id'] = $data['id'];
        $logisticsLog['req_router_name'] = '补录预约单';
        $logisticsLog['remark'] = "预约单号:".$data['repair_arrival_no'];
        $logisticsLog['snow_id'] = strval($this->request->getAttribute('snow_id'));
        wlog($logisticsLog['snow_id'],$logisticsLog, true);

        if ($res){
            return $this->returnApi(ResponseCode::SUCCESS, '绑定成功');
        }else{
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '绑定失败，请重试！');
        }
    }

    /**
     * 作废到货登记
     * @RequestMapping(path="/LogisticsInfo/cancelLogistics", methods="get,post")
     */
    public function cancelLogistics(){
        $data = $this->request->all();
        $rule = [
            'id' => ['required', 'integer']
        ];

        $message = [
            'id.required' => 'id必填',
            'id.integer' => 'id参数类型不符'
        ];
        $errors = $this->validator->make($data, $rule, $message);
        if ($errors->fails()) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
        }
        //获取登记信息
        $logistics = $this->logisticsInfoService->getLogisticsInfo(['id' => $data['id']]);
        if (empty($logistics)){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '到货登记信息不存在，请确认！');
        }

        $user_info = $this->session->get('userInfo');
        $res = $this->logisticsInfoService->updateLogisticsInfo(['id'=> $data['id']], ['status' => -1, 'updated_at' => currentTime(), 'cancel_at' => currentTime(), 'canceler_id' => $user_info['uid'], 'canceler_name' => $user_info['nickname']]);

        //添加log
        $log = [];
        $log['op_id'] = $data['id'];
        $log['req_router_name'] = '作废到货登记';
        $log['remark'] = "作废";
        $log['snow_id'] = strval($this->request->getAttribute('snow_id'));
        wlog($log['snow_id'],$log, true);

        if ($res){
            return $this->returnApi(ResponseCode::SUCCESS, '作废成功');
        }else{
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '作废失败，请重试！');
        }
    }

    /**
     * 检测是否存在登记差异待审核记录
     * @RequestMapping(path="/LogisticsInfo/checkDiff", methods="get,post")
     */
    public function checkDiff(){
        $data = $this->request->all();
        $rule = [
            'where' => ['required', 'array']
        ];

        $message = [
            'where.required' => '筛选条件不能为空',
            'where.array' => '筛选条件参数类型不符'
        ];
        $errors = $this->validator->make($data, $rule, $message);
        if ($errors->fails()) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
        }

        //获取非差异 & 已审核记录
        $where = $data['where'];
        $where['not_null_rela_no'] = true;
        $where['neq_cancel'] = true;
//        $where['delivery_diff'] = 1;
        $logisticsInfos = $this->logisticsInfoService->getList($where, 0, 0);
        $checkLogisticsInfos = collect($logisticsInfos)->where('diff_verify', '=', 1)->pluck('id')->toArray();
        if (!empty($checkLogisticsInfos)){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR,"只允许对差异待审记录发起差异邮件发送或差异审核");
        }

        //获取差异待审核记录
        $where = $data['where'];
        $where['not_null_rela_no'] = true;
        $where['neq_cancel'] = true;
        $where['delivery_diff'] = 1;
        $logisticsInfos = $this->logisticsInfoService->getList($where, 0, 0);

        $ids = implode(',', array_column($logisticsInfos, 'id'));
        return $this->returnApi(ResponseCode::SUCCESS, '校验通过', $ids);
    }

    /**
     * 发送邮件 查看页面
     * @RequestMapping(path="/LogisticsInfo/email", methods="get,post")
     */
    public function email()
    {
        $idStr = $this->request->input('ids');
        if (empty($idStr)) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '请选择要发送的单据！');
        }

        //获取指定单据
        $where['ids'] = explode(',', $idStr);
        $where['delivery_diff'] = 1;
        $logisticsList = $this->logisticsInfoService->getLogisticsInfoS($where, ['id', 'rela_no as arrival_code', 'send_box', 'recev_box', 'diff_box']);
        if (empty($logisticsList)){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '未查询到单据信息，请确认！');
        }

        //总差异箱数
        $sumDiffbox = array_sum(array_column($logisticsList, 'diff_box'));

        //获取预约单信息
        $arrivalCodes = array_column($logisticsList, 'arrival_code');
        $arrivalList = $this->arrivalOrderService->getOrders(['serial_nos' => $arrivalCodes]);
        if (empty($arrivalList)){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '未查询到预约单信息，请确认！');
        }
        //采购单信息
        $purchaseCodes = array_column($arrivalList, 'purchase_no');
        $purchaseList = $this->purchaseOrderService->getOrders(['serial_nos' => $purchaseCodes]);
        if (empty($purchaseList)){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '未查询到采购单信息，请确认！');
        }
        //获取采购单创建人信息
        $purchaserIds = array_column($purchaseList, 'admin_id');
        $adminList = $this->adminService->getAdminByIds($purchaserIds);
        $emails = implode(';', array_column($adminList, 'email'));
        if (empty($adminList)){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '未查询到用户信息，请确认！');
        }

        return $this->show('/logisticsInfo/email', [
            'data' => $logisticsList,
            'emails' => $emails,
            'arrivalNums' => count($arrivalCodes),
            'arrivalCodes' => implode(',', $arrivalCodes),
            'sumDiffBox' => $sumDiffbox
        ]);
    }

    /**
     * 发送邮件
     * @RequestMapping(path="/LogisticsInfo/sendEmail", methods="get,post")
     */
    public function sendEmail(){
        $data = $this->request->all();
        $rule = [
            'arrival_codes' => ['required', 'string'],
            'remark' => ['string']
        ];

        $message = [
            'arrival_codes.required' => '预约单参数不能为空',
            'arrival_codes.string' => '预约单参数类型不符'
        ];
        $errors = $this->validator->make($data, $rule, $message);
        if ($errors->fails()) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
        }

        $arrivalCodes = explode(',', $data['arrival_codes']);

        //获取到货差异信息
        $lWhere = [
            'rela_nos' => $arrivalCodes,
            'status' => 2,
            'diff_verify' => 0
        ];
        $logisticsList = $this->logisticsInfoService->getLogisticsInfoS($lWhere, ['id', 'rela_no as arrival_code', 'send_box', 'recev_box', 'diff_box']);
        if (empty($logisticsList)){
            throw new BusinessException('未查询到可发送邮件的数据');
        }
        //获取预约单信息
        $arrivalList = $this->arrivalOrderService->getOrders(['serial_nos' => $arrivalCodes]);
        if (empty($arrivalList)){
            throw new BusinessException('未查询到可发送邮件的数据');
        }
        $arrivalPurchaseMap = array_column($arrivalList, 'purchase_no', 'serial_no');

        //采购单信息
        $purchaseCodes = array_column($arrivalList, 'purchase_no');
        $purchaseList = $this->purchaseOrderService->getOrders(['serial_nos' => $purchaseCodes]);
        if (empty($purchaseList)){
            throw new BusinessException('未查询到可发送邮件的数据');
        }
        $purchaseAdminMap = array_column($purchaseList, 'admin_id', 'serial_no');

        //获取采购单创建人信息
        $purchaserIds = array_column($purchaseList, 'admin_id');
        $adminList = $this->adminService->getAdminByIds($purchaserIds);
        if (empty($adminList)){
            throw new BusinessException('未查询到可发送邮件的用户');
        }
        $adminEmailMap = array_column($adminList, 'email', 'id');

        $mailTpl = 'emails/logistics/send';
        //组装邮件数据
        $emailInfos = [];
        foreach ($logisticsList as $lItem){
            $purchaseAdminId = $purchaseAdminMap[$arrivalPurchaseMap[$lItem['arrival_code']]] ?? 0;
            if (empty($purchaseAdminId)){
                continue;
            }
            if (!isset($emailInfos[$purchaseAdminId])){
                $emailInfos[$purchaseAdminId] = [
                    'to_email' => $adminEmailMap[$purchaseAdminId],
                    'arrival_code_num' => 1,
                    'sum_diff_box' => $lItem['diff_box'],
                    'data' => [
                        $lItem
                    ]
                ];
            }else{
                $emailInfos[$purchaseAdminId]['arrival_code_num'] += 1;
                $emailInfos[$purchaseAdminId]['sum_diff_box'] += $lItem['diff_box'];
                $emailInfos[$purchaseAdminId]['data'][] = $lItem;
            }
        }
        $sendNum = 0;
        $errorSend = '';
        foreach ($emailInfos as $eItem){
            $sendParam = [];
            $sendParam['subject'] = "【".$eItem['arrival_code_num']."】个预约单下箱数登记总差异【".$eItem['sum_diff_box']."】，请及时跟进箱数登记";
            $sendParam['to_email'] = $eItem['to_email'];
            $sendParam['tpl'] = $mailTpl;
            $sendParam['data'] = [
                'data' => $eItem['data'],
                'arrival_codes' => implode(',', array_column($eItem['data'], 'arrival_code')),
                'sum_diff_box' => $eItem['sum_diff_box'],
                'remark' => $data['remark'] ?? ''
            ];
            $result = SendService::email($sendParam);
            if (isset($result['success']) && $result['success'] == true) {
                $sendNum ++;
                //将到货登记标记为已发送
                try {
                    $this->logisticsInfoService->updateLogisticsInfos(['ids' => array_column($eItem['data'], 'id')], ['is_send_email' => 1]);
                }catch (\Exception $e) {
                    logger()->info('update logistics info error', [$e->getMessage()]);
                }
            }else{
                $errorArrivalCodes = implode(',', array_column($eItem['data'], 'arrival_code'));
                $errorSend = $errorSend.','.$errorArrivalCodes;
            }
        }
        if(!empty($errorSend)){
            $errorMsg = '发送失败预约单号:'.$errorSend;
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errorMsg);
        }else{
            return $this->returnApi(ResponseCode::SUCCESS, '发送完成');
        }
    }

    /**
     * 差异审核
     * @RequestMapping(path="/LogisticsInfo/verify", methods="get,post")
     */
    public function verify(){
        $data = $this->request->all();
        $rule = [
            'where' => ['required', 'array'],
            'remark' => ['required', 'string'],
            'status' => ['required', 'integer']
        ];

        $message = [
            'where.required' => '筛选条件不能为空',
            'where.array' => '筛选条件参数类型不符',
            'remark.required' => '审核备注不能为空',
            'remark.string' => '审核备注参数类型不符',
            'status.required' => '审核状态不能为空',
            'status.integer' => '审核状态参数类型不符'
        ];
        $errors = $this->validator->make($data, $rule, $message);
        if ($errors->fails()) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
        }

        $where = $data['where'];
        $where['not_null_rela_no'] = true;
        $where['diff_verify'] = 0;
        $where['neq_cancel'] = true;
        $logisticsInfos = $this->logisticsInfoService->getList($where, 0, 0);
        if (empty($logisticsInfos)){
            return $this->returnApi(ResponseCode::VALIDATE_ERROR,"只允许对差异待审记录发起差异邮件发送或差异审核");
        }

        $userInfo = $this->getUserInfo();
        $params = [
            'logisticsInfos' => $logisticsInfos,
            'status' => $data['status'],
            'admin_id' => $userInfo['uid'],
            'admin_name' => $userInfo['nickname'],
            'remark' => $data['remark']
        ];
        try {
            $this->logisticsInfoService->verifyLogistics($params);
        }catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
        return $this->returnApi(ResponseCode::SUCCESS, '审核完成');
    }

    /**
     * 导出明细
     * @RequestMapping(path="/LogisticsInfo/exportDetail", methods="get,post")
     */
    public function exportDetail(RequestInterface $request){
        $userInfo = $this->session->get('userInfo');
        $header = [
            'serial_no' => '登记号',
            'created_at' => '登记时间',
            'admin_name' => '登记人',
            'third_party_no' => '三方发货单号',
            'arrival_no' => '预约单号',
            'is_amends' => '后补预约单',
            'purchase_admin_name' => '负责人',
            'sup_name' => '供应商',
            'w_name' => '仓库',
            'expected_time' => '预计送达时间',
            'first_category' => '一级分类',
            'second_category' => '二级分类',
            'thrid_category' => '三级分类',
            'brand' => '品牌',
            'spu_no' => '货号',
            'barcode' => '条形码',
            'arrival_num' => '预约件数',
            'into_num' => '入库件数',
            'diff_num' => '采购差异件数',
            'remark' => '备注'
        ];

        $where = $request->input('search');
        if (isset($where['warehouse']) && !empty($where['warehouse'])){
            $where['w_ids'] = explode(',', $where['warehouse']);
            unset($where['warehouse']);
        }
        if (isset($where['supplier']) && !empty($where['supplier'])){
            $where['sup_ids'] = explode(',', $where['supplier']);
            unset($where['supplier']);
        }
        //登记时间
        if (isset($where['date']) && !empty($where['date'])) {
            $date_time = explode(' - ', $where['date']);
            $where['start_created_at'] = $date_time[0] . ' 00:00:00';
            $where['end_created_at'] = $date_time[1] . ' 23:59:59';
            unset($where['date']);
        }
        //添加任务
        $data['name'] = '到货登记明细' . '-' . date('YmdHis');
        $data['where'] = json_encode($where, JSON_UNESCAPED_UNICODE);
        $data['is_excel'] = 1;
        $data['system'] = 'wms';
        $data['serial_no'] = $this->SerialNoService->generate(SerialType::WO_LOGISTICS);
        $data['status'] = 1;
        $data['admin_id'] = $userInfo['uid'];
        $data['admin_name'] = $userInfo['nickname'];
        $data['header'] = json_encode(['fields'=>array_keys($header),'names' => array_values($header)], JSON_UNESCAPED_UNICODE);
        $data['type'] = 2;
        $data['service'] = 'logistics_info/exportDetail';
        $data['is_limit'] = 0;

        $task_id = TaskService::addTask($data);
        return $this->returnApi(ResponseCode::SUCCESS, '明细异步下载已提交', ['task_id' => $task_id]);
    }
}