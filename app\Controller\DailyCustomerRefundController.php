<?php
declare(strict_types=1);

namespace App\Controller;

use App\Constants\ResponseCode;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\DailyCustomerRefundServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;

/**
 * @Controller()
 */
class DailyCustomerRefundController extends AbstractController
{
    /**
     * @Inject ()
     * @var ValidatorFactoryInterface
     */
    private $validator;

    /**
     * @Inject ()
     * @var DailyCustomerRefundServiceInterface
     */
    private $dailyCustomerRefundService;
    /**
     * @Inject ()
     * @var WarehouseServiceInterface
     */
    private $warehouseService;
    /**
     * @Inject ()
     * @var AdminServiceInterface
     */
    private $adminService;


    /**
     * 列表
     * @RequestMapping(path="/dailyCustomerRefund/list", methods="get,post")
     */
    public function list()
    {
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->adminService->organizeWareHouseData($userInfo['uid']);
        $warehouse = $this->warehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];

        $statusList = [1 => '已处理', 2 => '未处理（条码销售）', 3 => '未处理（无退货流水）', 4 => '未处理（未转架）'];
        $defaultDate = date('Y-m-d', strtotime('-1 day')) . ' ~ ' . date('Y-m-d', strtotime('-1 day'));

        if ($this->isAjax()) {
            $params = $this->request->all();
            $export = $params['export'] ?? 0;
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            $search['w_ids'] = $userWIds;
            if (!$search['date_range']) {
                $search['date_range'] = $defaultDate;
            }
            $search['start_time'] = explode(' ~ ', $search['date_range'])[0] . ' 00:00:00';
            $search['end_time'] = explode(' ~ ', $search['date_range'])[1] . ' 23:59:59';

            $list = $this->dailyCustomerRefundService->list((int)$export, (int)$page, (int)$pageLimit, $search);
            if ($list['data']) {
                foreach ($list['data'] as &$item) {
                    $item['status_text'] = $statusList[$item['status']];
                }
            }

            if ($export == 1) {
                if (!$list['data']) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
                }
                foreach ($list['data'] as &$item) {
                    $item['barcode'] = $item['barcode'] ? '`' . $item['barcode'] : '';
                }
                $url = exportToExcel([
                    'created_at' => '客退日期',
                    'branch_serial_no' => '订单号',
                    'category_names' => '类目',
                    'brand_name' => '品牌',
                    'barcode' => '条形码',
                    'unique_code' => '店内码',
                    'shop_name' => '仓库',
                    'shelf_code' => '当前货位',
                    'status_text' => '理货处理'
                ], $list['data'], '每日客退数据导出');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
        }

        return $this->show('dailyCustomerRefund/list', [
            'warehouse_list' => $warehouse,
            'status_list' => $statusList,
            'default_date' => $defaultDate,
        ]);
    }
}