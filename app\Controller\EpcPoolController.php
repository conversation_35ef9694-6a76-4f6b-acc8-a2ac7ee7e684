<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Controller;

use App\Constants\ErrorCode;
use App\Constants\ResponseCode;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\EpcPoolServiceInterface;
use App\JsonRpc\ShelfGoodsCodeMapServiceInterface;
use App\Service\HashService;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;

/**
 * @Controller()
 */
class EpcPoolController extends AbstractController
{

    /**
     * @Inject ()
     * @var EpcPoolServiceInterface
     */
    private $EpcPoolService;

    /**
     * @Inject ()
     * @var AdminServiceInterface
     */
    private $AdminService;

    /**
     * @Inject ()
     * @var HashService
     */
    private $hashService;

    /**
     * 新增
     * @RequestMapping(path="/epcPool/add", methods="get,post")
     */
    public function add(RequestInterface $request)
    {
        $params = $request -> all();
        logger() -> debug('add参数：',[$params]);
        if ($request -> isMethod('POST')) {
            $validator = validate()->make($params, [
                'epc_code' => 'required|array',
            ],[
                'epc_code.required'=> 'epc必传',
                'epc_code.array'=> 'epc必须是数组',
            ]);

            if ($validator->fails()) {
                return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
            }

            try {
                $params['remark'] = $params['remark'] ?? '';
                $userInfo = $this -> session -> get('userInfo');
                $adminInfo = [
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                ];
                $this -> EpcPoolService -> add($params,$adminInfo);
                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),'');
            } catch (\Exception $exception) {
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
            }
        }

        return $this -> show("/epcPool/add",[]);
    }

    /**
     * 磁扣池列表
     * @RequestMapping(path="/epcPool/list", methods="get,post")
     */
    public function list(RequestInterface $request)
    {
        $params = $request -> all();
        logger() -> debug('list参数：',[$params]);
        if ($request -> isMethod('POST')) {
            $newParams = [];
            if (isset($params['where']['epc_code']) && !empty($params['where']['epc_code'])) {
                $newParams['where']['epc_code'] = explode(PHP_EOL,$params['where']['epc_code']);
            }

            if (isset($params['where']['status']) && !empty($params['where']['status'])) {
                $newParams['where']['status'] = explode(',',$params['where']['status']);
            }

            if (isset($params['where']['create_admin_id']) && !empty($params['where']['create_admin_id'])) {
                $newParams['where']['create_admin_id'] = explode(',',$params['where']['create_admin_id']);
            }

            $newParams['where']['remark'] = $params['where']['remark'] ?? '';

            $page = $params['page'] ?? [];
            $limit = $params['limit'] ?? [];
            $where = $newParams['where'] ?? [];

            try {
                $ret = $this -> EpcPoolService ->list((int)$page,(int)$limit,$where);
                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),$ret);
            } catch (\Exception $exception) {
                $ret = $exception -> getMessage();
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$ret);
            }
        }

        return $this -> show("/epcPool/list",[]);
    }

    /**
     * 标记状态
     * @RequestMapping(path="/epcPool/markStatus", methods="get,post")
     */
    public function markStatus(RequestInterface $request)
    {
        $params = $request -> all();
        logger() -> debug('add参数：',[$params]);
        if ($request -> isMethod('POST')) {
            $validator = validate()->make($params, [
                'ids' => 'required|array',
                'status' => 'required|integer',
            ],[
                'ids.required'=> '请选择epc',
                'ids.array'=> 'epc参数必须是数组',
                'status.required'=> '请选择标记状态',
                'status.integer'=> '状态必须是正整数',
            ]);

            if ($validator->fails()) {
                return $this->returnApi( ErrorCode::REQUEST_ERROR, $validator->errors()->first() );
            }

            $userInfo = $this -> session -> get('userInfo');
            $adminInfo = [
                'admin_id' => $userInfo['uid'],
                'admin_name' => $userInfo['nickname'],
            ];

            try {
                $this -> EpcPoolService -> markStatus($params['ids'],(int)$params['status'],$adminInfo);
                return $this -> returnApi(ResponseCode::SUCCESS,ResponseCode::getMessage(ResponseCode::SUCCESS),'');
            } catch (\Exception $exception) {
                return $this -> returnApi(ErrorCode::SERVER_ERROR,$exception -> getMessage());
            }
        }

        return $this -> show("/epcPool/add",[]);
    }

}
