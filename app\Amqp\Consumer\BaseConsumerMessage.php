<?php


namespace App\Amqp\Consumer;


use Hyperf\Amqp\Message\ConsumerMessage;
use Hyperf\Amqp\Packer\Packer;
use Hyperf\Amqp\Result;
use Hyperf\Utils\ApplicationContext;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Wire\AMQPTable;

class BaseConsumerMessage extends ConsumerMessage
{
    /**
     * 获取消息重试次数
     *
     * @param AMQPMessage $msg
     *
     * @return int
     */
    protected function getRetryCount(AMQPMessage $msg): int
    {
        $retry = 0;
        if ($msg->has('application_headers')) {
            $headers = $msg->get('application_headers')->getNativeData();
            if (isset($headers['x-death'][0]['count'])) {
                $retry = $headers['x-death'][0]['count'];
            }
        }

        return (int)$retry;
    }

    /**
     * 将消息发送至重试队列
     * @param AMQPMessage $msg
     */

    /**
     * 将消息发送至重试队列
     * @param AMQPMessage $msg
     * @param int $maxRetryCount 最大重试次数
     * @return string
     */
    protected function retry(AMQPMessage $msg,$maxRetryCount = 3)
    {
        if($this->getRetryCount($msg) >= 3){
            $this->failed($msg);
        }
        /** @var AMQPChannel $channel */
        $channel = $msg->delivery_info['channel'];
//        $channel->basic_publish(
//            new AMQPMessage($packer->pack($msg->getBody()), [
//                'correlation_id' => $msg->get('correlation_id'),
//            ]),
//            '',
//            $msg->get('reply_to')
//        );
        /** @var AMQPTable $headers */
        if ($msg->has('application_headers')) {
            $headers = $msg->get('application_headers');
        } else {
            $headers = new AMQPTable();
        }

        $headers->set('x-orig-routing-key', $this->getOrigRoutingKey($msg));

        $properties = $msg->get_properties();
        $properties['application_headers'] = $headers;
        $newMsg = new AMQPMessage($msg->getBody(), $properties);


        $channel->basic_publish(
            $newMsg,
            $this->exchangeRetryTopic($this->getExchange()),
            $this->getQueue()
        );
        return Result::ACK;
    }

    /**
     * 将消息发送至错误队列
     * @param AMQPMessage $msg
     */
    protected function failed(AMQPMessage $msg){
        /** @var AMQPChannel $channel */
        $channel = $msg->delivery_info['channel'];
        logger()->info('send failed msg',[$this->exchangeFailedTopic($this->getExchange()), $msg->getRoutingKey()]);
        $channel->basic_publish(
            $msg,
            $this->exchangeFailedTopic($this->getExchange()),
            $this->getQueue()
        );
        return Result::ACK;
    }

    private function getOrigRoutingKey(AMQPMessage $msg)
    {
        return (function (AMQPMessage $msg) {
                $retry = null;
                if ($msg->has('application_headers')) {
                    $headers = $msg->get('application_headers')->getNativeData();
                    if (isset($headers['x-orig-routing-key'])) {
                        $retry = $headers['x-orig-routing-key'];
                    }
                }
                return $retry;
            })($msg) ?? $msg->get('routing_key');
    }

    /**
     * 重试交换机Topic
     *
     * @return string
     */
    protected function exchangeRetryTopic($exchange): string
    {
        return $exchange . '.retry';
    }

    /**
     * 失败交换机Topic
     *
     * @return string
     */
    protected function exchangeFailedTopic($exchange): string
    {
        return $exchange . '.failed';
    }


    private function getFailedQueueName(string $queueName): string
    {
        return "{$queueName}@failed";
    }

    private function getRetryQueueName(string $queueName): string
    {
        return "{$queueName}@retry";
    }
}