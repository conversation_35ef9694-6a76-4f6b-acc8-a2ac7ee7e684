<?php
declare(strict_types=1);

namespace App\Controller;

use Hyperf\Contract\OnCloseInterface;
use Hyperf\Contract\OnMessageInterface;
use Hyperf\Contract\OnOpenInterface;
use Swoole\Http\Request;
use Swoole\Server;
use Swoole\Websocket\Frame;
use Swoole\WebSocket\Server as WebSocketServer;

class WebSocketController implements OnMessageInterface, OnOpenInterface, OnCloseInterface
{
    private $redis;
    private $wsFdKey;

    public function __construct()
    {
        $this -> redis = redis();
        $this -> wsFdKey = "websocket_fd_list";
    }

    public function onMessage($server, Frame $frame): void
    {
        //心跳刷新缓存
        //获取所有的客户端id
        $fdList = $this -> redis->sMembers($this -> wsFdKey);
        //如果当前客户端在客户端集合中,就刷新
        if (in_array($frame->fd,$fdList)) {
            $this -> redis->sAdd($this -> wsFdKey,$frame->fd);
            $this -> redis->expire($this -> wsFdKey,7200);
        }
        $server->push($frame->fd,json_encode(['code' => 0,'msg' => $frame->data],1));

        //$server->push($frame->fd, 'Recv: ' . $frame->data);
    }

    public function onClose($server, int $fd, int $reactorId): void
    {
        //删掉客户端id
        //移除集合中指定的value
        $this -> redis->sRem($this -> wsFdKey,$fd);
        var_dump('closed');
    }

    public function onOpen($server, Request $request): void
    {
        //保存客户端id
        $res1 = $this -> redis->sAdd($this -> wsFdKey,$request->fd);
        var_dump($res1);

        $res = $this -> redis->expire($this -> wsFdKey,7200);
        var_dump($res);

        $server->push($request->fd,'Opened');
        //$server->push($request->fd, 'Opened');
    }
}