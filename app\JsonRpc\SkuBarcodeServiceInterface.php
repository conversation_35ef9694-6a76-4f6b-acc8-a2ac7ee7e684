<?php

namespace App\JsonRpc;

interface SkuBarcodeServiceInterface
{
    /**
     * 校验条码是否存在
     * @param array $data
     */
    public function checkBarcodes(array $data, $isTempFiltered = true);

    /**
     * 获取sku_id
     * @param string $barcode
     * @return \Hyperf\Utils\HigherOrderTapProxy|mixed|void|null
     */
    public function getSkuByBarcode(string $barcode);

    /**
     * 返回sku的主条码
     * @param array $sku_ids
     * @return array
     */
    public function getSkuMainBarcode(array $sku_ids);
}