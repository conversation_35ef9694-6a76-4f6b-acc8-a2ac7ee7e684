<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Controller;

use App\Constants\CachePre;
use App\Constants\ResponseCode;
use App\Exception\BusinessException;
use App\JsonRpc\TemplateSetServiceInterface;
use App\Library\Facades\AdminService;
use App\Library\Facades\AllotDiffService;
use App\Library\Facades\AllotService;
use App\Library\Facades\ConfigService;
use App\Library\Facades\HashService;
use App\Library\Facades\InStoreService;
use App\Library\Facades\LogisticsInfoService;
use App\Library\Facades\LogisticsService;
use App\Library\Facades\OutStoreService;
use App\Library\Facades\SyncTaskService;
use App\Library\Facades\TemplateSetService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\Validation\Rule;

/**
 * @Controller
 */
class TaskController extends AbstractController
{


    //状态 0=待出库，1=出库中，2=已完成，-1=作废
    //状态 1=待审核，2=待接单，3=驳回，4=待出库，5=待发货，6=待签收，7=待入库，8=已完成，9=作废
    private $status = [
        '2' => '待接单',
        '4' => '待出库',
//        '5' => '待发货',
        '6' => '待签收',
        '7' => '待入库',
        '8' => '已完成',
        '9' => '作废',
    ];

    private $search_type = [
        '1' => '店内码查找',
        '2' => '货号查找',
        '3' => '条形码查询',
        '4' => 'SPU查询',
        '5' => 'SKU查询',
    ];

    private $out_type = [
        '1' => '店内码级',
        '2' => '条形码级'
    ];

    private $searchTypeMap = [
        '1' => 'unique_code',
        '2' => 'spu_no',
        '3' => 'barcode',
        '4' => 'spu_id',
        '5' => 'sku_id',
    ];
    //单据类型【21差异调整单-出，22退返单，23调拨-出，24盘点-亏，25销售(订单)，26配送丢失，27配送报损】
    private $taskTypeMap = [
        '25' => '销售出库',
        '23' => '调拨出库',
        '28' => '拍照出库',
        '27' => '报损出库',
        '22' => '退返单出库',
    ];

    /**
     * @RequestMapping(path="/task/list", methods="get,post")
     * @return mixed
     */
    public function list(RequestInterface $request)
    {
        $data['title'] = '异步列表';

        if ($this->isAjax()) {
            $where = $this->validate($request->all(),'search');
            logger()->debug('list_where:start',[$where]);
            $page = intval($request->input('page',1));
            $limit = intval($request->input('limit',30));

            if (!empty($where['create_date_range'])){
                $dateRangeArray = explode(' - ',$where['create_date_range']);
                $where['start_time'] = $dateRangeArray[0];
                $where['end_time'] = date('Y-m-d 23:59:59',strtotime($dateRangeArray[1]));
            }

            if (!empty($where['admin_id'])){
                $where['admin_id'] = explode(',',$where['admin_id']);
            }

            unset($where['create_date_range']);
            logger()->debug('list_where:end',$where);
            $list = SyncTaskService::list( $where,$page, $limit,['id','ref_no','task_name','service_name','status','admin_id','admin_name','try_count','start_time','finish_time','sys_type','created_at','updated_at']);
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $limit]);
        }
        $userInfo = $this->getUserInfo();

        $adminList = AdminService::organizeUsers($userInfo['uid']);
        $data = [
            'admin_list' => $adminList,
            'status_list' => ConfigService::getConfigByKey( 'sync_task_status' ),
        ];

        return $this->show('task/list', $data);
    }

    /**
     * @RequestMapping(path="/task/reTry", methods="post")
     * @return mixed
     */
    public function reTry(RequestInterface $request)
    {
        $taskId = intval($request->input('task_id',1));
        if (empty($taskId)){
            throw new BusinessException('任务id不能为空！');
        }
        SyncTaskService::reTry($taskId);
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
    }

    /**
     * @RequestMapping(path="/task/downResult", methods="post")
     * @return mixed
     */
    public function downResult(RequestInterface $request)
    {
        $taskId = intval($request->input('task_id',1));
        if (empty($taskId)){
            throw new BusinessException('任务id不能为空！');
        }
        $info = SyncTaskService::info($taskId);
        if (empty($info)){
            throw new BusinessException('无法获取任务信息！');
        }
        if (in_array($info['service_name'], ['imperfect:exportList', 'price:exportShopPrice'])){
            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', ['url' => str_replace('\\','', trim($info['result'], '"'))]);
        }else{
            return exportTxt(str_replace('\n',"\r\n",trim($info['result'],'"')),  urlencode($info['task_name']).'.txt');
        }
    }

    /**
     * 数据验证
     * @param array $data
     * @return array
     */
    protected function validate(array $data,$secne='default'){

        $message = [
            'admin_id.string' => '操作人参数有误!',
            'create_date_range.string' => '时间格式有误!',
            'task_name.string' => '查询值有误!',
            'status.numeric' => '状态参数有误!',
        ];
        $rules = [
            'status' => 'numeric',
            'create_date_range' => 'string',
            'task_name' => 'string',
            'admin_id' => 'string',
        ];
        /**
         * 根据调拨单号获取差异详请
         * @param string $params [
         *   "handle_num" => 处理数量
         *   "diff_reason" => 差异原因
         *   "liabler" => 归属方
         *   "detail_id" => 差异明细id
         * ]
         * @return array
         */
        $secnes = [
            'search' => ['task_name','admin_id','status','create_date_range']
        ];
        $useRule = [];
        if(isset($secnes[$secne])){
            foreach ($secnes[$secne] as $item){
                $useRule[$item] = $rules[$item];
            }
        }else{
            throw new BusinessException('验证场景值有误');
        }

        $validator = validate()->make(
            $data,
            $useRule,
            $message
        );

        return $validator->validate(); //验证数据有效性
    }

}