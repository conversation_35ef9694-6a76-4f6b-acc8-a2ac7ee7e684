<?php


namespace App\Middleware;

use App\Exception\AuthenticationException;
use App\Library\Facades\AdminService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Snowflake\IdGeneratorInterface;
use Hyperf\Utils\Context;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Response;


class Auth implements MiddlewareInterface
{
    /**
     * @Inject
     * @var RequestInterface
     */
    protected $req;

    /**
     * @Inject()
     * @var Response
     */
    protected $response;

    /**
     * @Inject()
     * @var \Hyperf\Contract\SessionInterface
     */
    protected $session;
    /**
     * @Inject
     * @var ContainerInterface
     */
    protected $container;


    /**
     * @param \Psr\Http\Message\ServerRequestInterface $request
     * @param \Psr\Http\Server\RequestHandlerInterface $handler
     * @return \Psr\Http\Message\ResponseInterface
     */
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $router = $request->getUri()->getPath();
        $jumpLogin = [
            '/login',
            '/verify',
            '/favicon.ico',
            '/pickTask/info',//分拣任务外部接口
            '/pickTask/out'//分拣接口
        ];
        logger()->info('request process',['path'=>$router,'cookie'=>$this->req->cookie('cookie_x-rbac-token')]);
        if (!in_array( $router, $jumpLogin )) {
            $token = $this->req->cookie('cookie_x-rbac-token');
            $userInfo = $this->session->get('userInfo');
            logger()->info('request check',[$userInfo]);
            if (empty($userInfo)) {
                if (!is_null($token)){
                    $this->session->set('token', $token);
                    $splitToken = explode('#',$token);
                    $token = $splitToken[2] ?? '';
                    try{
                        !empty($token) && $userInfo = AdminService::tokenGetUserInfo($token);
                        if (!empty($userInfo)){
                            $this->session->set('userInfo', $userInfo);
                        }
                        logger()->info('request AdminService',[$userInfo]);
                    }catch (\Throwable $e){
                        logger()->info('token无法获取用户信息',['token'=>$token,'e'=>$e->getMessage()]);
                    }
                }
            }
//            else{
//                $snow_id = $this->container->get(IdGeneratorInterface::class)->generate();
//                $request = $request->withAttribute('snow_id',$snow_id);
//                Context::set(RequestInterface::class, $request);
////                $admin_info = $this->session->get('userInfo');
//                $post = $this->req->all();
//                $post['snow_id'] = $snow_id;
//                $post['interface']=$router;
//                redis()->setex('req_'.$snow_id,30*60,json_encode(array_merge($post,['admin_id'=>$userInfo['uid'] ?? 0,'admin_name'=>$userInfo['nickname'],'req_time'=>date('Y-m-d H:i:s')])));
//            }
            if (empty($userInfo)){
                if(strtolower($request->getMethod()) == 'get'){
                    return $this->response->redirect(getLoginUrl($request));
                }else{
                    throw new AuthenticationException();
                }
            }

            $snow_id = $this->container->get(IdGeneratorInterface::class)->generate();
            $request = $request->withAttribute('snow_id',$snow_id);
            Context::set(RequestInterface::class, $request);
//                $admin_info = $this->session->get('userInfo');
            $post = $this->req->all();
            $post['snow_id'] = $snow_id;
            $post['interface']=$router;
            redis()->setex('req_'.$snow_id,30*60,json_encode(array_merge($post,['admin_id'=>$userInfo['uid'] ?? 0,'admin_name'=>$userInfo['nickname'],'req_time'=>date('Y-m-d H:i:s')])));

        }

        return $handler->handle($request);
    }
}