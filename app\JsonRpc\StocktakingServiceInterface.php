<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * erp盘点服务消费者
 */
Interface  StocktakingServiceInterface
{
    /**
     * 添加盘点任务
     * @param array $data
     * @return mixed
     */
    public function addStocktakingTask(array $data);

    /**
     * 盘点任务单详情
     * @param int $id
     */
    public function getStocktakingTaskOne(int $id);

    /**
     * 盘点任务单列表
     * @param int $page
     * @param int $limit
     * @param array $where
     * @param array $field
     */
    public function getStocktakingTaskList(int $page = 1, int $limit = 10, array $where = [], array $field = []);

    /**
     * 根据盘点任务单ID获取盘盘点清单列表
     * @param int $stId 盘点任务id
     * @param int $page
     * @param int $limit
     * @param array $where
     * @param array $field
     */
    public function getStocktakingBillList(int $stId, int $page = 1, int $limit = 10, array $where = [], array $field = []);

    /**
     * 根据盘点任务id获取盘点清单统计信息
     * @param int $stId 盘点任务id
     */
    public function getStocktakingBillStatistics(int $stId = 0);

    /**
     * 更新状态
     * @param int $id 盘点任务id
     * @param array $data
     */
    public function updateStatus(int $id, array $data);

    /**
     * 批量导入盘点数据
     * @param array $id 盘点任务id
     * @param array $importType 导入类型 1=商品维度（条码），2=唯一马维度，3=epc码维度
     * @param array $data
     */
    public function importBill(int $id, int $importType, array $data);

    /**
     * 获取所有盈亏数据
     * @param int $stId 盘点任务id
     */
    public function getPlData(int $stId);

}