<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * Interface ArrivalOrderServiceInterface
 * @package App\JsonRpc
 */
interface ArrivalOrderServiceInterface
{
    // 所有的状态列表 例如[-1=>作废]
    public function getOrderStatusList();
    // 查单个 [serial_no=>]
    public function getOrder(array $where, array $filed = ['*']);
    // 查多个
    public function getOrders(array $where = [], array $filed = ['*']);
    // 是否存在
    public function checkExist(array $where);
    // 创建[order=>主表, detail=>附表]
    public function createOrder(array $data);
    // 单明细
    public function getOrderDetail(array $where);
    // 可流转的状态
    public function getAllowStatusList(int $status);
    // 更新
    public function updateOrder(array $where, array $data);
    // 列表分页 $export=0 /导出时 $export=1
    public function orderList(int $export, int $page, int $pageLimit, array $search);
    /**
     * 获取到货任务列表
     * @param array $params
     * @param int $page
     * @param int $pageLimit
     */
    public function arrivalList(array $params=[], int $page, int $pageLimit);
}
