<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * 品牌服务
 */
interface BoxQualityServiceInterface
{
    /**
     * 获取箱号质检信息
     * @param array $where
     */
    public function getInfo(array $where);

    /**
     * 获取质检详情信息
     * @param array $where
     */
    public function getDetailS(array $where);

    /**
     * 质检/质检明细列表
     * @param int $export
     * @param int $page
     * @param int $pageSize
     * @param array $search
     * @return array
     */
    public function qualityList(int $export = 0, int $page = 1, int $pageSize = 10, array $search);

    /**
     * 更新
     * @param array $where
     * @param array $data
     * @return int
     */
    public function update(array $where, array $data);

    public function getDetailsNum(array $where);
}