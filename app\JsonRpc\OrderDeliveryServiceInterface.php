<?php

namespace App\JsonRpc;

interface OrderDeliveryServiceInterface
{
    /**
     * 获取快递交接列表
     * @param array $where
     */
    public function deliveryList(array $where, int $perPage = 10, int $currentPage = 1);

    /**
     * 快递发货交接详情
     * @param array $where
     * @return mixed
     */
    public function deliveryOne(array $where);

    /**
     * 快递交接明细
     * @param array $params
     */
    public function deliveryDetailList(array $where, int $perPage = 10, int $currentPage = 1);

    /**
     * 添加快递交接信息
     * @param array $params
     */
    public function addDelivery(array $params);

    /**
     * 更新快递发货交接单信息
     * @param array $where
     * @param array $data
     */
    public function updateDelivery(array $where, array $data);

    /**
     * 快递交接导出
     * @param array $where
     * @return mixed
     */
    public function orderLogisticsExports(array $where);
}