<?php
declare(strict_types=1);

namespace App\JsonRpc;

Interface ImperfectServiceInterface
{
    /**
     * 残次分类 - 查多个
     * @param array $where
     * @param array|string[] $filed
     * @return array
     */
    public function getCategoryS(array $where = [], array $filed = ['*']);

    /**
     * 标残
     * @param array $data
     * @return array
     */
    public function batchCreate(array $data);

    /**
     * 列表
     * @param int $export
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return array
     */
    public function list(int $export, int $page, int $pageLimit, array $search);

    /**
     * 明细详情
     * @param int $id
     * @return array
     */
    public function getImperfectDetail(int $id);

    /**
     * 残次明细表
     * @param int $imperfect_id
     * @return array
     */
    public function getDetailById(int $imperfect_id);

    /**
     * 残次分类
     * 根据子分类循环获取其父级分类
     * @param int $cId
     * @param array $data
     * @return string
     */
    public function categoryList(int $cId, array &$data = []);

    /**
     * 查单个
     * @param array $where
     * @param array|string[] $filed
     */
    public function getImperfect(array $where, array $filed = ['*']);

    /**
     * 审核（除到货原残、调拨在途后残 其他的走审核）
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function verify(int $id, array $data);

    /**
     * 查多个
     * @param array $where
     * @param array|string[] $filed
     * @return array
     */
    public function getImperfectS(array $where, array $filed = ['*']);

    /**
     * 取消标残
     * @param array $uniqueCodes
     * @param array $userWIds
     * @return array
     */
    public function cancel(array $uniqueCodes, array $userWIds);

    /**
     * 条码标残 - 匹配批次（先入先出）
     * @param int $wId
     * @param array $barcodes
     * @return array
     */
    public function matchCanImperInStockNo(int $wId, array $barcodes);

    /**
     * 条码标残 - 匹配货架号
     * @param int $wId
     * @param array $barcodes
     * @return array
     */
    public function matchCanImperShelf(int $wId, array $barcodes);

    /**
     * 条码标残 - 校验匹配的批次库存
     * @param int $wId
     * @param array $data
     * @param bool $isVerify
     * @return array
     */
    public function checkMatchInStockNo(int $wId, array $data, bool $isVerify = false);

    /**
     * 条码标残 - 校验匹配的货架库存
     * @param int $wId
     * @param array $data
     * @param bool $isVerify
     * @return array
     */
    public function checkMatchShelf(int $wId, array $data, bool $isVerify = false);

    /**
     * 批量审核
     * @param array $ids
     * @param array $data
     * @return mixed
     */
    public function batchVerify(array $ids, array $data);
}