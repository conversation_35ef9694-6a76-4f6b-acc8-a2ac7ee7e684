<?php
declare(strict_types=1);

namespace App\Controller;

use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\JsonRpc\AchievementServiceInterface;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use Cassandra\Date;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;

/**
 * @Controller()
 */
class AchievementController extends AbstractController
{
    /**
     * @Inject()
     * @var AchievementServiceInterface
     */
    private $AchievementService;

    /**
     * @Inject()
     * @var AdminServiceInterface
     */
    private $AdminService;

    /**
     * @Inject()
     * @var WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * 列表
     * @RequestMapping(path="/achievement/list", methods="get,post")
     */
    public function list()
    {
        // 用户
        $userInfo = $this->session->get('userInfo');
        $userWIds = $this->AdminService->organizeWareHouseData($userInfo['uid']);
        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        // 默认查询日期
        $default_date = date('Y-m-d').' ~ '.date('Y-m-d');

        if ($this->isAjax()) {
            $params = $this->request->all();
            if (!$params['search']['w_id']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '请选择仓库');
            }
            if (!$params['search']['achieve_type']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '请选择统计类型');
            }
            if (!$params['search']['date_range']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '请选择日期');
            }

            // 仓库id 获取所有员工
            $adminList = $this->AdminService->getAdminByRelaId(2, (int)$params['search']['w_id']);
            if (!$adminList) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '此仓库下没有员工');
            }

            $search = [
                'admin_list' => $adminList,
                'achieve_type' => $params['search']['achieve_type'],
                'start_time' => explode(' ~ ', $params['search']['date_range'])[0] . ' 00:00:00',
                'end_time' => explode(' ~ ', $params['search']['date_range'])[1] . ' 23:59:59'
            ];
            $list = $this->AchievementService->list($search);
            if (!$list) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据');
            }

            // 导出
            if ($params['export'] == 1) {
                $url = exportToExcel([
                    'admin_id' => '员工号',
                    'admin_name' => '员工名',
                    'unique_num' => '店内码数量',
                    'barcode_num' => '条码数量'
                ], $list, '绩效记录');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list);
        }

        return $this->show('achievement/list', [
            'warehouse_list' => $warehouse,
            'achieve_type' => PublicCode::achievement_type,
            'default_date' => $default_date
        ]);
    }
}
