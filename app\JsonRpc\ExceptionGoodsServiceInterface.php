<?php
declare(strict_types=1);

namespace App\JsonRpc;

/**
 * Interface ExceptionGoodsServiceInterface
 * @package App\JsonRpc
 */
interface ExceptionGoodsServiceInterface
{
    /**
     * 新增
     * @param array $params
     */
    public function add(array $params);

    /**
     * 列表
     * @param array $where
     * @param int $page
     * @param int $limit
     */
    public function list(array $where=[],$page=1,$limit=10);

    /**
     * 详情
     */
    public function detail($id,$params=[]);

    /**
     * 买手更新异常货品信息
     * @param $id
     * @param $data
     * @return void
     */
    public function updateByBuyer($id, $data);

    /**
     * 仓店更新异常货品信息
     * @param $id
     * @param $data
     * @return void
     */
    public function updateByShop($id, $data);

    /**
     * 更新状态
     * @param $id
     * @param $params
     */
    public function updateStatus($id, $params);

    /**
     * 微残驳回
     * @param $id
     * @return void
     */
    public function updateByImperfect($id);

    /**
     * 校验店内码
     * @param $params
     */
    public function checkUniqueInfo($params);

    /**
     * 获取导出数据列表
     * @param $params
     */
    public function getAllFieldList($params,$page=1,$limit=10,$isCount=true);

    /**
     * 获取供应商列表
     * @param $id
     * @return array
     */
    public function getSuppliers($id);

    /**
     * 获取供应商合同效期内剩余货损金额
     * @param $sup_id
     * @return int|string
     */
    public function getContractLossAmount($sup_id);

    /**
     * 获取异常货品的供应商列表
     * @param $sup_id
     * @return int|string
     */
    public function getExceptionSuppliers();
}