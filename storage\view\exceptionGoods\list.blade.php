<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>{{$title}}</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="/static/layui/css/layui.css" rel="stylesheet"/>
    <link href="/static/css/pearCommon.css" rel="stylesheet"/>
    <link href="/static/css/myui.css" rel="stylesheet"/>
    <link rel="stylesheet" href="/static/css/myui.css"/>
    <script src="/static/layui/layui.js" charset="utf-8"></script>
    <style>
        .layui-form-select {
            width: 40%;
        }
        .layui-textarea-diy {
            /* min-height: 100px; */
            height: 36px;
            line-height: 36px;
            /* padding: 6px 10px; */
            resize: vertical;
        }
        .btn i.layui-icon{
            color: #fff;
        }

        .btn.disabled i.layui-icon {
            /*background: gray;*/
        }
        .gray {
            background-color:  #c1c1c1;
            color: #fff;
        }
    </style>
</head>

<body class="pear-container">
<div class="layui-card">
    <hr class="layui-bg-gray">

    {{--筛选--}}
    <form class="layui-form layui-form-pane p-16" lay-filter='search_form' >
        <div class="layui-btn-group demoTable">
            <button class="layui-btn" data-type="getCheckData" id="add">新增异常货品</button>
        </div>
        <hr class="layui-bg-gray">
        <div class="layui-row mt-7">
            <div class="layui-col-xs4">
                <label class="layui-form-label">仓店</label>
                <div class="layui-input-inline" style="width: 60%">
                    <div id="w_id" style="display: inline-block;width: 100%"></div>
                </div>
            </div>

            <div class="layui-col-xs4">
                <label class="layui-form-label">品牌</label>
                <div class="layui-input-inline" style="width: 60%">
                    <div id="brand_id" style="display: inline-block;width: 100%"></div>
                </div>
            </div>

            <div class="layui-col-xs4">
                <label class="layui-form-label">商品筛选</label>
                <div class="row">
                    <select class="layui-select-width" name="code_type" id="type"  lay-search>
                        <option value="">请选择</option>
                        <option value="1">店内码</option>
                        <option value="2">条码</option>
                        <option value="3">货号</option>
                    </select>
                    <textarea placeholder="  一行一个" name="goods_code" class=" layui-textarea-diy" style="width: 43%;"></textarea>
                </div>
            </div>
        </div>

        <div class="layui-row mt-7">
            <div class="layui-col-xs4">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-inline" style="width: 60%">
                    <div id="status" style="display: inline-block;width: 100%"></div>
                </div>
            </div>
            <div class="layui-col-xs4">
                <label class="layui-form-label">提交人</label>
                <div class="layui-input-inline" style="width: 60%">
                    <div id="admin_id" style="display: inline-block;width: 100%"></div>
                </div>
            </div>
            <div class="layui-col-xs4">
                <label class="layui-form-label">跟进买手</label>
                <div class="layui-input-inline" style="width: 60%">
                    <div id="buyer_admin_id" style="display: inline-block;width: 100%"></div>
                </div>
            </div>
        </div>

        <div class="layui-row mt-7">
            <div class="layui-col-xs4">
                <label class="layui-form-label">异常类型</label>
                <div class="layui-input-inline" style="width: 60%">
                    <div id="exception_type" style="display: inline-block;width: 100%"></div>
                </div>
            </div>
            <div class="layui-col-xs4">
                <label class="layui-form-label">货品去向</label>
                <div class="layui-input-inline" style="width: 60%">
                    <div id="destination" style="display: inline-block;width: 100%"></div>
                </div>
            </div>
            <div class="layui-col-xs4">
                <label class="layui-form-label">ID</label>
                <div class="layui-input-inline" style="width: 60%">
                    <textarea placeholder="  一行一个" name="id" class=" layui-textarea-diy" style="width: 100%;border: 1px solid #d3d3d3"></textarea>
                </div>
            </div>
        </div>
        <div class="layui-row mt-7">
            <div class="layui-col-xs4">
                <label class="layui-form-label">供应商</label>
                <div class="layui-input-inline" style="width: 60%">
                    <div id="sup_id" style="display: inline-block;width: 100%"></div>
                </div>
            </div>
        </div>


        <div class="layui-form-item">
            <div  style="display: flex;justify-content: flex-end;">
                <button class="layui-btn-sm layui-btn layui-btn-normal" lay-submit lay-filter="query">
                    <i class="layui-icon layui-icon-search"></i>
                    查询
                </button>
                <button id="newExportBtn" class="btn layui-btn-sm layui-btn layui-btn-normal" lay-submit lay-filter="export">
                    <i class="layui-icon layui-icon-export"></i>
                    导出
                </button>
            </div>
        </div>
    </form>

    {{--表格--}}
    <div class="layui-card-body">
        <table class="layui-hide" id="list" lay-filter="tableBar"></table>
    </div>

</div>
<script type="text/html" id="opt">
    <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="edit">查看日志</button>
</script>
<script>
    layui.config({
        base: '/static/js/' //自定义模块
    }).use(['form','dropdown','element','layer', 'table' ,'laydate','xmSelect','iframeTools'], function () {
        // Tab的切换功能，切换事件监听等，需要依赖element模块
        var $ = layui.jquery
        var table = layui.table
        var form = layui.form
        var layer = layui.layer
        var laydate = layui.laydate
        var dropdown = layui.dropdown
        var iframeTools = layui.iframeTools
        var comm = {}

        var wId = $('input[name=w_id]').val()
        var is_init = $('input[name=is_init]').val()
        var shelfCode = $('input[name=shelf_code]').val()
        var inStockNo = $('input[name=in_stock_no]').val()
        var skuId = $('input[name=skuid]').val()
        var xmSelect = layui.xmSelect; // 下拉多选

        var state = {
            export_page_data:[],
            formData: {},
            start_time: '',
            end_time: '',
            suppliers:[],
            loading: null
        }

        $.ajax({
            type: 'get',
            url: "/common/getWarehouses",
            async: false,
            success: function (res) {
                if (res.code==200) {
                    comm.warehouses = res.data.warehouses
                } else {
                    layer.msg(res.msg,{icon:2,time:1500})
                }
            }
        })

        $.ajax({
            type: 'get',
            url: "/common/getBrands",
            async: false,
            success: function (res) {
                if (res.code==200) {
                    comm.brands = res.data
                } else {
                    layer.msg(res.msg,{icon:2,time:1500})
                }
            }
        })

        $.ajax({
            type: 'get',
            url: "/common/adminList",
            async: false,
            success: function (res) {
                if (res.code==200) {
                    comm.adminList = res.data
                } else {
                    layer.msg(res.msg,{icon:2,time:1500})
                }
            }
        })

        // 新增异常货品
        $("#add").click(function () {
            console.log("新增异常货品==")
            iframeTools.editTab('新增异常货品',"/exceptionGoods/add")
            return false
        })

        // 列表初始化加载
        getList('#list', {status:1})

        form.on('submit(query)',function (d) {
            console.log('筛选==', d.field)
            if (d.field.goods_code && d.field.goods_code.trim() !== '') {
                if (!d.field.code_type) {
                    layer.msg('请选择货品类型！', {icon: 2});
                    return false;
                }
            }
            getList('#list', d.field)
            return false
        })

        form.on('submit(export)',function (d) {
            console.log('导出==', d.field)
            var index = layer.load(1, {
                shade: [0.1,'#fff'] //0.1透明度的白色背景
            });
            $.ajax({
                type: 'post',
                url: "/exceptionGoods/export",
                data: d.field,
                success: function (res) {
                    if (res.code == 200) {
                        if (res.data && res.data.url) {
                            window.location.href = res.data.url;
                            layer.close(index);
                        } else {
                            layer.msg('导出文件地址不存在', {icon: 2, time: 1500});
                        }
                    } else {
                        layer.msg(res.msg, {icon: 2, time: 1500});
                        layer.close(index);
                    }
                },
                error: function() {
                    layer.close(index);
                    layer.msg('导出请求失败', {icon: 2, time: 1500});
                }
            });
            return false
        })

        // 监听table编辑事件
        table.on("tool(tableBar)", function (obj) {
            var data = obj.data; //获得当前行数据
            var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）
            console.log('当前行数据==',JSON.stringify(obj.data))
            console.log('当前行事件==',layEvent)
            if(layEvent == 'detail') {
                console.log('详情')
                iframeTools.editTab('查看',"/exceptionGoods/detail?id="+data.id)
                return false
            }
            if(layEvent == 'buyer') {
                console.log('买手')
                iframeTools.editTab('买手处理',"/exceptionGoods/buyer?id="+data.id)
                return false
            }
            if(layEvent == 'shop') {
                console.log('仓店')
                iframeTools.editTab('仓店处理',"/exceptionGoods/shop?id="+data.id)
                return false
            }
            if(layEvent == 'invalid') { //查看
                console.log('仓店')
                layer.confirm('确定要作废吗？', function(index){
                    var index = layer.load(1, {
                        shade: [0.1,'#fff'] //0.1透明度的白色背景
                    });
                    $.ajax({
                        type: 'post',
                        url: "/exceptionGoods/invalid",
                        data: {id:data.id},
                        success: function (res) {
                            layer.close(index)
                            if (res.code==200) {
                                layer.msg(res.msg,{icon:1,time:1500})
                                getList('#list', state.formData)
                           } else {
                                layer.msg(res.msg,{icon:2,time:1500})
                           }
                        }
                    })
                })
                return false
            }
            return false
        })

        /********************************************自定义方法***************************************************/
        function getList(position, where={}) {
            console.log(where);
            var index = layer.load(1, {
                shade: [0.1,'#fff'] //0.1透明度的白色背景
            });
            table.render({
                elem: position
                , url: '/exceptionGoods/list'
                ,method: "post"
                ,where: where
                ,parseData: function(res){ //res 即为原始返回的数据
                    layer.close(index)
                    if (res.code !== 200) {
                        alert(res.msg)
                        return
                    }
                    state.export_page_data = res.data.export_page_data
                    renderDropDown('#export_page_data',res.data.export_page_data)
                    return {
                        "code": res.code, //解析接口状态
                        "msg": res.msg, //解析提示文本
                        "count": res.data.total, //解析数据长度
                        "data": res.data.data //解析数据列表
                    };
                }
                , skin: 'line'
                , cellMinWidth: 80 //全局定义常规单元格的最小宽度
                , cols: [[
                    {field: 'id', title: 'ID',width: 60}
                    , {width: 150,field: 'ware_name', title: '仓店'}
                    , {width: 150,field: 'exception_type_name', title: '异常类型'}
                    , {width: 100,field: 'status_name', title: '状态'}
                    , {width: 100,field: 'cate1_name', title: '一级类目'}
                    , {width: 100,field: 'brand_name', title: '品牌'}
                    , {width: 100,field: 'spu_no', title: '货号'}
                    , {width: 100,field: 'goods_code', title: '条码/店内码'}
                    , {width: 70,field: 'admin_name', title: '创建人'}
                    , {width: 200,field: 'created_at', title: '创建时间'}
                    , {field: 'remark', title: '备注'}
                    , {width: 180,fixed: 'right', title: '状态',align: 'center',templet:function (d) {
                            let str = ''
                            
                            // 作废按钮
                            if ((d.status == 3 && d.exception_type == 3) || (d.status != 3)) {
                                str += '<a class="pear-btn pear-btn-primary pear-btn-sm" lay-event="invalid">作废</a>'
                            }
                            
                            // 买手处理按钮
                            if (d.status == 1) {
                                str += '<a class="pear-btn pear-btn-primary pear-btn-sm" lay-event="buyer">买手处理</a>'
                            }
                            
                            // 仓店处理按钮
                            if (d.status == 2) {
                                str += '<a class="pear-btn pear-btn-primary pear-btn-sm" lay-event="shop">仓店处理</a>'
                            }
                            
                            // 查看按钮
                            if ([3,4,5].includes(d.status)) {
                                str += '<a class="pear-btn pear-btn-primary pear-btn-sm" lay-event="detail">查看</a>'
                            }
                            
                            return str
                    }}
                ]]
                , page: true
                , done: function (index, layero) {
                    //表体部分
                    //动态监听表体高度变化，冻结行跟着改变高度
                    $(".layui-table-body  tr").resize(function () {
                        $(".layui-table-body  tr").each(function (index, val) {
                            $($(".layui-table-fixed .layui-table-body table tr")[index]).css("cssText", "height:"+$(val).height()+"px!important");
                        });
                    });
                    //初始化高度，使得冻结行表体高度一致
                    $(".layui-table-body  tr").each(function (index, val) {
                        $($(".layui-table-fixed .layui-table-body table tr")[index]).css("cssText", "height:"+$(val).height()+"px!important");
                    });
                }
            });
        }

        var startTimeDate = laydate.render({
            elem: '#start_time'
            ,type: 'datetime'
            ,format: 'yyyy-MM-dd HH:mm:ss'
            // ,done:function(value, date){
            // 	console.log("start="+JSON.stringify(date))
            // 	//endTimeDate.config.min=getDateArray('max',date);//重点
            //
            // }
        });

        var endTimeDate = laydate.render({
            elem: '#end_time'
            ,type: 'datetime'
            ,format: 'yyyy-MM-dd HH:mm:ss'
            // ,done:function(value, date){
            // 	console.log("end="+JSON.stringify(date))
            // 	//startTimeDate.config.max=getDateArray('min',date);//重点
            // 	//startTimeDate.config.max=getDateArray('min',date);//重点
            // }
        });

        //时间插件 - w
        laydate.render({
            elem: '#pick-order-time'
            , type: 'datetime'
            , range: '~'
            , trigger: 'click'
            , change: function(value, date, endDate){
                console.log("value=",value)
                let timeArr = value.split(' ~ ')
                console.log("timeArr===",timeArr)
                state.start_time = timeArr[0]
                state.end_time = timeArr[1]
                console.log("state.start_time===",$.trim(state.start_time))
                console.log("state.end_time===",$.trim(state.end_time))
                console.log("date=",date)
                console.log("endDate=",endDate)
            }
            , done: function (value,date,endDate) {
                var hours = endDate.hours;
                var minutes = endDate.minutes;
                var seconds = endDate.seconds;
                if (hours == "0" && minutes == "0" && seconds == "0"){
                    $(".layui-laydate-footer [lay-type='datetime'].laydate-btns-time").click();
                    // 如果是datetime的范围选择，改变开始时间默认值
                    // $(".laydate-main-list-0 .layui-laydate-content li ol li:last-child").click();
                    // 改变结束时间默认值
                    $(".laydate-main-list-1 .layui-laydate-content li ol li:last-child").click();
                    // 如果不是范围选择，只是日期时间选择
                    //$(".laydate-main-list-0 .layui-laydate-content li ol li:last-child").click();

                    $(".layui-laydate-footer [lay-type='date'].laydate-btns-time").click();
                }
            }
        });

        function getDateArray(type,date) {//获取时间数组
            let dateStr = convertTimeObjToStr(date)
            let newDate = new Date(dateStr);
            console.log( "aaaaa：" +newDate.toLocaleDateString()+ newDate.toLocaleTimeString())

            let dateObj = {}
            console.log("dateStr==", dateStr)
            if (type === 'min') {
                // 此时date是max时间值
                let month = newDate.getMonth()
                newDate.setMonth(month + 3)
                console.log( "三个月后时间：" +newDate.toLocaleDateString()+ newDate.toLocaleTimeString())
            }

            if (type === 'max') {
                // 此时date是max时间值
                let month = newDate.getMonth()
                newDate.setMonth(month - 3)
                console.log( "三个月前时间：" +newDate.toLocaleDateString()+ newDate.toLocaleTimeString())
            }

            dateObj.year = newDate.getFullYear()
            dateObj.month = newDate.getMonth()
            dateObj.date = newDate.getDay()
            dateObj.hours = newDate.getHours()
            dateObj.minutes = newDate.getMinutes()
            dateObj.seconds = newDate.getSeconds()

            return dateObj
        }

        function convertTimeObjToStr(date) {
            let year = date.year
            let month = date.month.toString().length === 1 ? "0"+date.month : date.month
            let day = date.date.toString().length === 1 ? "0"+date.date : date.date
            let hour = date.hours.toString().length === 1 ? "0"+date.hours : date.hours
            let minute = date.minutes.toString().length === 1 ? "0"+date.minutes : date.minutes
            let second = date.seconds.toString().length === 1 ? "0"+date.seconds : date.seconds

            return year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second
        }

        var status =  renderXmSelect('#status','status','状态',[
            {name: '待买手处理', value: '1',selected: true},
            {name: '待仓店处理', value: '2',},
            {name: '已完成', value: '3',},
            {name: '作废', value: '4',},
            {name: '微残驳回', value: '5',},
        ])

        var status =  renderXmSelect('#exception_type','exception_type','异常类型',[
            {name: '疑难货品', value: '1',},
            {name: '实物系统条码不符', value: '2'},
            {name: '退供轻微瑕疵', value: '3',},
            {name: '新品采购差异', value: '4',},
        ])
        var status =  renderXmSelect('#destination','destination','货品去向',[
            {name: '线下售卖', value: '1',},
            {name: '冲底盘亏', value: '2',},
            {name: '报损', value: '3',},
        ])

        // 获取供应商数据
        $.ajax({
            type: 'get',
            url: "/exceptionGoods/getSuppliers",
            async: false,
            success: function (res) {
                if (res.code==200) {
                    comm.suppliers = res.data
                } else {
                    layer.msg(res.msg,{icon:2,time:1500})
                }
            }
        })

        var instBrandId =  renderXmSelect('#brand_id','brand_id','品牌', comm.brands)
        var instWId =  renderXmSelect('#w_id','w_id','仓库', comm.warehouses)
        var adminList =  renderXmSelect('#admin_id','admin_id','提交人', comm.adminList)
        var buyerAdminList =  renderXmSelect('#buyer_admin_id','buyer_admin_id','跟进买手', comm.adminList)
        var instSup =  renderXmSelect('#sup_id','sup_id','供应商', comm.suppliers)

        // 渲染下拉框
        function renderXmSelect(id,name,tips,data) {
            var inst = xmSelect.render({
                el: id,
                autoRow: true,
                filterable: true,
                direction: "down",
                name: name,
                height: '200px',
                tips: tips, // 类似于placeholder
                model: {
                    label: {
                        // type: 'block',
                        // block: {
                        // 	//最大显示数量, 0:不限制
                        // 	showCount: 3,
                        // 	//是否显示删除图标
                        // 	showIcon: true,
                        // },


                        type: 'my_text',
                        my_text: {
                            template(data, sels){
                                console.log('选择的item===',sels)
                                // return "已选中 " + sels.length + " 项, 共 " + data.length + " 项"
                                let selectItems = []
                                // 取前三个的name
                                for (let i = 0; i < sels.length; i++) {
                                    if (i <= 2) {
                                        selectItems.push(sels[i].name)
                                    } else {
                                        break
                                    }
                                }
                                let itemsStr = selectItems.join()
                                // 等于三个，追加拼接省略号显示
                                if (selectItems.length === 3) {
                                    itemsStr += "... "
                                }
                                console.log('itemsStr===',itemsStr)
                                return itemsStr + " 已选中<span style='font-weight: bolder'> " + sels.length + "</span> 项"
                            }
                        },

                    }
                },
                tree: {
                    //是否显示树状结构
                    show: true,
                    //是否展示三角图标
                    showFolderIcon: true,
                    //是否显示虚线
                    showLine: false,
                    //间距
                    indent: 20,
                    //默认展开节点的数组, 为 true 时, 展开所有节点
                    expandedKeys: [],
                    //是否严格遵守父子模式
                    strict: true,
                    //是否开启极简模式
                    simple: false,
                    //点击节点是否展开
                    clickExpand: true,
                    //点击节点是否选中
                    clickCheck: true,
                },
                toolbar: {
                    show: true,
                    list: ['ALL', 'REVERSE', 'CLEAR']
                },

                data: data,
            })

            return inst
        }

        // xselect清空
        $('#reset').on('click',function () {
            instIsPerfect && instIsPerfect.setValue([])
            instBrandId && instBrandId.setValue([])
            instSup && instSup.setValue([])
            instWId && instWId.setValue([])
        })

        function renderDropDown(idDom,data) {
            console.log('data======',data)
            dropdown.suite(idDom, {
                showBy: "click",
                arrow: true,
                onItemClick: function (event, menu) {
                    console.log('event======',event)
                    console.log('menu======',menu)
                    let formData = state.formData
                    Object.assign(formData,{page:menu.page,limit:menu.limit})
                    console.log('formData======',formData)
                    exportData(formData)
                },
                menus: data
            });
        }

    })
</script>

</body>

</html>
