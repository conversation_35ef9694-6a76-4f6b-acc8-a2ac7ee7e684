<?php
declare(strict_types=1);

namespace App\Controller;

use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\LogisticsServiceInterface;
use App\JsonRpc\MessageLogServiceInterface;
use App\JsonRpc\OrderServiceInterface;
use App\JsonRpc\OrderServiceServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use App\JsonRpc\OrderLogisticsServiceInterface;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;

/**
 * @Controller()
 */
class OrderServiceController extends AbstractController
{
    /**
     * @Inject()
     * @var ValidatorFactoryInterface
     */
    private $validator;
    /**
     * @Inject()
     * @var AdminServiceInterface
     */
    private $AdminService;
    /**
     * @Inject()
     * @var OrderServiceServiceInterface
     */
    private $OrderServiceService;
    /**
     * @Inject()
     * @var WarehouseServiceInterface
     */
    private $WarehouseService;
    /**
     * @Inject()
     * @var LogisticsServiceInterface
     */
    private $LogisticsService;
    /**
     * @Inject()
     * @var OrderServiceInterface
     */
    private $OrderService;
    /**
     * @Inject()
     * @var MessageLogServiceInterface
     */
    private $MessageLogService;

    /**
     * @Inject()
     * @var OrderLogisticsServiceInterface
     */
    private $OrderLogisticsService;

    private $service_status = [
        1 => [4],#未处理
        2 => [5,6,7,8],#已处理
    ];

    /**
     * 退货处理 - 列表
     * @RequestMapping(path="/orderService/dealList", methods="get,post")
     */
    public function dealList()
    {
        if ($this->isAjax()) {
            $userWIds = $this->AdminService->organizeWareHouseData($this->getUserId());

            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();

            $order_where = [];
            $order_where['is_pay'] = 1;
            $order_where['w_ids'] = $userWIds;
            //订单号
            if (isset($params['order_serial_no']) && !empty($params['order_serial_no'])){
                $order_where['order_serial_no'] = $params['order_serial_no'];
            }
            //手机号
            if (isset($params['phone']) && !empty($params['phone']) ){
                $order_where['receiving_phone'] = $params['phone'];
            }
            //店内码
            if (isset($params['unique_code']) && !empty($params['unique_code'])){
                $order_where['unique_code'] = $params['unique_code'];
            }
            //售后状态
            if ( isset($params['service_status']) && !empty($params['service_status'])){
                $order_where['join_order_service'] = true;
                $order_where['order_service_status_list'] = $this->service_status[$params['service_status']];
            }
            //退回单号
            if ( isset($params['back_logistics_no']) && !empty($params['back_logistics_no'])){
                $order_where['join_order_service'] = true;
                $order_where['logistics_serial_no'] = $params['back_logistics_no'];
            }
            //发货单号
            if (isset($params['send_logistics_no']) && !empty($params['send_logistics_no'])){
                $order_logistics_list = $this->OrderLogisticsService->getLogisticsOrder(['logistics_serial_no' => $params['send_logistics_no']]);
                if (empty($order_logistics_list)){
                    return $this->returnApi(ResponseCode::SUCCESS, '操作成功', [], ['count' => 0, 'limit' => $pageLimit]);
                }else{
                    $order_where['branch_serial_nos'] = array_unique(array_column($order_logistics_list, 'branch_serial_no'));
                }
            }
            //收货人
            if (isset($params['receiving_name']) && !empty($params['receiving_name'])){
                $order_where['receiving_name'] = $params['receiving_name'];
            }

            //按照子订单号分组
            $order_where['group_branch_serial_no'] = true;
            $order_where['get_order_detail'] = true;
            $order_where['order_by_branch_id_desc'] = true;

            $list = $this->OrderServiceService->getServiceList($order_where, (int)$pageLimit, (int)$page);
//            $list = $this->OrderService->getList($order_where, (int)$pageLimit, (int)$page);
            $order_data = [];
            if (!empty($list['list'])){
                //获取订单的售后信息，处理售后状态
                $processing_where = [
                    'branch_serial_nos' => array_column($list['list'], 'branch_serial_no')
                ];
                $order_service_process_data = $this->OrderServiceService->orderServiceProcessingData($processing_where);
                $order_service_process_map = [];
                if (!empty($order_service_process_data)){
                    $order_service_process_map = array_combine(array_column($order_service_process_data, 'branch_serial_no'), array_values($order_service_process_data));
                }

                foreach ($list['list'] as $o_info) {
                    //检测处理结果
                    $processs_status = '——';
                    if (isset($order_service_process_map[$o_info['branch_serial_no']])){
                        $order_process_info = $order_service_process_map[$o_info['branch_serial_no']];
                        if ($order_process_info['service_nums'] == $order_process_info['processed_nums']){
                            $processs_status = '已处理';
                        }elseif ($order_process_info['service_nums'] == $order_process_info['wait_nums']){
                            $processs_status = '未处理';
                        }else{
                            $processs_status = '处理中';
                        }
                    }

                    $order_data[] = [
                        'order_id' => $o_info['ob_id'],
                        'order_code' => $o_info['branch_serial_no'],
                        'w_name' => $o_info['w_name'],
                        'sku_nums' => $o_info['sku_nums'],
                        'processs_status' => $processs_status
                    ];
                }
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $order_data, ['count' => $list['total'], 'limit' => $pageLimit]);
        }

        return $this->show('orderService/dealList');
    }

    /**
     * 退货处理 - 详情
     * @RequestMapping(path="/orderService/dealDetail", methods="get,post")
     */
    public function dealDetail()
    {
        $userWIds = $this->AdminService->organizeWareHouseData($this->getUserId());

        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        // 退回类型
        $return_type = PublicCode::order_service_sign_return_type;
        // 快递公司
        $logistics = $this->LogisticsService->getLogisticsS([], ['id', 'name']);
        $logistics = $logistics ? array_column($logistics, 'name', 'id') : [];
        // 货品存放
        $address_type = PublicCode::order_service_quality_address_type;
        // 质检结果
        $quality_res = PublicCode::order_service_quality_res;

        // 数据 明细分页展示
        $params = $this->request->all();
        if ($this->isAjax()) {
            $detailPage = $params['page'] ?? 1;
//            $detailLimit = $params['limit'] ?? $this->pageLimit();

            $search['w_ids'] = $userWIds; // 权限-仓库
            $search['order_no'] = $params['order_no'];
            if ($params['content']) {
                $search['order_detail']['detail_content'] = $params['content'];
            }

            $list = $this->OrderServiceService->wmsOrderList(1, 1, $search, (int)$detailPage, 100);//默认取100条，小程序下单一单最多99件

            //检测处理状态
            $processing_where = [
                'branch_serial_nos' => [$params['order_no']]
            ];
            $order_service_process_data = $this->OrderServiceService->orderServiceProcessingData($processing_where);
            //检测处理结果
            $processs_status = '——';
            $process_color = '#6f6b6b';
            if (!empty($order_service_process_data) && !empty($order_service_process_data[0]['branch_serial_no'])){
                if ($order_service_process_data[0]['service_nums'] == $order_service_process_data[0]['processed_nums']){
                    $processs_status = '已处理';
                    $process_color = '#008400';
                }elseif ($order_service_process_data[0]['service_nums'] == $order_service_process_data[0]['wait_nums']){
                    $processs_status = '未处理';
                    $process_color = '#f00';
                }else{
                    $processs_status = '处理中';
                    $process_color = '#0202fb';
                }
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功',  [
                'total' => $list['total'],
                'data' => $list['data'],
                'processs_status' => $processs_status,
                'process_color' => $process_color
            ]);
        }

        return $this->show('orderService/dealDetail', [
            'warehouse_list' => $warehouse,
            'return_type' => $return_type,
            'logistics_list' => $logistics,
            'address_type' => $address_type,
            'quality_res' => $quality_res,

            'order_no' => $params['order_no']
        ]);
    }

    /**
     * 退货处理 - 办理
     * @RequestMapping(path="/orderService/deal", methods="get,post")
     */
    public function deal()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $rule = [
                'order_no' => ['required', 'string'],// 订单号
                'w_id' => ['required', 'integer'],
                'return_type' => ['required', 'integer', 'between:1,2'],// 2：快递退回（用户拒收等情况）1：用户寄回是走正常售后流程，快递邮寄回仓
                'logistics_id' => ['required', 'integer'],
                'logistics_no' => ['required', 'string'],
                'order_detail' => ['required', 'array']//二维数组：[id、quality_res、quality_remark、address_type]
            ];
            $errors = $this->validator->make($params, $rule);
            if ($errors->fails()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
            }
            $params['w_id'] = (int)$params['w_id'];
            $params['return_type'] = (int)$params['return_type'];
            $params['logistics_id'] = (int)$params['logistics_id'];

            $ids = array_column($params['order_detail'], 'id');

            // 质检不合格的，不能回仓
            foreach ($params['order_detail'] as $item) {
                if ($item['quality_res'] == PublicCode::order_service_quality_res_no && $item['address_type'] == PublicCode::order_service_quality_address_status_w) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '质检不合格的货品，不允许放回仓库');
                }
            }

            // 检查订单明细是否存在
            $orderDetail = $this->OrderService->getOrderBranchDetailS(['ids' => $ids, 'branch_serial_no' => $params['order_no']], ['id', 'pick_type', 'unique_code', 'barcode', 'sku_id', 'spu_id', 'brand_id', 'category_id']);
            $hasDetailIds = $orderDetail ? array_column($orderDetail, 'id') : [];
            foreach ($ids as $id) {
                if (!in_array($id, $hasDetailIds)) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '订单明细不存在');
                }
            }
            $hasDetail = [];
            foreach ($orderDetail as $item) {
                $hasDetail[$item['id']] = [
                    'pick_type' => $item['pick_type'],
                    'unique_code' => $item['unique_code'],
                    'barcode' => $item['barcode'],
                    'sku_id' => $item['sku_id'],
                    'spu_id' => $item['spu_id'],
                    'brand_id' => $item['brand_id'],
                    'category_id' => $item['category_id']
                ];
            }

            // 订单明细有售后单的话，检查售后单状态
            $service = $this->OrderServiceService->getOrders(['order_detail_ids' => $ids, 'valid_status' => 1]);
            // 处理前的售后单数据
            $beforeService = $service ? collect($service)->groupBy(['id'])->toArray() : [];
            $hasService = $service ? array_column($service, 'status', 'order_detail_id') : [];
            foreach ($ids as $id) {
                if (isset($hasService[$id]) && $hasService[$id] != PublicCode::order_service_status_wait_return) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '售后单状态不符合，无法操作');
                }
                // 先屏蔽掉未知售后单的处理，只处理有售后单的订单
                if (!(isset($hasService[$id]))) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '请先联系客服创建售后单，否则会导致无法触发客户自动退款！');
                }
            }
            $hasServiceNo = $service ? array_column($service, 'serial_no', 'order_detail_id') : [];

            // 有签收记录的是已处理过的，则不能再次处理
            $orderSign = $this->OrderServiceService->getSignS(['order_detail_ids' => $ids], ['id', 'order_detail_id']);
            if ($orderSign) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '有处理过的记录，请刷新页面');
            }

            // 获取订单来源渠道
            $branchOrder = $this->OrderService->getOrderBranch(['branch_serial_no' => $params['order_no']], ['parent_serial_no']);
            $parentOrder = $this->OrderService->getOrder(['parent_serial_no' => $branchOrder['parent_serial_no']]);
            $source = (int)$parentOrder['source'];

            // 快递名称/仓库名称
            $logistics_name = $this->LogisticsService->getLogistics(['id' => $params['logistics_id']], ['name'])['name'] ?? '';
            $w_name = $this->WarehouseService->getWarehouse(['id' => $params['w_id']], ['name'])['name'];

            $userInfo = $this->session->get('userInfo');
            // 保存
            try {
                $list = [];
                $uqLineLog = [];

                foreach ($params['order_detail'] as $item) {
                    $item['id'] = (int)$item['id'];
                    $data = [];
                    $data['order_detail_id'] = $item['id'];

                    if (!isset($hasServiceNo[$item['id']])) {
                        // 没有售后单则新建售后单-类型：未知；有则更新为签收状态
                        $data['service_no'] = '';
                        $data['service'] = [
                            'source' => $source,
                            'reason_type' => PublicCode::service_reason_refuse,
                            'admin_id' => $userInfo['uid'],
                            'admin_name' => $userInfo['nickname']
                        ];
                    } else {
                        $data['service_no'] = $hasServiceNo[$item['id']];
                        $data['service'] = [
                            'status' => PublicCode::order_service_status_already_sign
                        ];
                    }

                    // 签收记录
                    $data['sign'] = [
                        'branch_serial_no' => $params['order_no'],
                        'order_detail_id' => $item['id'],
                        'w_id' => $params['w_id'],
                        'w_name' => $w_name,
                        'return_type' => $params['return_type'],
                        'logistics_id' => $params['logistics_id'],
                        'logistics_name' => $logistics_name,
                        'logistics_no' => $params['logistics_no'],
                        'admin_id' => $userInfo['uid'],
                        'admin_name' => $userInfo['nickname'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                    // 质检记录
                    $data['quality'] = [
                        'branch_serial_no' => $params['order_no'],
                        'order_detail_id' => $item['id'],
                        'w_id' => $params['w_id'],
                        'w_name' => $w_name,
                        'status' => $item['quality_res'],
                        'address_type' => $item['address_type'],// 1仓库 2暂存区
                        'address_status' => $item['address_type'],// 1回仓 2暂存区
                        'remark' => $item['quality_remark'],
                        'admin_id' => $userInfo['uid'],
                        'admin_name' => $userInfo['nickname'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];

                    // 放回仓库，需执行入生产区
                    $data['produce_area'] = [];
                    if ($data['quality']['address_status'] == PublicCode::order_service_quality_address_status_w) {

                        $produceAreaData = [
                            'w_id' => $params['w_id'],
                            'source_type' => PublicCode::produce_area_source_type_service,
                            'sku_id' => $hasDetail[$item['id']]['sku_id'],
                            'spu_id' => $hasDetail[$item['id']]['spu_id'],
                            'brand_id' => $hasDetail[$item['id']]['brand_id'],
                            'category_id' => $hasDetail[$item['id']]['category_id'],
                            'num' => 1,
                            'produce_num' => 0,
                            'reback_num' => 0,
                            'surplus_num' => 1,
                            'status' => PublicCode::produce_area_status_no_finish,
                            'admin_id' => $userInfo['uid'],
                            'admin_name' => $userInfo['nickname'],
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ];
                        if ($hasDetail[$item['id']]['pick_type'] == PublicCode::pick_type_barcode) {
                            $produceAreaData['sign_type'] = PublicCode::produce_area_sign_type_barcode;
                            $produceAreaData['barcode'] = $hasDetail[$item['id']]['barcode'];
                        } else {
                            $produceAreaData['sign_type'] = PublicCode::produce_area_sign_type_unique_code;
                            $produceAreaData['unique_code'] = $hasDetail[$item['id']]['unique_code'];
                            $produceAreaData['barcode'] = $hasDetail[$item['id']]['barcode'];
                        }
                        $data['produce_area'] = $produceAreaData;
                    }

                    $list[] = $data;

                    // 店内码流程日志
                    if ($hasDetail[$item['id']]['pick_type'] == PublicCode::pick_type_unique_code) {
                        $quality_res_text = PublicCode::order_service_quality_res[$item['quality_res']];
                        $uqLineLog[] = [
                            'unique_code' => $hasDetail[$item['id']]['unique_code'],
                            'operation_type' => $item['address_type'] == 1 ? PublicCode::UNIQUE_CODE_LOG_TYPE_MAP['back_order_in_produce_area'] : PublicCode::UNIQUE_CODE_LOG_TYPE_MAP['staging_area_on_shelf'], // 回仓，类型为(退货入库)；暂存区，类型为（暂存区上架）
                            'admin_id' => $userInfo['uid'],
                            'admin_name' => $userInfo['nickname'],
                            'operation_desc' => "仓库：{$w_name}；质检结果：{$quality_res_text}；质检备注：{$item['quality_remark']}",
                            'operation_time' => date('Y-m-d H:i:s'),
                            'snow_id' => newSnowId()
                        ];
                    }
                }

                $serviceNos = $this->OrderServiceService->batchDeal($list);

                // 售后存日志
                if ($serviceNos) {
                    $serviceList = $this->OrderServiceService->getOrders(['serial_nos' => $serviceNos], ['id', 'serial_no', 'order_detail_id', 'type', 'status']);

                    $quality_res = array_column($params['order_detail'], 'quality_res', 'id');
                    $quality_remark = array_column($params['order_detail'], 'quality_remark', 'id');

                    foreach ($serviceList as $item) {
                        $snow_id = generateSnowId();
                        wlog($snow_id, [
                            'snow_id' => $snow_id,
                            'op_id' => $item['id'],
                            'serial_no' => $item['serial_no'],

                            'handle_type' => '签收',
                            'handle_content' => [
                                'desc' => '我方 签收商品',
                                'logistics_name' => $logistics_name,
                                'logistics_no' => $params['logistics_no'],
                            ],
                            'handle_admin_id' => $userInfo['uid'],
                            'handle_admin_name' => $userInfo['nickname'],
                            'handle_time' => date('Y-m-d H:i:s')
                        ]);
                        serviceLog($snow_id, [
                            'operation_id' => $snow_id,
                            'serial_no' => $item['serial_no'],
                            'type' => $item['type'],
                            'operation_time' => date('Y-m-d H:i:s'),
                            'operation_user_id' => $userInfo['uid'],
                            'operation_user_name' => $userInfo['nickname'],
                            'operation_before_data' => $beforeService[$item['id']][0] ? json_encode($beforeService[$item['id']][0]) : '',
                            'operation_status' => 6, // 前端日志展示 状态6商品已签收
                            'operation_desc' => '商家 已签收商品',
                            'remark' => ''
                        ]);
                        $snow_id = generateSnowId();
                        wlog($snow_id, [
                            'snow_id' => $snow_id,
                            'op_id' => $item['id'],
                            'serial_no' => $item['serial_no'],

                            'handle_type' => '质检',
                            'handle_content' => [
                                'desc' => '我方 质检商品',
                                'quality_res' => PublicCode::order_service_quality_res[$quality_res[$item['order_detail_id']]],
                                'remark' => $quality_remark[$item['order_detail_id']],
                            ],
                            'handle_admin_id' => $userInfo['uid'],
                            'handle_admin_name' => $userInfo['nickname'],
                            'handle_time' => date('Y-m-d H:i:s')
                        ]);
                    }

                    // 店内码流程日志
                    if ($uqLineLog) {
                        foreach ($uqLineLog as $item) {
                            addUniqueCodeLog($item['snow_id'], $item);
                        }
                    }
                }

            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 退货处理记录 - 列表
     * @RequestMapping(path="/orderService/list", methods="get,post")
     */
    public function list()
    {
        $userWIds = $this->AdminService->organizeWareHouseData($this->getUserId());
        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        // 快递公司
        $logistics = $this->LogisticsService->getLogisticsS([], ['id', 'name']);
        $logistics = $logistics ? array_column($logistics, 'name', 'id') : [];
        // 质检结果
        $quality_res = PublicCode::order_service_quality_res;
        // 货品存放
        $address_type = PublicCode::order_service_quality_address_type;
        // 状态
        $address_status = PublicCode::order_service_quality_address_status;
        // 用户寄回方式
        $return_type = PublicCode::order_service_sign_return_type;

        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            $search['w_ids'] = $userWIds;
            $export = $params['export'] ?? 0;// 0列表 1导出

            $list = $this->OrderServiceService->signList($export, (int)$page, (int)$pageLimit, $search);
            if ($list['data']) {
                foreach ($list['data'] as &$item) {
                    $item['w_name'] = $warehouse[$item['w_id']];
                    $item['return_type'] = $return_type[$item['return_type']];
                    $item['quality_res'] = $quality_res[$item['quality_res']];
                    $item['address_type'] = $address_type[$item['address_type']];
                    $item['address_status'] = $address_status[$item['address_status']];
                    $item['content'] = $item['pick_type'] == PublicCode::pick_type_barcode ? $item['barcode'] : $item['unique_code'];
                }
            }

            if ($export == 1) {
                if (!$list['data']) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
                }
                $url = exportToExcel($this->exportListHeader(), $list['data'], '退货处理记录');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
        }

        return $this->show('orderService/list', [
            'warehouse_list' => $warehouse,
            'logistics_list' => $logistics,
            'quality_res' => $quality_res,
            'address_type' => $address_type,
            'address_status' => $address_status
        ]);
    }

    private function exportListHeader()
    {
        return [
            'id' => 'ID',
            'w_name' => '仓库',
            'branch_serial_no' => '订单号',
            'return_type' => '寄回方式',
            'logistics_name' => '快递公司',
            'logistics_no' => '快递单号',
            'content' => '货品',
            'quality_res' => '质检结果',
            'address_type' => '存放区',
            'address_status' => '状态',
            'created_at' => '操作时间',
        ];
    }

    /**
     * 退货处理记录 - 详情
     * @RequestMapping(path="/orderService/detail", methods="get,post")
     */
    public function detail()
    {
        $params = $this->request->all();
        if (!isset($params['id']) || !$params['id']) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '参数错误');
        }

        // 签收、质检、退回用户物流 数据
        $orderSign = $this->OrderServiceService->getSign(['id' => $params['id']]);
        if (!$orderSign) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '不存在');
        }
        $orderQuality = $this->OrderServiceService->getQuality(['order_service_no' => $orderSign['order_service_no']]);
        $orderLogistics = $this->OrderServiceService->getLogistics(['order_service_no' => $orderSign['order_service_no'], 'status' => PublicCode::order_service_logistics_status_valid]);

        // 退回类型
        $return_type = PublicCode::order_service_sign_return_type;
        // 快递公司
        $logistics = $this->LogisticsService->getLogisticsS([], ['id', 'name']);
        $logistics = $logistics ? array_column($logistics, 'name', 'id') : [];
        // 寄回用户的快递公司
        $send_logistics = $this->LogisticsService->getLogisticsConfigByWId($orderSign['w_id']);
        $send_logistics = $send_logistics ? array_column($send_logistics, 'name', 'id') : [];
        // 货品存放
        $address_type = PublicCode::order_service_quality_address_type;
        // 质检结果
        $quality_res = PublicCode::order_service_quality_res;

        // 订单数据
        $userWIds = $this->AdminService->organizeWareHouseData($this->getUserId());
        $search['w_ids'] = $userWIds; // 权限-仓库
        $search['order_no'] = $orderSign['branch_serial_no'];
        $search['order_detail']['order_detail_id'] = $orderSign['order_detail_id'];

        $list = $this->OrderServiceService->wmsOrderList(1, 1, $search, 1, 1);

        // 操作记录
        $log = $this->MessageLogService->getLogList([
            'res_params.serial_no' => $orderSign['order_service_no']
        ]);
        $log_list = $log['data'] ? array_column($log['data'], 'res_params') : [];
        if ($log_list) {
            foreach ($log_list as &$item) {
                $item['handle_content'] = implode('；', json_decode($item['handle_content'], true));
            }
        }

        // 按钮是否可点击
        $canEditQuality = 0;
        if ($orderQuality['address_status'] == 2) {
            $canEditQuality = 1;
        }
        $canReturnToUser = 0;
        if ($orderLogistics && !$orderLogistics['logistics_no']) {
            $canReturnToUser = 1;
        }

        return $this->show('orderService/detail', [
            'return_type' => $return_type,
            'logistics_list' => $logistics,
            'address_type' => $address_type,
            'quality_res' => $quality_res,
            'order_data' => $list['data'],
            'order_sign' => $orderSign,
            'order_quality' => $orderQuality,
            'order_logistics' => $orderLogistics,
            'send_logistics' => $send_logistics,
            'log' => $log_list,
            'canEditQuality' => $canEditQuality,
            'canReturnToUser' => $canReturnToUser
        ]);
    }

    /**
     * 退货处理记录 - 修改质检
     * @RequestMapping(path="/orderService/editQuality", methods="get,post")
     */
    public function editQuality()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $rule = [
                'id' => ['required'],
                'quality_res' => ['required', 'integer', 'between:0,1'],
                'address_type' => ['required', 'integer', 'between:1,2'],
                'remark' => ['string']
            ];
            $errors = $this->validator->make($params, $rule);
            if ($errors->fails()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
            }

            $orderQuality = $this->OrderServiceService->getQuality(['id' => $params['id']]);
            if (!$orderQuality) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '质检记录不存在');
            }
            $orderDetail = $this->OrderService->getOrderBranchDetail(['id' => $orderQuality['order_detail_id']], ['pick_type', 'unique_code', 'barcode', 'sku_id', 'spu_id', 'brand_id', 'category_id']);
            if (!$orderDetail) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '订单明细不存在');
            }

            // 质检不合格的，不能放回仓库
            if ($params['quality_res'] == PublicCode::order_service_quality_res_no && $params['address_type'] == PublicCode::order_service_quality_address_status_w) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '质检不合格的货品，不允许放回仓库');
            }

            // 已质检通过的不能变成不通过
            if ($orderQuality['status'] == PublicCode::order_service_quality_res_yes
                && $params['quality_res'] == PublicCode::order_service_quality_res_no
            ) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '已通过不能更为不通过');
            }

            // 回仓不能再放到暂存区
            if ($orderQuality['address_type'] == PublicCode::order_service_quality_address_type_w
                && $params['address_type'] == PublicCode::order_service_quality_address_type_t) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '已回仓商品不能移到暂存区');
            }

            // 更新数据
            $upData = [
                'address_type' => $params['address_type'],
                'remark' => $params['remark']
            ];
            if ($params['quality_res'] != $orderQuality['status']) {
                $upData['status'] = $params['quality_res'];
            }

            // 存放地址：从暂存区改到仓库，则状态更为回仓
            if ($orderQuality['address_type'] == PublicCode::order_service_quality_address_type_t
                && $params['address_type'] == PublicCode::order_service_quality_address_type_w) {
                $upData['address_status'] = PublicCode::order_service_quality_address_status_w;
            }

            $userInfo = $this->session->get('userInfo');

            try {
                // 从暂存区放回仓库，需执行入生产区
                $produceAreaData = [];
                // 店内码流程日志
                $uqLineLog = [];
                if (isset($upData['address_status'])
                    && $upData['address_status'] == PublicCode::order_service_quality_address_status_w) {

                    $produceAreaData = [
                        'w_id' => $orderQuality['w_id'],
                        'source_type' => PublicCode::produce_area_source_type_service,
                        'source_order_no' => $orderQuality['order_service_no'],
                        'sku_id' => $orderDetail['sku_id'],
                        'spu_id' => $orderDetail['spu_id'],
                        'brand_id' => $orderDetail['brand_id'],
                        'category_id' => $orderDetail['category_id'],
                        'num' => 1,
                        'produce_num' => 0,
                        'reback_num' => 0,
                        'surplus_num' => 1,
                        'status' => PublicCode::produce_area_status_no_finish,
                        'admin_id' => $userInfo['uid'],
                        'admin_name' => $userInfo['nickname'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                    if ($orderDetail['pick_type'] == PublicCode::pick_type_barcode) {
                        $produceAreaData['sign_type'] = PublicCode::produce_area_sign_type_barcode;
                        $produceAreaData['barcode'] = $orderDetail['barcode'];
                    } else {
                        $produceAreaData['sign_type'] = PublicCode::produce_area_sign_type_unique_code;
                        $produceAreaData['unique_code'] = $orderDetail['unique_code'];
                        $produceAreaData['barcode'] = $orderDetail['barcode'];

                        // 店内码流程日志
                        $uqLineLog = [
                            'unique_code' => $orderDetail['unique_code'],
                            'operation_type' => PublicCode::UNIQUE_CODE_LOG_TYPE_MAP['staging_area_down_shelf'], // 类型为44（暂存区下架）
                            'admin_id' => $userInfo['uid'],
                            'admin_name' => $userInfo['nickname'],
                            'operation_desc' => "仓库：{$orderQuality['w_name']}；从暂存区下架进入生产区",
                            'operation_time' => date('Y-m-d H:i:s'),
                            'snow_id' => newSnowId()
                        ];
                    }
                }

                // 更新质检记录
                $res = $this->OrderServiceService->updateQuality(
                    ['id' => $params['id']],
                    $upData,
                    $produceAreaData,
                    [
                        'admin_id' => $userInfo['uid'],
                        'admin_name' => $userInfo['nickname'],
                    ]
                );

                // 质检不通过改成通过，记录日志
                if (isset($upData['status'])) {
                    $service = $this->OrderServiceService->getOrder(['serial_no' => $orderQuality['order_service_no']]);
                    $snowId = $this->request->getAttribute('snow_id');
                    wlog($snowId, [
                        'snow_id' => $snowId,
                        'op_id' => $service['id'],
                        'serial_no' => $service['serial_no'],

                        'handle_type' => '质检',
                        'handle_content' => [
                            'desc' => '我方 质检商品',
                            'quality_res' => '通过',
                            'remark' => '质检结果由不通过改成通过',
                        ],
                        'handle_admin_id' => $userInfo['uid'],
                        'handle_admin_name' => $userInfo['nickname'],
                        'handle_time' => date('Y-m-d H:i:s')
                    ]);
                }

                // 店内码流程日志
                if ($res && $uqLineLog) {
                    addUniqueCodeLog($uqLineLog['snow_id'], $uqLineLog);
                }

            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 退货处理记录 - 修改发货物流
     * @RequestMapping(path="/orderService/editLogistics", methods="get,post")
     */
    public function editLogistics()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $rule = [
                'id' => ['required'],
                'logistics_id' => ['required', 'integer'],
                'logistics_no' => ['required', 'string']
            ];
            $errors = $this->validator->make($params, $rule);
            if ($errors->fails()) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
            }

            $orderLogistics = $this->OrderServiceService->getLogistics(['id' => $params['id']]);
            if (!$orderLogistics) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '物流记录不存在');
            }

            try {
                // 1、更新发货物流
                $this->OrderServiceService->updateLogistics(['id' => $params['id']], [
                    'logistics_id' => $params['logistics_id'],
                    'logistics_name' => $this->LogisticsService->getLogistics(['id' => $params['logistics_id']], ['name'])['name'] ?? '',
                    'logistics_no' => $params['logistics_no']
                ]);

                // 2、更新质检状态-退回给用户
                $this->OrderServiceService->updateQuality(
                    ['order_service_no' => $orderLogistics['order_service_no']],
                    [
                        'address_status' => PublicCode::order_service_quality_address_status_u,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }
}