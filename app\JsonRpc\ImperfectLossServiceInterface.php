<?php
declare(strict_types=1);

namespace App\JsonRpc;

interface ImperfectLossServiceInterface
{

    /**
     * 报损列表
     * @param $where
     * @param int $page
     * @param int $limit
     * @return mixed
     */
    public function list($where, int $page = 1, int $limit = 30);

    /**
     * 获取报损明细
     * @param $imperfectId
     * @return mixed
     */
    public function detail($imperfectId);

    /**
     * 保存凭证
     * @param $id
     * @param $data
     * @return mixed
     */
    public function saveProof($id, $data);

    /**
     * 完成报损任务
     * @param $id
     * @return mixed
     */
    public function finish($id);

    /**
     * 作废报损任务
     * @param $id
     * @return mixed
     */
    public function toVoid($id);

    /**
     * 保存标记主单信息
     * @param $id
     * @param $data
     * @return mixed
     */
    public function saveEdit($id, $data);

    /**
     * 明细列表
     * @param int $export
     * @param int $page
     * @param int $pageLimit
     * @param array $search
     * @return mixed
     */
    public function detailList(int $export, int $page, int $pageLimit, array $search);

    /**
     * 报损
     * @param $id
     * @param $params
     * @return mixed
     */
    public function imperfectUploadSave($id, $params);

    /**
     * 提示信息
     * @param $id
     * @return mixed
     */
    public function tips($id);
}