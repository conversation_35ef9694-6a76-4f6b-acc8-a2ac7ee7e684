<?php
declare(strict_types=1);

namespace App\Controller;

use App\Constants\PublicCode;
use App\Constants\ResponseCode;
use App\JsonRpc\AdminServiceInterface;
use App\JsonRpc\ImperfectLossServiceInterface;
use App\JsonRpc\MessageLogServiceInterface;
use App\JsonRpc\OutStoreServiceInterface;
use App\JsonRpc\SupplierServiceInterface;
use App\JsonRpc\WarehouseServiceInterface;
use App\Library\Facades\OutStoreService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Request;
use Hyperf\Validation\Contract\ValidatorFactoryInterface;

/**
 * 报损处理
 * @Controller()
 */
class ImperfectLossController extends AbstractController
{
    /**
     * @Inject()
     * @var ValidatorFactoryInterface
     */
    private $validator;
    /**
     * @Inject()
     * @var SupplierServiceInterface
     */
    private $SupplierService;

    /**
     * @Inject()
     * @var AdminServiceInterface
     */
    private $AdminService;

    /**
     * @Inject()
     * @var WarehouseServiceInterface
     */
    private $WarehouseService;

    /**
     * @Inject()
     * @var ImperfectLossServiceInterface
     */
    private $ImperfectLossService;

    /**
     * @Inject()
     * @var OutStoreServiceInterface
     */
    private $OutStoreService;

    /**
     * @Inject()
     * @var MessageLogServiceInterface
     */
    private $MessageLogService;

    /**
     * 报损任务列表
     * @RequestMapping(path="/imperfectLoss/list", methods="get,post")
     */
    public function list()
    {
        $userWIds = $this->AdminService->organizeWareHouseData($this->getUserId());

        // 仓库列表
        $warehouse = $this->WarehouseService->getWarehouses(['ids' => $userWIds], ['id', 'name']);
        $warehouse = $warehouse ? array_column($warehouse, 'name', 'id') : [];
        // 状态
        $orderStatus = PublicCode::imperfect_loss_status_map;

        // 查找类型
        $selectType = [
            1 => '店内码查找',
            2 => '货号查找',
            3 => '条形码查找',
            4 => 'SPU查找',
            5 => 'SKU查找'
        ];

        if ($this->isAjax()) {
            $params = $this->request->all();
            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            $search = $params['search'] ?? [];
            $search['w_ids'] = $userWIds; // 权限-仓库
            $search['export'] = $params['export'] ?? 0;// 0列表 1导出
            $search['exportDetail'] = $params['exportDetail'] ?? 0;// 0列表 1导出
            if (!empty($search['date'])) {
                $date = explode(" - ", $search['date']);
                $search['start_time'] = $date[0];
                $search['end_time'] = $date[1];
            }

            logger()->info('wms报损', [$search, $date, $params]);
            $list = $this->ImperfectLossService->list($search, (int)$page, (int)$pageLimit);
            logger()->info('wms报损列表', [$list]);
            if ($list['data']) {
                //查询出库数量
                foreach ($list['data'] as &$item) {
                    $item['w_name'] = $warehouse[$item['w_id']];
                    $item['status_text'] = $orderStatus[$item['status']];
                }
            }

            if ($search['export'] == 1) {
                if (!$list['data']) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
                }

                $url = exportToExcel(config('file_header.imperfect_export'), $list['data'], '报损任务单');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }

            if ($search['exportDetail'] == 1) {
                if (!$list['data']) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
                }

                $url = exportToExcel(config('file_header.imperfect_detail_export'), $list['data'], '报损任务单明细');

                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
        }

        return $this->show('imperfectLoss/list', [
            'warehouse_list' => $warehouse,
            'order_status' => $orderStatus,
            'select_type' => $selectType
        ]);
    }


    /**
     * 报损视频上传
     * @RequestMapping(path="/imperfectLoss/upload/{im_id:\d+}", methods="get,post")
     */
    public function upload($im_id = 0)
    {
        $info = $this->ImperfectLossService->detail($im_id);
        if (!$info) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '单据不存在');
        }

        return $this->show('imperfectLoss/upload', [
            'info' => $info,
        ]);
    }

    /**
     * 报损视频上传
     * @RequestMapping(path="/imperfectLoss/saveUpload", methods="get,post")
     */
    public function saveUpload()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();
            $info = $this->ImperfectLossService->detail($params['id']);
            if (!$info) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '单据不存在');
            }
        }
    }


    /**
     * 报损任务列表
     * @RequestMapping(path="/imperfectLoss/detail/{im_id:\d+}", methods="get,post")
     */
    public function detail($im_id = 0)
    {
        $info = $this->ImperfectLossService->detail($im_id);
        if (!$info) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '单据不存在');
        }

        //处理状态
        $warehouse = $this->WarehouseService->getWarehouseOne($info['w_id']);
        $info['w_name'] = $warehouse['name'];
        $info['status_text'] = PublicCode::imperfect_loss_status_map[$info['status']] ?? '未知';

        //视频信息
        $videos = [];
        if (!empty($info['video'])) {
            foreach ($info['video'] as $item) {
                $itemVideo = json_decode($item['info'], true);
                foreach ($itemVideo as $value) {
                    $videos[] = [
                        'name' => $value[0],
                        'oss_url' => 'https://x-bigoffs.oss-cn-beijing.aliyuncs.com/' . $value[1],
                    ];
                }
            }
        }

        // 出库任务 决定货品信息表头不同
        $outStoreInfo = $this->OutStoreService->getOutStore([
            'order_no' => $info['serial_no'],
            'status_list' => PublicCode::out_store_status_valid
        ]);
        $detail_table_type = 1;
        if ($outStoreInfo) {
            $detail_table_type = 2;
        }

        //日志查询
        $log = $this->MessageLogService->getLogList(['system' => 'wms', 'model_name' => 'ImperfectLoss', 'op_id' => $info['id']]);
        $log_list = $log['data'] ? array_column($log['data'], 'res_params') : [];

        return $this->show('imperfectLoss/detail', [
            'info' => $info,
            'detail_table_type' => $detail_table_type,
            'videos' => $videos,
            'log' => $log_list,
        ]);
    }

    /**
     * 上传信息查看
     * @RequestMapping(path="/imperfectLoss/uploadSan/{im_id:\d+}", methods="get,post")
     */
    public function uploadSan($im_id)
    {
        $info = $this->ImperfectLossService->detail($im_id);
        if (!$info) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '单据不存在');
        }

        //视频信息
        $videos = [];
        if (!empty($info['video'])) {
            foreach ($info['video'] as $item) {
                $itemVideo = json_decode($item['info'], true);
                foreach ($itemVideo as $value) {
                    $videos[] = [
                        'name' => $value[0],
                        'oss_url' => 'https://x-bigoffs.oss-cn-beijing.aliyuncs.com/' . $value[1],
                    ];
                }
            }
        }

        return $this->show('imperfectLoss/uploadSan', [
            'info' => $info,
            'videos' => $videos,
        ]);
    }

    /**
     * 作废任务
     * @RequestMapping(path="/imperfectLoss/outStoreTask/{im_id:\d+}", methods="get,post")
     */
    public function outStoreTask($im_id)
    {
        if ($this->isAjax()) {
            $imperfect = $this->ImperfectLossService->detail($im_id);
            if (!$imperfect) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '单据不存在');
            }

            if (!in_array($imperfect['status'], [PublicCode::imperfect_loss_status_wait, PublicCode::imperfect_loss_status_out_wait])) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '状态不符合，无法操作');
            }
            // 出库任务
            $outStoreInfo = $this->OutStoreService->getOutStore([
                'order_no' => $imperfect['serial_no'],
                'status_list' => PublicCode::out_store_status_valid
            ]);
            if ($outStoreInfo) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '已有生效中的出库任务');
            }

            $userInfo = $this->session->get('userInfo');

            try {
                // 更新退返单状态为待出库
                if ($imperfect['status'] == PublicCode::imperfect_loss_status_wait) {
                    $res = $this->ImperfectLossService->saveEdit($imperfect['id'], ['status' => PublicCode::imperfect_loss_status_out_wait]);
                    if (!$res) {
                        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '更新状态失败');
                    }
                }

                $params = [
                    'serial_no' => $imperfect['serial_no'],
                    'admin_id' => $userInfo['uid'],
                    'type' => 31,
                    'admin_name' => $userInfo['nickname']
                ];
                $res = OutStoreService::add($params);
                if (!$res) {
                    $this->returnApi(ResponseCode::VALIDATE_ERROR, '生成出库任务失败');
                }

                $logData = [
                    'snow_id' => $this->request->getAttribute('snow_id'),
                    'op_id' => $imperfect['id'],
                    'status' => PublicCode::back_order_status_wait_out,
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                    'handle_time' => date('Y-m-d H:i:s'),
                    'handle_type' => '接单',
                    'remark' => "接单了，待出库装箱发货"
                ];
                wlog((string)$logData['snow_id'], $logData);

            } catch (\Exception $e) {
                return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
        }

        return $this->returnApi(ResponseCode::VALIDATE_ERROR, '非法请求');
    }

    /**
     * 作废任务
     * @RequestMapping(path="/imperfectLoss/toVoid/{im_id:\d+}", methods="get,post")
     */
    public function toVoid($im_id = 0)
    {
        $info = $this->ImperfectLossService->detail($im_id);
        if (!$info) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '单据不存在');
        }

        if ($info['status'] == -1) {
            //return $this->returnApi(ResponseCode::VALIDATE_ERROR, '改单据已作废');
        }

        if ($info['status'] > PublicCode::imperfect_loss_status_out_wait) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '单据状态不允许作废');
        }
        $userInfo = $this->session->get('userInfo');

        $data = [
            'status' => PublicCode::imperfect_loss_status_void,
            'admin_id' => $userInfo['uid'],
            'admin_name' => $userInfo['nickname'],
        ];

        $res = $this->ImperfectLossService->saveEdit($im_id, $data);
        if (!empty($res)) {
            $logData = [
                'snow_id' => $this->request->getAttribute('snow_id'),
                'op_id' => $info['id'],
                'status' => PublicCode::imperfect_loss_status_void,
                'admin_id' => $userInfo['uid'],
                'admin_name' => $userInfo['nickname'],
                'handle_time' => date('Y-m-d H:i:s'),
                'handle_type' => '作废',
                'remark' => "作废报损单"
            ];
            wlog((string)$logData['snow_id'], $logData);
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
    }

    /**
     * 列表
     * @RequestMapping(path="/imperfectLoss/detailList", methods="get,post")
     */
    public function detailList()
    {
        if ($this->isAjax()) {
            $params = $this->request->all();

            $imperfect = $this->ImperfectLossService->detail($params['id']);
            if (!$imperfect) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '单据不存在');
            }

            $page = $params['page'] ?? 1;
            $pageLimit = $params['limit'] ?? $this->pageLimit();
            if (isset($params['search']['content'])) $params['search']['content'] = array_filter($params['search']['content']);
            $search = $params['search'] ?? [];
            $export = (int)$params['export'] ?? 0;// 0列表 1导出

            // 查-出库任务
            $outStoreInfo = $this->OutStoreService->getOutStore([
                'order_no' => $imperfect['serial_no'],
                'status_list' => PublicCode::out_store_status_valid
            ]);
            logger()->info("参数", [$search]);
            if (!$outStoreInfo) { // 无匹配店内码/条码数据
                $table_type = 1;

                $search['imperfect_loss_id'] = $imperfect['id'];
                $list = $this->ImperfectLossService->detailList($export, (int)$page, (int)$pageLimit, $search);
            } else { // 有匹配店内码/条码数据
                $table_type = 2;

                $search['out_store_id'] = $outStoreInfo['id'];
                if ($export == 1) {
                    $list = $this->OutStoreService->detailGroupList($search, 0);
                } else {
                    $list = $this->OutStoreService->detailGroupList($search, (int)$page, (int)$pageLimit);
                }
                if ($list['data']) {
                    foreach ($list['data'] as &$item) {
                        $item['sign_type'] = $item['unique_code'] ? '店内码' : '条码';
                        $item['content'] = !empty($item['unique_code']) ? $item['unique_code'] : $item['barcode'];
                        $item['finish_num'] = ($item['out_num'] > 0) ? $item['out_num'] : 0;
                    }
                }
            }

            if ($export == 1) {
                if (!$list['data']) {
                    return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
                }
                $url = exportToExcel($this->exportDetailHeader($table_type), $list['data'], '报损任务单明细');
                return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
            }

            return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
        }
    }

    /**
     * 明细列表
     * @RequestMapping(path="/imperfectLoss/details", methods="get,post")
     */
    public function details()
    {
        $params = $this->request->all();

        $imperfect = $this->ImperfectLossService->detail($params['id']);
        if (!$imperfect) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '单据不存在');
        }

        $page = $params['page'] ?? 1;
        $pageLimit = $params['limit'] ?? $this->pageLimit();
        if (isset($params['search']['content'])) $params['search']['content'] = array_filter($params['search']['content']);
        $search = $params['search'] ?? [];
        $export = (int)$params['export'] ?? 0;// 0列表 1导出
        $search['imperfect_loss_id'] = $imperfect['id'];
        $search['out_num'] = true;
        $list = $this->ImperfectLossService->detailList($export, (int)$page, (int)$pageLimit, $search);
        if ($export == 1) {
            if (!$list['data']) {
                return $this->returnApi(ResponseCode::VALIDATE_ERROR, '无数据可导出');
            }
            $url = exportToExcel($this->exportDetailHeader(3), $list['data'], '报损任务单明细');
            return $this->returnApi(ResponseCode::SUCCESS, '导出成功', ['url' => $url]);
        }

        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $list['data'], ['count' => $list['total'], 'limit' => $pageLimit]);
    }

    /**
     * 提示信息
     * @RequestMapping(path="/imperfectLoss/tips", methods="get,post")
     */
    public function tips()
    {
        $params = $this->request->all();

        $tips = $this->ImperfectLossService->tips($params['id']);

        return $this->returnApi(ResponseCode::SUCCESS, '操作成功', $tips);
    }

    /**
     * 完成报损
     * @RequestMapping(path="/imperfectLoss/finishImperfect", methods="post")
     */
    public function finishImperfect()
    {
        $params = $this->request->all();
        $rule = [
            'id' => ['required']
        ];
        $errors = $this->validator->make($params, $rule);
        if ($errors->fails()) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, $errors->errors()->first());
        }

        $order = $this->ImperfectLossService->detail($params['id']);
        if (!$order) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '单据不存在');
        }

        if ($order['status'] != PublicCode::imperfect_loss_status_wait_proof) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '单据状态不允许完成报损!');
        }

        $userInfo = $this->session->get('userInfo');

        try {
            $result = $this->ImperfectLossService->finish($order['id']);
            if ($result) {
                $logData = [
                    'snow_id' => $this->request->getAttribute('snow_id'),
                    'op_id' => $params['id'],
                    'status' => PublicCode::imperfect_loss_status_finish,
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                    'handle_time' => date('Y-m-d H:i:s'),
                    'handle_type' => '完成报损',
                    'remark' => "完成报损任务单"
                ];
                wlog((string)$logData['snow_id'], $logData);
            }
        } catch (\Exception $e) {
            return $this->returnApi(ResponseCode::SERVICE_ERROR, $e->getMessage());
        }
        return $this->returnApi(ResponseCode::SUCCESS, '操作成功');
    }

    /**
     * oss鉴权信息
     * @RequestMapping(path="/imperfectLoss/imperfectOssAccess", methods="post")
     */
    public function imperfectOssAccess()
    {
        $ossConfig = [
            'region' => 'oss-cn-beijing',
            'AccessKeyId' => env('OSS_ACCESS_ID'),
            'AccessKeySecret' => env('OSS_ACCESS_SECRET'),
            'bucket' => env('OSS_BUCKET'),
            'fileName' => "media/" . date("Y-m-d") . "/" . uuid(),
        ];
        return $this->returnApi(ResponseCode::SUCCESS, '获取成功', $ossConfig);
    }

    /**
     * 保存上传视频信息
     * @RequestMapping(path="/imperfectLoss/imperfectUploadSave/{im_id:\d+}", methods="get,post")
     */
    public function imperfectUploadSave($im_id = 0)
    {
        $params = $this->request->all();
        $imperfect = $this->ImperfectLossService->detail($im_id);
        if (!$imperfect) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '报损单据不存在');
        }

        $names = "";
        if (!empty($params['info'])) {
            foreach ($params['info'] as $info) {
                $names .= $info[0] . "|";
            }
            $names = rtrim($names,"|");
        }

        if (empty($names)) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, '保存视频异常，请重新上传');
        }

        try {
            //处理视频信息
            $userInfo = $this->session->get('userInfo');
            $params['admin_id'] = $userInfo['uid'];
            $params['admin_name'] = $userInfo['nickname'];
            $res = $this->ImperfectLossService->imperfectUploadSave($im_id, $params);
            if (!empty($res)) {
                //记录日志
                $count = $res['unique_count'];//店内码数量
                $logData = [
                    'snow_id' => $this->request->getAttribute('snow_id'),
                    'op_id' => $im_id,
                    'status' => PublicCode::imperfect_loss_status_wait_proof,
                    'admin_id' => $userInfo['uid'],
                    'admin_name' => $userInfo['nickname'],
                    'handle_time' => date('Y-m-d H:i:s'),
                    'handle_type' => '报损视频上传',
                    'remark' => "文件名：{$names}，关联店内码数量：{$count}"
                ];
                wlog((string)$logData['snow_id'], $logData);
            }
        } catch (\Throwable  $e) {
            return $this->returnApi(ResponseCode::VALIDATE_ERROR, $e->getMessage());
        }
        return $this->returnApi(ResponseCode::SUCCESS, '保存成功', [$count]);
    }

    private function exportDetailHeader($type)
    {
        if ($type == 1) {
            return [
                'brand_name' => '品牌',
                'category_name' => '品类',
                'sku_id' => 'SKU',
                'num' => '任务数',
            ];
        } elseif ($type == 2) {
            return [
                'brand_name' => '品牌',
                'category_name' => '品类',
                'sku_id' => 'SKU',
                'sign_type' => '类型',
                'content' => '店内码/条形码',
                'num' => '任务数',
                'pack_num' => '装箱数',
                'send_num' => '发货数'
            ];
        } else {
            return [
                'brand_name' => '品牌',
                'category_name' => '品类',
                'barcode' => '条形码',
                'unique_code' => '店内码',
                'num' => '任务数',
                'type_name' => '残次类型',
                'score' => '残次分值',
                'proof_name' => '是否上传凭证'
            ];
        }
    }
}